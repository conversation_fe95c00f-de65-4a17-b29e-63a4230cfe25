import{r as g,c as N,b as o,d as n,e as l,u as f,f as d,F as C,Z as ae,g as e,h as D,v as oe,G as le,i as A,j as L,m as B,k as $,t as r,H as ne,y as ie,n as re,O as E}from"./app-3b719d4c.js";import{_ as de,b as ce,a as P}from"./AdminLayout-4519ee57.js";import{M as ue,_ as _e}from"./Modal-dc9bcec4.js";import{D as me}from"./DangerButton-0a5fa3dd.js";import{s as pe,_ as he,a as ve}from"./ArrowIcon-4162df1a.js";import{_ as T}from"./SearchableDropdownNew-2de91b02.js";import{_ as S}from"./InputLabel-3b756a6a.js";/* empty css                                                              */import"./_plugin-vue_export-helper-c27b6911.js";const ge={class:"animate-top"},fe={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},xe=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Orders")],-1),be={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},ye={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},we=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ke={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Ce={class:"flex justify-between mb-2"},Ae={class:"flex"},Se=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Oe={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ve={key:0,class:"sm:col-span-4"},Me={class:"relative mt-2"},Ne={class:"sm:col-span-4"},De={class:"relative mt-2"},Le={class:"sm:col-span-4"},$e={class:"relative mt-2"},Te={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Ue={class:"shadow rounded-lg"},Be={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ee={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Pe={class:"border-b-2"},je=["onClick"],ze={key:0},Fe={class:"px-4 py-2.5 min-w-36"},Re={class:"px-4 py-2.5 min-w-36"},Ie={class:"px-4 py-2.5 min-w-28"},qe={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ge={class:"px-4 py-2.5 min-w-36"},We={class:"px-4 py-2.5 font-semibold text-green-600 min-w-36"},He={class:"px-4 py-2.5"},Ke={key:0,class:"text-gray-700 text-sm"},Ye={key:1,class:"text-gray-400 text-sm"},Qe={class:"px-4 py-2.5 min-w-36"},Xe={class:"px-4 py-2.5 min-w-44"},Ze={key:0,class:"flex items-center space-x-2"},Je=["onUpdate:modelValue","onChange"],et=["value"],tt=["onClick"],st={key:1,class:"flex items-center space-x-2"},at=["onClick"],ot={key:0,class:"px-4 py-2.5"},lt={class:"items-center px-4 py-2.5"},nt={class:"flex items-center justify-start gap-4"},it=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),rt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),dt=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),ct=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),ut=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),_t=["onClick"],mt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),pt=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),ht=[mt,pt],vt=["onClick"],gt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),ft=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),xt=[gt,ft],bt={key:1},yt=e("tr",{class:"bg-white"},[e("td",{colspan:"10",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),wt=[yt],kt={class:"p-6"},Ct=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this order? ",-1),At={class:"mt-6 flex justify-end"},Bt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","isAdmin"],setup(h){const c=h,{form:O,search:y,sort:j,fetchData:St,sortKey:z,sortDirection:F}=pe("orders.index"),V=g(!1),U=g(null),R=[{id:"confirmed",name:"Confirmed"},{id:"under_production",name:"Under Production"},{id:"shipped",name:"Shipped"},{id:"delivered",name:"Delivered"}],I=N(()=>[{id:"",name:"All Agents"},...c.agents]),q=N(()=>[{id:"",name:"All Country"},...c.counties]),G=N(()=>[{id:"",name:"All Status"},...c.statusOptions]),W=[{field:"order_number",label:"ORDER NO",sortable:!0,visible:!0},{field:"quotation.quotation_number",label:"QUOTATION NO",sortable:!1,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!1,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"lead.county.name",label:"COUNTRY",sortable:!0,visible:!0},{field:"total_amount",label:"TOTAL AMOUNT",sortable:!0,visible:!0},{field:"tracking_number",label:"TRACKING",sortable:!1,visible:!0},{field:"expected_delivery",label:"EXP. DELIVERY",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:c.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],H=s=>{U.value=s,V.value=!0},M=()=>{V.value=!1},K=()=>{O.delete(route("orders.destroy",{order:U.value}),{onSuccess:()=>M()})},Y=s=>({confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-indigo-100 text-indigo-800",delivered:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",u=g(c.agent_id||""),_=g(c.county_id||""),m=g(c.status||""),x=g(""),v=g({}),Q=(s,a)=>{u.value=s,w(x.value,u.value,_.value,m.value)},X=(s,a)=>{_.value=s,w(x.value,u.value,_.value,m.value)},Z=(s,a)=>{m.value=s,w(x.value,u.value,_.value,m.value)},w=(s,a,t,p)=>{x.value=s;const b=a===""?null:a,k=t===""?null:t,i=p===""?null:p;O.get(route("orders.index",{search:s,agent_id:b,county_id:k,status:i}),{preserveState:!0})},J=(s,a)=>{v.value[s]=a},ee=s=>{delete v.value[s]},te=s=>{window.open(route("orders.pdf",s),"_blank")},se=(s,a)=>{E.post(route("orders.update-status",s),{status:a},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete v.value[s];const p=new URLSearchParams(window.location.search).get("page")||1;E.get(route("orders.index"),{search:x.value,agent_id:u.value===""?null:u.value,county_id:_.value===""?null:_.value,status:m.value===""?null:m.value,page:p},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})};return(s,a)=>(o(),n(C,null,[l(f(ae),{title:"Orders"}),l(de,null,{default:d(()=>[e("div",ge,[e("div",fe,[xe,e("div",be,[e("div",ye,[we,D(e("input",{type:"text","onUpdate:modelValue":a[0]||(a[0]=t=>le(y)?y.value=t:null),onInput:a[1]||(a[1]=t=>w(f(y),u.value,_.value,m.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for orders..."},null,544),[[oe,f(y)]])])])]),e("div",ke,[e("div",Ce,[e("div",Ae,[Se,l(S,{for:"filters",value:"Filters"})])]),e("div",Oe,[c.isAdmin?(o(),n("div",Ve,[l(S,{for:"agent_filter",value:"Agents"}),e("div",Me,[l(T,{options:I.value,modelValue:u.value,"onUpdate:modelValue":a[2]||(a[2]=t=>u.value=t),onOnchange:Q},null,8,["options","modelValue"])])])):A("",!0),e("div",Ne,[l(S,{for:"county_filter",value:"Country"}),e("div",De,[l(T,{options:q.value,modelValue:_.value,"onUpdate:modelValue":a[3]||(a[3]=t=>_.value=t),onOnchange:X},null,8,["options","modelValue"])])]),e("div",Le,[l(S,{for:"status_filter",value:"Status"}),e("div",$e,[l(T,{options:G.value,modelValue:m.value,"onUpdate:modelValue":a[4]||(a[4]=t=>m.value=t),onOnchange:Z},null,8,["options","modelValue"])])])])]),e("div",Te,[e("div",Ue,[e("table",Be,[e("thead",Ee,[e("tr",Pe,[(o(),n(C,null,L(W,(t,p)=>D(e("th",{key:p,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:b=>f(j)(t.field,t.sortable)},[$(r(t.label)+" ",1),t.sortable?(o(),B(ve,{key:0,isSorted:f(z)===t.field,direction:f(F)},null,8,["isSorted","direction"])):A("",!0)],8,je),[[ne,t.visible]])),64))])]),h.data.data&&h.data.data.length>0?(o(),n("tbody",ze,[(o(!0),n(C,null,L(h.data.data,t=>{var p,b,k;return o(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Fe,r(t.order_number),1),e("td",Re,r(t.quotation?t.quotation.quotation_number:"N/A"),1),e("td",Ie,r(t.lead?t.lead.lead_number:"N/A"),1),e("td",qe,r((p=t.lead)==null?void 0:p.client_name),1),e("td",Ge,r((k=(b=t.lead)==null?void 0:b.county)==null?void 0:k.name),1),e("td",We,"£"+r(parseFloat(t.total_amount).toFixed(2)),1),e("td",He,[t.tracking_number?(o(),n("span",Ke,r(t.tracking_number),1)):(o(),n("span",Ye,"-"))]),e("td",Qe,r(t.expected_delivery?new Date(t.expected_delivery).toLocaleDateString("en-GB"):"N/A"),1),e("td",Xe,[v.value[t.id]!==void 0?(o(),n("div",Ze,[D(e("select",{"onUpdate:modelValue":i=>v.value[t.id]=i,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:i=>se(t.id,v.value[t.id])},[(o(),n(C,null,L(R,i=>e("option",{key:i.id,value:i.id},r(i.name),9,et)),64))],40,Je),[[ie,v.value[t.id]]]),e("button",{onClick:i=>ee(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,tt)])):(o(),n("div",st,[e("span",{class:re(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",Y(t.status)]),onClick:i=>J(t.id,t.status),title:"Click to edit status"},r(t.status.charAt(0).toUpperCase()+t.status.slice(1).replace("_"," ")),11,at)]))]),c.isAdmin?(o(),n("td",ot,r(t.lead.creator?t.lead.creator.first_name:"N/A"),1)):A("",!0),e("td",lt,[e("div",nt,[l(ce,{align:"right",width:"48"},{trigger:d(()=>[it]),content:d(()=>[l(P,{href:s.route("orders.show",{order:t.id})},{svg:d(()=>[rt]),text:d(()=>[dt]),_:2},1032,["href"]),l(P,{href:s.route("orders.edit",{order:t.id})},{svg:d(()=>[ct]),text:d(()=>[ut]),_:2},1032,["href"]),e("button",{type:"button",onClick:i=>H(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ht,8,_t),e("button",{type:"button",onClick:i=>te(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},xt,8,vt)]),_:2},1024)])])])}),128))])):(o(),n("tbody",bt,wt))])])]),h.data.data&&h.data.data.length>0?(o(),B(he,{key:0,class:"mt-6",links:h.data.links},null,8,["links"])):A("",!0)]),l(ue,{show:V.value,onClose:M},{default:d(()=>[e("div",kt,[Ct,e("div",At,[l(_e,{onClick:M},{default:d(()=>[$("Cancel")]),_:1}),l(me,{class:"ml-3",onClick:K,disabled:f(O).processing},{default:d(()=>[$(" Delete Order ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Bt as default};
