<script setup>
import { ref, onMounted, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import Dropdown from '@/Components/Dropdown.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { Head, useForm, router } from '@inertiajs/vue3';
import { sortAndSearch } from '@/Composables/sortAndSearch';

const props = defineProps(['data', 'search', 'permissions', 'counties', 'county_id', 'agents', 'agent_id', 'statusOptions', 'status', 'isAdmin']);
const { form, search, sort, fetchData, sortKey, sortDirection } = sortAndSearch('orders.index');

const modalVisible = ref(false);
const selectedOrderId = ref(null);

// Status options for inline editing
const statusOptions = [
    { id: 'confirmed', name: 'Confirmed' },
    { id: 'under_production', name: 'Under Production' },
    { id: 'shipped', name: 'Shipped' },
    { id: 'delivered', name: 'Delivered' }
];

// Enhanced filter options with "All" option
const agentsWithAll = computed(() => {
    return [
        { id: '', name: 'All Agents' },
        ...props.agents
    ];
});

const countiesWithAll = computed(() => {
    return [
        { id: '', name: 'All Country' },
        ...props.counties
    ];
});

const statusOptionsWithAll = computed(() => {
    return [
        { id: '', name: 'All Status' },
        ...props.statusOptions
    ];
});

const columns = [
    { field: 'order_number', label: 'ORDER NO', sortable: true , visible: true},
    { field: 'quotation.quotation_number', label: 'QUOTATION NO', sortable: false, visible: true },
    { field: 'lead.lead_number', label: 'LEAD NO', sortable: false, visible: true },
    { field: 'client_name', label: 'CLIENT NAME', sortable: true , visible: true},
    { field: 'total_amount', label: 'TOTAL AMOUNT', sortable: true, visible: true },
    { field: 'tracking_number', label: 'TRACKING', sortable: false, visible: true },
    { field: 'expected_delivery', label: 'EXP. DELIVERY', sortable: true, visible: true },
    { field: 'status', label: 'STATUS', sortable: true , visible: true},
    { field: 'creator.first_name', label: 'AGENT', sortable: true , visible: props.isAdmin},
    { field: 'action', label: 'ACTION', sortable: false, visible: true }
];

const openDeleteModal = (orderId) => {
    selectedOrderId.value = orderId;
    modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteOrder = () => {
    form.delete(route('orders.destroy', { order: selectedOrderId.value }), {
        onSuccess: () => closeModal()
    });
};

const getStatusClass = (status) => {
    const classes = {
        'confirmed': 'bg-blue-100 text-blue-800',
        'under_production': 'bg-purple-100 text-purple-800',
        'shipped': 'bg-indigo-100 text-indigo-800',
        'delivered': 'bg-green-100 text-green-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

const agentId = ref(props.agent_id || '');
const countyId = ref(props.county_id || '');
const statusFilter = ref(props.status || '');
const searchValue = ref('');

// Inline editing state
const editingStatus = ref({});

const setAgent = (id, name) => {
    agentId.value = id;
    handleSearchChange(searchValue.value, agentId.value, countyId.value, statusFilter.value);
};

const setCounty = (id, name) => {
    countyId.value = id;
    handleSearchChange(searchValue.value, agentId.value, countyId.value, statusFilter.value);
};

const setStatus = (id, name) => {
    statusFilter.value = id;
    handleSearchChange(searchValue.value, agentId.value, countyId.value, statusFilter.value);
};

const handleSearchChange = (value, agentId, countyId, status) => {
    searchValue.value = value;
    // Convert empty string to null for backend
    const agentParam = agentId === '' ? null : agentId;
    const countyParam = countyId === '' ? null : countyId;
    const statusParam = status === '' ? null : status;

    form.get(route('orders.index', {
        search: value,
        agent_id: agentParam,
        county_id: countyParam,
        status: statusParam
    }), {
        preserveState: true,
    });
};

// Inline status editing functions
const startEditingStatus = (orderId, currentStatus) => {
    editingStatus.value[orderId] = currentStatus;
};

const cancelEditingStatus = (orderId) => {
    delete editingStatus.value[orderId];
};

const downloadPdf = (id) => {
    window.open(route('orders.pdf', id), '_blank');
};

const updateOrderStatus = (orderId, newStatus) => {
    router.post(route('orders.update-status', orderId), { status: newStatus }, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
            delete editingStatus.value[orderId];
            // Get current URL parameters to preserve pagination
            const urlParams = new URLSearchParams(window.location.search);
            const currentPage = urlParams.get('page') || 1;

            // Reload with current filters and page preserved
            router.get(route('orders.index'), {
                search: searchValue.value,
                agent_id: agentId.value === '' ? null : agentId.value,
                county_id: countyId.value === '' ? null : countyId.value,
                status: statusFilter.value === '' ? null : statusFilter.value,
                page: currentPage
            }, {
                preserveScroll: true,
                preserveState: true,
                only: ['data']
            });
        },
        onError: (errors) => {
            console.error("Update failed:", errors);
            alert("Failed to update status. Please try again.");
        }
    });
};
</script>

<template>
    <Head title="Orders" />
    <AdminLayout>
        <div class="animate-top">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Orders</h1>
                </div>
                <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                        </svg>
                        <input type="text" v-model="search" @input="handleSearchChange(search, agentId, countyId, statusFilter)" class="block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white" placeholder="Search for orders...">
                    </div>
                </div>
            </div>

            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex justify-between mb-2">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                        </svg>
                        <InputLabel for="filters" value="Filters" />
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-4" v-if="props.isAdmin">
                        <InputLabel for="agent_filter" value="Agents" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="agentsWithAll"
                            v-model="agentId"
                            @onchange="setAgent"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="county_filter" value="Country" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="countiesWithAll"
                            v-model="countyId"
                            @onchange="setCounty"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="status_filter" value="Status" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="statusOptionsWithAll"
                            v-model="statusFilter"
                            @onchange="setStatus"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto rounded-lg max-w-full">
                <div class="shadow rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" v-show="column.visible" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="order in data.data" :key="order.id">
                                <td class="px-4 py-2.5 min-w-36">{{ order.order_number }}</td>
                                <td class="px-4 py-2.5 min-w-36">{{ order.quotation ? order.quotation.quotation_number : 'N/A' }}</td>
                                <td class="px-4 py-2.5 min-w-28">{{ order.lead ? order.lead.lead_number : 'N/A' }}</td>
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36">{{ order.lead?.client_name }}</td>
                                <td class="px-4 py-2.5 font-semibold text-green-600 min-w-36">£{{ parseFloat(order.total_amount).toFixed(2) }}</td>
                                <td class="px-4 py-2.5">
                                    <span v-if="order.tracking_number" class="text-gray-700 text-sm">{{ order.tracking_number }}</span>
                                    <span v-else class="text-gray-400 text-sm">-</span>
                                </td>
                                <td class="px-4 py-2.5 min-w-36">{{ order.expected_delivery ? new Date(order.expected_delivery).toLocaleDateString('en-GB') : 'N/A' }}</td>
                                <td class="px-4 py-2.5 min-w-44">
                                    <div v-if="editingStatus[order.id] !== undefined" class="flex items-center space-x-2">
                                        <select
                                            v-model="editingStatus[order.id]"
                                            class="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                            @change="updateOrderStatus(order.id, editingStatus[order.id])"
                                        >
                                            <option v-for="status in statusOptions" :key="status.id" :value="status.id">
                                                {{ status.name }}
                                            </option>
                                        </select>
                                        <button
                                            @click="cancelEditingStatus(order.id)"
                                            class="text-gray-400 hover:text-gray-600 text-sm"
                                            title="Cancel"
                                        >
                                            ✕
                                        </button>
                                    </div>
                                    <!-- Display Status -->
                                    <div v-else class="flex items-center space-x-2">
                                        <span
                                            :class="['px-3 py-1 rounded-full text-sm font-medium cursor-pointer', getStatusClass(order.status)]"
                                            @click="startEditingStatus(order.id, order.status)"
                                            title="Click to edit status"
                                        >
                                            {{ order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('_', ' ') }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-4 py-2.5" v-if="props.isAdmin">{{ order.lead.creator ? order.lead.creator.first_name : 'N/A' }}</td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown align="right" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink :href="route('orders.show', {order: order.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">View</span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink :href="route('orders.edit',{order: order.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">Edit</span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button" @click="openDeleteModal(order.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">Delete</span>
                                                </button>

                                                <button type="button" @click="downloadPdf(order.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">Download PDF</span>
                                                </button>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="10" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>

        <!-- Delete Confirmation Modal -->
        <Modal :show="modalVisible" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this order?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal">Cancel</SecondaryButton>
                    <DangerButton class="ml-3" @click="deleteOrder" :disabled="form.processing">
                        Delete Order
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
