<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id();
            $table->string('quotation_number')->unique();
            $table->string('client_name');
            $table->foreignId('county_id')->constrained('counties');
            $table->foreignId('lead_id')->nullable()->constrained('leads');
            $table->string('dimensions');
            $table->string('open_size');
            $table->string('box_style');
            $table->string('stock');
            $table->string('lamination');
            $table->string('printing');
            $table->string('add_ons')->nullable();
            $table->integer('qty_1');
            $table->integer('qty_2')->nullable();
            $table->integer('qty_3')->nullable();
            $table->integer('qty_4')->nullable();
            $table->decimal('price_qty_1', 10, 2);
            $table->decimal('price_qty_2', 10, 2)->nullable();
            $table->decimal('price_qty_3', 10, 2)->nullable();
            $table->decimal('price_qty_4', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['draft', 'sent', 'accepted', 'rejected', 'expired'])->default('draft');
            $table->date('valid_until')->nullable();
            $table->decimal('total_amount', 12, 2)->default(0);
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('quotations');
    }
};
