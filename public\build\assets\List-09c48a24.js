import{r as p,c as S,b as a,d,e as l,u as g,f as n,F as $,Z as X,g as e,h as z,v as q,G as ee,i as v,j as B,m as k,k as A,t as c,H as te,n as se}from"./app-5d829b53.js";import{_ as oe,b as le,a as O}from"./AdminLayout-f9e7be5b.js";import{M as ae,_ as ne}from"./Modal-39219406.js";import{D as ie}from"./DangerButton-55c4dbac.js";import{s as re,_ as de,a as ce}from"./ArrowIcon-49e4f1ff.js";import{_ as j}from"./SearchableDropdownNew-6707e237.js";import{_ as C}from"./InputLabel-876f960a.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ue={class:"animate-top"},me={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},_e=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Quotations")],-1),he={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},pe={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},ge=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ve={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},fe={class:"flex justify-between mb-2"},ye={class:"flex"},be=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),we={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},xe={key:0,class:"sm:col-span-4"},ke={class:"relative mt-2"},Ce={class:"sm:col-span-4"},Ae={class:"relative mt-2"},Ve={class:"sm:col-span-4"},Me={class:"relative mt-2"},Ne={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Se={class:"shadow rounded-lg"},$e={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Oe={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},je={class:"border-b-2"},De=["onClick"],ze={key:0},Be={class:"px-4 py-2.5 min-w-36"},Le={class:"px-4 py-2.5 min-w-28"},Qe={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Te={class:"px-4 py-2.5"},He={class:"px-4 py-2.5"},Ee={class:"px-4 py-2.5"},Ue={class:"px-4 py-2.5"},Ie={class:"flex items-center space-x-2 min-w-36"},Fe={key:0,class:"px-4 py-2.5"},Pe={class:"items-center px-4 py-2.5"},We={class:"flex items-center justify-start gap-4"},Ge=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Re=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),Ye=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),Ke=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Ze=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),Je=["onClick"],Xe=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),qe=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),et=[Xe,qe],tt=["onClick"],st=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),ot=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),lt=[st,ot],at=e("button",{class:"w-full flex items-center justify-center px-4 py-2 bg-green-600 border border-green-600 rounded-md shadow-sm text-sm font-medium text-white hover:bg-green-700"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"})]),A(" Convert to Order ")],-1),nt={key:1},it=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),rt=[it],dt={class:"p-6"},ct=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this quotation? ",-1),ut={class:"mt-6 flex justify-end"},xt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","isAdmin"],setup(i){const r=i,{form:V,search:b,sort:L,fetchData:mt,sortKey:Q,sortDirection:T}=re("quotations.index"),M=p(!1),D=p(null),H=S(()=>[{id:"",name:"All Agents"},...r.agents]),E=S(()=>[{id:"",name:"All Country"},...r.counties]),U=S(()=>[{id:"",name:"All Status"},...r.statusOptions]),I=[{field:"quotation_number",label:"QUOTATION NO",sortable:!0,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"lead.client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!0,visible:!0},{field:"lead.dimensions",label:"DIMENSIONS",sortable:!0,visible:!0},{field:"qty",label:"QTY",sortable:!1,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:r.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],F=s=>{D.value=s,M.value=!0},N=()=>{M.value=!1},P=()=>{V.delete(route("quotations.destroy",{quotation:D.value}),{onSuccess:()=>N()})},W=s=>({pending:"bg-blue-100 text-blue-800",quotation_ready:"bg-yellow-100 text-yellow-800",order_placed:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",u=p(r.agent_id||""),m=p(r.county_id||""),_=p(r.status||""),w=p("");p({});const G=(s,o)=>{u.value=s,x(w.value,u.value,m.value,_.value)},R=(s,o)=>{m.value=s,x(w.value,u.value,m.value,_.value)},Y=(s,o)=>{_.value=s,x(w.value,u.value,m.value,_.value)},x=(s,o,t,h)=>{w.value=s;const f=o===""?null:o,y=t===""?null:t,J=h===""?null:h;V.get(route("quotations.index",{search:s,agent_id:f,county_id:y,status:J}),{preserveState:!0})},K=s=>{window.open(route("quotations.pdf",s),"_blank")};function Z(s){return[{label:"1",qty:(s==null?void 0:s.qty_1)??null,price:(s==null?void 0:s.price_qty_1)??null},{label:"2",qty:(s==null?void 0:s.qty_2)??null,price:(s==null?void 0:s.price_qty_2)??null},{label:"3",qty:(s==null?void 0:s.qty_3)??null,price:(s==null?void 0:s.price_qty_3)??null},{label:"4",qty:(s==null?void 0:s.qty_4)??null,price:(s==null?void 0:s.price_qty_4)??null}].filter(t=>t.qty!=null).map(t=>t.price!=null?`Qty ${t.label}: ${t.qty} - £ ${t.price}`:`Qty ${t.label}: ${t.qty}`).join(", ")||"N/A"}return(s,o)=>(a(),d($,null,[l(g(X),{title:"Quotations"}),l(oe,null,{default:n(()=>[e("div",ue,[e("div",me,[_e,e("div",he,[e("div",pe,[ge,z(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>ee(b)?b.value=t:null),onInput:o[1]||(o[1]=t=>x(g(b),u.value,m.value,_.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for quotations..."},null,544),[[q,g(b)]])])])]),e("div",ve,[e("div",fe,[e("div",ye,[be,l(C,{for:"filters",value:"Filters"})])]),e("div",we,[r.isAdmin?(a(),d("div",xe,[l(C,{for:"agent_filter",value:"Agents"}),e("div",ke,[l(j,{options:H.value,modelValue:u.value,"onUpdate:modelValue":o[2]||(o[2]=t=>u.value=t),onOnchange:G},null,8,["options","modelValue"])])])):v("",!0),e("div",Ce,[l(C,{for:"county_filter",value:"Country"}),e("div",Ae,[l(j,{options:E.value,modelValue:m.value,"onUpdate:modelValue":o[3]||(o[3]=t=>m.value=t),onOnchange:R},null,8,["options","modelValue"])])]),e("div",Ve,[l(C,{for:"status_filter",value:"Status"}),e("div",Me,[l(j,{options:U.value,modelValue:_.value,"onUpdate:modelValue":o[4]||(o[4]=t=>_.value=t),onOnchange:Y},null,8,["options","modelValue"])])])])]),e("div",Ne,[e("div",Se,[e("table",$e,[e("thead",Oe,[e("tr",je,[(a(),d($,null,B(I,(t,h)=>z(e("th",{key:h,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:f=>g(L)(t.field,t.sortable)},[A(c(t.label)+" ",1),t.sortable?(a(),k(ce,{key:0,isSorted:g(Q)===t.field,direction:g(T)},null,8,["isSorted","direction"])):v("",!0)],8,De),[[te,t.visible]])),64))])]),i.data.data&&i.data.data.length>0?(a(),d("tbody",ze,[(a(!0),d($,null,B(i.data.data,t=>{var h,f;return a(),d("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Be,c(t.quotation_number),1),e("td",Le,c(t.lead?t.lead.lead_number:"N/A"),1),e("td",Qe,c((h=t.lead)==null?void 0:h.client_name),1),e("td",Te,c(t.county?t.county.name:"N/A"),1),e("td",He,c((f=t.lead)==null?void 0:f.dimensions),1),e("td",Ee,c(Z(t.lead)),1),e("td",Ue,[e("div",Ie,[e("span",{class:se(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",W(t.status)])},c(t.status.replace(/_/g," ").replace(/\b\w/g,y=>y.toUpperCase())),3)])]),r.isAdmin?(a(),d("td",Fe,c(t.lead.creator?t.lead.creator.first_name:"N/A"),1)):v("",!0),e("td",Pe,[e("div",We,[l(le,{align:"right",width:"48"},{trigger:n(()=>[Ge]),content:n(()=>[l(O,{href:s.route("quotations.show",{quotation:t.id})},{svg:n(()=>[Re]),text:n(()=>[Ye]),_:2},1032,["href"]),i.permissions.canEditQuotation?(a(),k(O,{key:0,href:s.route("quotations.edit",{quotation:t.id})},{svg:n(()=>[Ke]),text:n(()=>[Ze]),_:2},1032,["href"])):v("",!0),i.permissions.canDeleteQuotation?(a(),d("button",{key:1,type:"button",onClick:y=>F(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},et,8,Je)):v("",!0),e("button",{type:"button",onClick:y=>K(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},lt,8,tt),t.status==="quotation_ready"?(a(),k(O,{key:2,href:s.route("orders.convert",{quotation:t.id}),class:"w-full"},{svg:n(()=>[at]),_:2},1032,["href"])):v("",!0)]),_:2},1024)])])])}),128))])):(a(),d("tbody",nt,rt))])])]),i.data.data&&i.data.data.length>0?(a(),k(de,{key:0,class:"mt-6",links:i.data.links},null,8,["links"])):v("",!0)]),l(ae,{show:M.value,onClose:N},{default:n(()=>[e("div",dt,[ct,e("div",ut,[l(ne,{onClick:N},{default:n(()=>[A("Cancel")]),_:1}),l(ie,{class:"ml-3",onClick:P,disabled:g(V).processing},{default:n(()=>[A(" Delete Quotation ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{xt as default};
