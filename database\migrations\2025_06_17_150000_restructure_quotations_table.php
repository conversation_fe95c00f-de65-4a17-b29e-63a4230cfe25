<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('quotations', function (Blueprint $table) {
            // Remove duplicate fields that exist in leads table
            $table->dropColumn([
                'client_name',
                'county_id', 
                'dimensions',
                'open_size',
                'box_style',
                'stock',
                'lamination',
                'printing',
                'add_ons'
            ]);
            
            // Make lead_id required since we'll reference lead data
            $table->foreignId('lead_id')->change()->constrained('leads');
        });
    }

    public function down(): void
    {
        Schema::table('quotations', function (Blueprint $table) {
            // Add back the removed columns
            $table->string('client_name');
            $table->foreignId('county_id')->constrained('counties');
            $table->string('dimensions');
            $table->string('open_size');
            $table->string('box_style');
            $table->string('stock');
            $table->string('lamination');
            $table->string('printing');
            $table->string('add_ons')->nullable();
            
            // Make lead_id nullable again
            $table->foreignId('lead_id')->nullable()->change();
        });
    }
};
