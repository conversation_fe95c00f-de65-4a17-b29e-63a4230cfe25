import{T,r as f,w as I,b as u,d as c,e as a,u as d,f as A,F as E,Z as H,g as t,k as P,t as n,i as r,q as L}from"./app-df1bcebc.js";import{_ as Z,a as G}from"./AdminLayout-e6738fa9.js";import{_ as h,a as g}from"./TextInput-ff2a6c73.js";import{_}from"./InputLabel-df28ac37.js";import{P as J}from"./PrimaryButton-2296ddc6.js";import{_ as K}from"./TextArea-65f0749e.js";import{_ as V}from"./Checkbox-a3885079.js";import"./_plugin-vue_export-helper-c27b6911.js";const R={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},W=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"}," Convert Quotation to Order ",-1),X={class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},Y={class:"text-sm text-blue-800"},tt=t("strong",null,"Converting from Quotation:",-1),et={class:"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg"},st=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Lead Information",-1),lt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ot=t("p",{class:"text-sm font-medium text-gray-500"},"Client Name",-1),at={class:"text-sm text-gray-900"},it=t("p",{class:"text-sm font-medium text-gray-500"},"County",-1),dt={class:"text-sm text-gray-900"},nt=t("p",{class:"text-sm font-medium text-gray-500"},"Dimensions",-1),ut={class:"text-sm text-gray-900"},ct=t("p",{class:"text-sm font-medium text-gray-500"},"Open Size",-1),rt={class:"text-sm text-gray-900"},_t=t("p",{class:"text-sm font-medium text-gray-500"},"Box Style",-1),mt={class:"text-sm text-gray-900"},yt=t("p",{class:"text-sm font-medium text-gray-500"},"Stock",-1),qt={class:"text-sm text-gray-900"},xt=t("p",{class:"text-sm font-medium text-gray-500"},"Lamination",-1),vt={class:"text-sm text-gray-900"},gt=t("p",{class:"text-sm font-medium text-gray-500"},"Printing",-1),pt={class:"text-sm text-gray-900"},ft={key:0},ht=t("p",{class:"text-sm font-medium text-gray-500"},"Add-ons",-1),bt={class:"text-sm text-gray-900"},kt=["onSubmit"],Vt={class:"border-b border-gray-900/10 pb-12"},wt={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},At=t("h3",{class:"text-lg font-medium text-gray-900"},"Select Quantities for Order",-1),Ct=t("p",{class:"text-sm text-gray-600 mb-6"},"All available quantities are pre-selected. Uncheck quantities the client doesn't want:",-1),$t={key:0,class:"sm:col-span-3"},Qt={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Nt={key:0,class:"grid grid-cols-1 gap-4"},Ot={class:"text-sm text-gray-500 mt-1"},St={key:1,class:"sm:col-span-3"},Ft={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Ut={key:0,class:"grid grid-cols-1 gap-4"},Dt={class:"text-sm text-gray-500 mt-1"},Bt={key:2,class:"sm:col-span-3"},Pt={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},Mt={key:0,class:"grid grid-cols-1 gap-4"},jt={class:"text-sm text-gray-500 mt-1"},zt={key:3,class:"sm:col-span-3"},Tt={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},It={key:0,class:"grid grid-cols-1 gap-4"},Et={class:"text-sm text-gray-500 mt-1"},Ht={key:4,class:"sm:col-span-12"},Lt={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Zt={class:"flex items-center justify-between"},Gt={class:"text-lg font-semibold text-green-800"},Jt=t("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing from quotation ",-1),Kt=t("div",{class:"text-right"},[t("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),Rt=t("div",{class:"sm:col-span-12 mt-8"},[t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Order Information")],-1),Wt={class:"sm:col-span-6"},Xt={class:"sm:col-span-12"},Yt={class:"flex mt-6 items-center justify-between"},te={class:"ml-auto flex items-center justify-end gap-x-6"},ee=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),ce={__name:"Add",props:{quotation:{type:Object,required:!0}},setup(l){const s=l,e=T({quotation_id:s.quotation.id,lead_id:s.quotation.lead_id,selected_qty_1:s.quotation.lead.qty_1&&s.quotation.price_qty_1?s.quotation.lead.qty_1:"",selected_qty_2:s.quotation.lead.qty_2&&s.quotation.price_qty_2?s.quotation.lead.qty_2:"",selected_qty_3:s.quotation.lead.qty_3&&s.quotation.price_qty_3?s.quotation.lead.qty_3:"",selected_qty_4:s.quotation.lead.qty_4&&s.quotation.price_qty_4?s.quotation.lead.qty_4:"",notes:"",expected_delivery:"",tracking_number:""}),y=f(!!(s.quotation.lead.qty_1&&s.quotation.price_qty_1)),q=f(!!(s.quotation.lead.qty_2&&s.quotation.price_qty_2)),x=f(!!(s.quotation.lead.qty_3&&s.quotation.price_qty_3)),v=f(!!(s.quotation.lead.qty_4&&s.quotation.price_qty_4)),b=f(0),M=()=>{e.post(route("orders.store"),{preserveScroll:!0,onSuccess:()=>e.reset()})},p=()=>{let m=0;y.value&&e.selected_qty_1&&s.quotation.price_qty_1&&(m+=parseFloat(e.selected_qty_1)*parseFloat(s.quotation.price_qty_1)),q.value&&e.selected_qty_2&&s.quotation.price_qty_2&&(m+=parseFloat(e.selected_qty_2)*parseFloat(s.quotation.price_qty_2)),x.value&&e.selected_qty_3&&s.quotation.price_qty_3&&(m+=parseFloat(e.selected_qty_3)*parseFloat(s.quotation.price_qty_3)),v.value&&e.selected_qty_4&&s.quotation.price_qty_4&&(m+=parseFloat(e.selected_qty_4)*parseFloat(s.quotation.price_qty_4)),b.value=m},k=(m,o)=>{o||(e[`selected_qty_${m}`]=""),p()};I([()=>e.selected_qty_1,()=>y.value,()=>e.selected_qty_2,()=>q.value,()=>e.selected_qty_3,()=>x.value,()=>e.selected_qty_4,()=>v.value],p);const w=new Date;w.setDate(w.getDate()+7),e.expected_delivery=w.toISOString().split("T")[0],p();const j=()=>{s.quotation.lead.qty_1&&(y.value=!0,e.selected_qty_1=s.quotation.lead.qty_1),s.quotation.lead.qty_2&&(q.value=!0,e.selected_qty_2=s.quotation.lead.qty_2),s.quotation.lead.qty_3&&(x.value=!0,e.selected_qty_3=s.quotation.lead.qty_3),s.quotation.lead.qty_4&&(v.value=!0,e.selected_qty_4=s.quotation.lead.qty_4),p()},z=()=>{y.value=!1,q.value=!1,x.value=!1,v.value=!1,e.selected_qty_1="",e.selected_qty_2="",e.selected_qty_3="",e.selected_qty_4="",p()};return(m,o)=>(u(),c(E,null,[a(d(H),{title:"Convert Quotation to Order"}),a(Z,null,{default:A(()=>{var C,$,Q,N,O,S,F,U,D,B;return[t("div",R,[W,t("div",X,[t("p",Y,[tt,P(" "+n(l.quotation.quotation_number),1)])]),t("div",et,[st,t("div",lt,[t("div",null,[ot,t("p",at,n(((C=l.quotation.lead)==null?void 0:C.client_name)||"N/A"),1)]),t("div",null,[it,t("p",dt,n(((Q=($=l.quotation.lead)==null?void 0:$.county)==null?void 0:Q.name)||"N/A"),1)]),t("div",null,[nt,t("p",ut,n(((N=l.quotation.lead)==null?void 0:N.dimensions)||"N/A"),1)]),t("div",null,[ct,t("p",rt,n(((O=l.quotation.lead)==null?void 0:O.open_size)||"N/A"),1)]),t("div",null,[_t,t("p",mt,n(((S=l.quotation.lead)==null?void 0:S.box_style)||"N/A"),1)]),t("div",null,[yt,t("p",qt,n(((F=l.quotation.lead)==null?void 0:F.stock)||"N/A"),1)]),t("div",null,[xt,t("p",vt,n(((U=l.quotation.lead)==null?void 0:U.lamination)||"N/A"),1)]),t("div",null,[gt,t("p",pt,n(((D=l.quotation.lead)==null?void 0:D.printing)||"N/A"),1)]),(B=l.quotation.lead)!=null&&B.add_ons?(u(),c("div",ft,[ht,t("p",bt,n(l.quotation.lead.add_ons),1)])):r("",!0)])]),t("form",{onSubmit:L(M,["prevent"])},[t("div",Vt,[t("div",wt,[t("div",{class:"sm:col-span-12 mt-8"},[t("div",{class:"flex items-center justify-between mb-4"},[At,t("div",{class:"flex space-x-2"},[t("button",{type:"button",onClick:j,class:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"}," Select All "),t("button",{type:"button",onClick:z,class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"}," Clear All ")])]),Ct]),l.quotation.lead.qty_1&&l.quotation.price_qty_1?(u(),c("div",$t,[t("div",Qt,[a(V,{checked:y.value,"onUpdate:checked":[o[0]||(o[0]=i=>y.value=i),o[1]||(o[1]=i=>k(1,i))]},null,8,["checked"]),a(_,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),y.value?(u(),c("div",Nt,[t("div",null,[a(_,{for:"selected_qty_1",value:`Client Order Qty 1 (Available: ${l.quotation.lead.qty_1})`},null,8,["value"]),a(h,{id:"selected_qty_1",type:"number",modelValue:d(e).selected_qty_1,"onUpdate:modelValue":o[2]||(o[2]=i=>d(e).selected_qty_1=i),max:l.quotation.lead.qty_1,min:"1",placeholder:`Max: ${l.quotation.lead.qty_1}`},null,8,["modelValue","max","placeholder"]),a(g,{message:d(e).errors.selected_qty_1},null,8,["message"]),t("p",Ot,"Price: $"+n(l.quotation.price_qty_1)+" per unit",1)])])):r("",!0)])):r("",!0),l.quotation.lead.qty_2&&l.quotation.price_qty_2?(u(),c("div",St,[t("div",Ft,[a(V,{checked:q.value,"onUpdate:checked":[o[3]||(o[3]=i=>q.value=i),o[4]||(o[4]=i=>k(2,i))]},null,8,["checked"]),a(_,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),q.value?(u(),c("div",Ut,[t("div",null,[a(_,{for:"selected_qty_2",value:`Client Order Qty 2 (Available: ${l.quotation.lead.qty_2})`},null,8,["value"]),a(h,{id:"selected_qty_2",type:"number",modelValue:d(e).selected_qty_2,"onUpdate:modelValue":o[5]||(o[5]=i=>d(e).selected_qty_2=i),max:l.quotation.lead.qty_2,min:"1",placeholder:`Max: ${l.quotation.lead.qty_2}`},null,8,["modelValue","max","placeholder"]),a(g,{message:d(e).errors.selected_qty_2},null,8,["message"]),t("p",Dt,"Price: $"+n(l.quotation.price_qty_2)+" per unit",1)])])):r("",!0)])):r("",!0),l.quotation.lead.qty_3&&l.quotation.price_qty_3?(u(),c("div",Bt,[t("div",Pt,[a(V,{checked:x.value,"onUpdate:checked":[o[6]||(o[6]=i=>x.value=i),o[7]||(o[7]=i=>k(3,i))]},null,8,["checked"]),a(_,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),x.value?(u(),c("div",Mt,[t("div",null,[a(_,{for:"selected_qty_3",value:`Client Order Qty 3 (Available: ${l.quotation.lead.qty_3})`},null,8,["value"]),a(h,{id:"selected_qty_3",type:"number",modelValue:d(e).selected_qty_3,"onUpdate:modelValue":o[8]||(o[8]=i=>d(e).selected_qty_3=i),max:l.quotation.lead.qty_3,min:"1",placeholder:`Max: ${l.quotation.lead.qty_3}`},null,8,["modelValue","max","placeholder"]),a(g,{message:d(e).errors.selected_qty_3},null,8,["message"]),t("p",jt,"Price: $"+n(l.quotation.price_qty_3)+" per unit",1)])])):r("",!0)])):r("",!0),l.quotation.lead.qty_4&&l.quotation.price_qty_4?(u(),c("div",zt,[t("div",Tt,[a(V,{checked:v.value,"onUpdate:checked":[o[9]||(o[9]=i=>v.value=i),o[10]||(o[10]=i=>k(4,i))]},null,8,["checked"]),a(_,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),v.value?(u(),c("div",It,[t("div",null,[a(_,{for:"selected_qty_4",value:`Client Order Qty 4 (Available: ${l.quotation.lead.qty_4})`},null,8,["value"]),a(h,{id:"selected_qty_4",type:"number",modelValue:d(e).selected_qty_4,"onUpdate:modelValue":o[11]||(o[11]=i=>d(e).selected_qty_4=i),max:l.quotation.lead.qty_4,min:"1",placeholder:`Max: ${l.quotation.lead.qty_4}`},null,8,["modelValue","max","placeholder"]),a(g,{message:d(e).errors.selected_qty_4},null,8,["message"]),t("p",Et,"Price: $"+n(l.quotation.price_qty_4)+" per unit",1)])])):r("",!0)])):r("",!0),b.value>0?(u(),c("div",Ht,[t("div",Lt,[t("div",Zt,[t("div",null,[t("p",Gt," Order Total: $"+n(b.value.toFixed(2)),1),Jt]),Kt])])])):r("",!0),Rt,t("div",Wt,[a(_,{for:"expected_delivery",value:"Expected Delivery Date"}),a(h,{id:"expected_delivery",type:"date",modelValue:d(e).expected_delivery,"onUpdate:modelValue":o[12]||(o[12]=i=>d(e).expected_delivery=i)},null,8,["modelValue"]),a(g,{message:d(e).errors.expected_delivery},null,8,["message"])]),t("div",Xt,[a(_,{for:"notes",value:"Order Notes"}),a(K,{id:"notes",modelValue:d(e).notes,"onUpdate:modelValue":o[13]||(o[13]=i=>d(e).notes=i),rows:"4",placeholder:"Any special instructions or notes for this order..."},null,8,["modelValue"]),a(g,{message:d(e).errors.notes},null,8,["message"])])])]),t("div",Yt,[t("div",te,[a(G,{href:m.route("quotations.show",l.quotation.id)},{svg:A(()=>[ee]),_:1},8,["href"]),a(J,{disabled:d(e).processing||b.value===0},{default:A(()=>[P(" Save ")]),_:1},8,["disabled"])])])],40,kt)])]}),_:1})],64))}};export{ce as default};
