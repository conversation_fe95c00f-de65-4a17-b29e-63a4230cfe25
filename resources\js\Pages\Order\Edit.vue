<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps({
    data: {
        type: Object,
        required: true
    }
});

// Check if user is admin
const isAdmin = ref(true); // You can pass this from backend

// Initialize form with existing order data
const form = useForm({
    status: props.data.status || 'pending',
    tracking_number: props.data.tracking_number || '',
    expected_delivery: props.data.expected_delivery || '',
    actual_delivery: props.data.actual_delivery || '',
    notes: props.data.notes || ''
});

const statusOptions = [
    { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'confirmed', label: 'Confirmed', color: 'bg-blue-100 text-blue-800' },
    { value: 'under_production', label: 'Under Production', color: 'bg-purple-100 text-purple-800' },
    { value: 'shipped', label: 'Shipped', color: 'bg-indigo-100 text-indigo-800' },
    { value: 'delivered', label: 'Delivered', color: 'bg-green-100 text-green-800' }
];

const submit = () => {
    form.put(route('orders.update', props.data.id), {
        preserveScroll: true,
    });
};

const confirmOrder = () => {
    if (confirm('Are you sure you want to confirm this order? This will update the lead with the selected quantities.')) {
        form.post(route('orders.confirm', props.data.id), {
            preserveScroll: true,
        });
    }
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};
</script>

<template>
    <Head title="Edit Order" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">
                        Edit Order - {{ data.order_number }}
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">Client: {{ data.client_name }}</p>
                </div>
                <div class="flex space-x-3">
                    <SecondaryButton 
                        v-if="!data.is_confirmed && data.status === 'pending'" 
                        @click="confirmOrder"
                        class="bg-green-600 text-white hover:bg-green-700"
                    >
                        Confirm Order
                    </SecondaryButton>
                    <span 
                        v-if="data.is_confirmed" 
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                    >
                        ✓ Confirmed
                    </span>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Order Summary</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Quotation</p>
                        <p class="text-sm text-blue-600">{{ data.quotation?.quotation_number || 'N/A' }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">County</p>
                        <p class="text-sm text-gray-900">{{ data.county?.name || 'N/A' }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Amount</p>
                        <p class="text-lg font-semibold text-green-600">{{ formatCurrency(data.total_amount) }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">Created By</p>
                        <p class="text-sm text-gray-900">{{ data.creator?.first_name }} {{ data.creator?.last_name }}</p>
                    </div>
                </div>
            </div>

            <!-- Selected Quantities -->
            <div class="mb-8 p-4 bg-blue-50 rounded-lg">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Selected Quantities</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div v-if="data.selected_qty_1" class="bg-white p-3 rounded border">
                        <p class="text-sm font-medium text-gray-500">Quantity 1</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_1).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_1) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_1 * data.price_qty_1) }}</p>
                    </div>
                    <div v-if="data.selected_qty_2" class="bg-white p-3 rounded border">
                        <p class="text-sm font-medium text-gray-500">Quantity 2</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_2).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_2) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_2 * data.price_qty_2) }}</p>
                    </div>
                    <div v-if="data.selected_qty_3" class="bg-white p-3 rounded border">
                        <p class="text-sm font-medium text-gray-500">Quantity 3</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_3).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_3) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_3 * data.price_qty_3) }}</p>
                    </div>
                    <div v-if="data.selected_qty_4" class="bg-white p-3 rounded border">
                        <p class="text-sm font-medium text-gray-500">Quantity 4</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_4).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_4) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_4 * data.price_qty_4) }}</p>
                    </div>
                </div>
            </div>

            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        
                        <!-- Order Status -->
                        <div class="sm:col-span-6">
                            <InputLabel for="status" value="Order Status *"/>
                            <select
                                id="status"
                                v-model="form.status"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                required
                            >
                                <option v-for="status in statusOptions" :key="status.value" :value="status.value">
                                    {{ status.label }}
                                </option>
                            </select>
                            <InputError :message="form.errors.status"/>
                        </div>

                        <!-- Tracking Number (only for shipped orders) -->
                        <div class="sm:col-span-6" v-if="form.status === 'shipped' || data.tracking_number">
                            <InputLabel for="tracking_number" value="Tracking Number"/>
                            <TextInput
                                id="tracking_number"
                                type="text"
                                v-model="form.tracking_number"
                                placeholder="Enter tracking number"
                            />
                            <InputError :message="form.errors.tracking_number"/>
                            <p class="text-sm text-gray-500 mt-1">Add tracking number when order is shipped</p>
                        </div>

                        <!-- Expected Delivery -->
                        <div class="sm:col-span-6">
                            <InputLabel for="expected_delivery" value="Expected Delivery Date"/>
                            <TextInput
                                id="expected_delivery"
                                type="date"
                                v-model="form.expected_delivery"
                            />
                            <InputError :message="form.errors.expected_delivery"/>
                        </div>

                        <!-- Actual Delivery (only for delivered orders) -->
                        <div class="sm:col-span-6" v-if="form.status === 'delivered' || data.actual_delivery">
                            <InputLabel for="actual_delivery" value="Actual Delivery Date"/>
                            <TextInput
                                id="actual_delivery"
                                type="date"
                                v-model="form.actual_delivery"
                            />
                            <InputError :message="form.errors.actual_delivery"/>
                        </div>

                        <!-- Notes -->
                        <div class="sm:col-span-12">
                            <InputLabel for="notes" value="Order Notes"/>
                            <TextArea
                                id="notes"
                                v-model="form.notes"
                                rows="4"
                                placeholder="Add any notes about this order..."
                            />
                            <InputError :message="form.errors.notes"/>
                        </div>

                        <!-- Product Specifications (Read-only) -->
                        <div class="sm:col-span-12 mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Product Specifications</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Dimensions</p>
                                    <p class="text-sm text-gray-900">{{ data.dimensions }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Open Size</p>
                                    <p class="text-sm text-gray-900">{{ data.open_size }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Box Style</p>
                                    <p class="text-sm text-gray-900">{{ data.box_style }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Stock</p>
                                    <p class="text-sm text-gray-900">{{ data.stock }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Lamination</p>
                                    <p class="text-sm text-gray-900">{{ data.lamination }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Printing</p>
                                    <p class="text-sm text-gray-900">{{ data.printing }}</p>
                                </div>
                                <div v-if="data.add_ons" class="md:col-span-3">
                                    <p class="text-sm font-medium text-gray-500">Add-ons</p>
                                    <p class="text-sm text-gray-900">{{ data.add_ons }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('orders.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">
                            Update Order
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
