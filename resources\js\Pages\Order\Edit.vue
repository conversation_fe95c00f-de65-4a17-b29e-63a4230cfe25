<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

const props = defineProps({
    data: {
        type: Object,
        required: true
    }
});

// Check if user is admin
const isAdmin = ref(true); // You can pass this from backend

// Checkbox states for quantity editing
const includeQty1 = ref(!!props.data.selected_qty_1);
const includeQty2 = ref(!!props.data.selected_qty_2);
const includeQty3 = ref(!!props.data.selected_qty_3);
const includeQty4 = ref(!!props.data.selected_qty_4);

const totalAmount = ref(0);

// Initialize form with existing order data
const form = useForm({
    selected_qty_1: props.data.lead.qty_1 || '',
    selected_qty_2: props.data.lead.qty_2 || '',
    selected_qty_3: props.data.lead.qty_3 || '',
    selected_qty_4: props.data.lead.qty_4 || '',
    tracking_number: props.data.tracking_number || '',
    expected_delivery: props.data.expected_delivery || '',
    actual_delivery: props.data.actual_delivery || '',
    notes: props.data.notes || ''
});

// Validation function
const validateForm = () => {
    const hasSelectedQuantity = includeQty1.value || includeQty2.value || includeQty3.value || includeQty4.value;

    if (!hasSelectedQuantity) {
        alert('Please select at least one quantity for the order.');
        return false;
    }

    // Check if selected quantities have values
    if (includeQty1.value && !form.selected_qty_1) {
        alert('Please enter a value for Quantity 1.');
        return false;
    }
    if (includeQty2.value && !form.selected_qty_2) {
        alert('Please enter a value for Quantity 2.');
        return false;
    }
    if (includeQty3.value && !form.selected_qty_3) {
        alert('Please enter a value for Quantity 3.');
        return false;
    }
    if (includeQty4.value && !form.selected_qty_4) {
        alert('Please enter a value for Quantity 4.');
        return false;
    }

    return true;
};

const submit = () => {
    if (!validateForm()) {
        return;
    }

    const formData = prepareFormData();

    // Create a new form instance with the prepared data
    const submitForm = useForm(formData);

    submitForm.put(route('orders.update', props.data.id), {
        preserveScroll: true,
    });
};

const formatCurrency = (amount) => {
    const countyCurrencyMap = {
        'United Kingdom': { locale: 'en-GB', currency: 'GBP' },
        'United States': { locale: 'en-US', currency: 'USD' },
        'Canada': { locale: 'en-CA', currency: 'CAD' },
        'Australia': { locale: 'en-AU', currency: 'AUD' }
    };

    const countyName = props.data.lead?.county?.name || 'UK'; // default fallback
    const matchedKey = Object.keys(countyCurrencyMap).find(key =>
        countyName.toLowerCase().includes(key.toLowerCase())
    );

    const { locale, currency } = countyCurrencyMap[matchedKey] || countyCurrencyMap['UK'];

    const formattedAmount = new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        // currencyDisplay: 'code',  // Shows USD, AUD, CAD instead of just $
        currencyDisplay: 'symbol'  // Keep normal symbol like $ or £
    }).format(amount);
    return `${currency} ${formattedAmount}`;
};

// Calculate total amount when quantities change
const calculateTotal = () => {
    let total = 0;

    if (includeQty1.value && form.selected_qty_1 && props.data.quotation?.price_qty_1) {
        total += parseFloat(form.selected_qty_1) * parseFloat(props.data.quotation.price_qty_1);
    }
    if (includeQty2.value && form.selected_qty_2 && props.data.quotation?.price_qty_2) {
        total += parseFloat(form.selected_qty_2) * parseFloat(props.data.quotation.price_qty_2);
    }
    if (includeQty3.value && form.selected_qty_3 && props.data.quotation?.price_qty_3) {
        total += parseFloat(form.selected_qty_3) * parseFloat(props.data.quotation.price_qty_3);
    }
    if (includeQty4.value && form.selected_qty_4 && props.data.quotation?.price_qty_4) {
        total += parseFloat(form.selected_qty_4) * parseFloat(props.data.quotation.price_qty_4);
    }

    totalAmount.value = total;
};

// Clear quantity when checkbox is unchecked
const handleQtyCheckbox = (qtyNumber, isChecked) => {
    // if (!isChecked) {
    //     form[`selected_qty_${qtyNumber}`] = '';
    // }
    calculateTotal();
};

// Prepare form data for submission - only include checked quantities
const prepareFormData = () => {
    const formData = {
        status: form.status,
        tracking_number: form.tracking_number,
        expected_delivery: form.expected_delivery,
        actual_delivery: form.actual_delivery,
        notes: form.notes,
        // Set all quantities to null first
        selected_qty_1: null,
        selected_qty_2: null,
        selected_qty_3: null,
        selected_qty_4: null,
        price_qty_1: null,
        price_qty_2: null,
        price_qty_3: null,
        price_qty_4: null,
    };

    // Only include checked quantities
    if (includeQty1.value && form.selected_qty_1) {
        formData.selected_qty_1 = form.selected_qty_1;
        formData.price_qty_1 = props.data.quotation?.price_qty_1;
    }
    if (includeQty2.value && form.selected_qty_2) {
        formData.selected_qty_2 = form.selected_qty_2;
        formData.price_qty_2 = props.data.quotation?.price_qty_2;
    }
    if (includeQty3.value && form.selected_qty_3) {
        formData.selected_qty_3 = form.selected_qty_3;
        formData.price_qty_3 = props.data.quotation?.price_qty_3;
    }
    if (includeQty4.value && form.selected_qty_4) {
        formData.selected_qty_4 = form.selected_qty_4;
        formData.price_qty_4 = props.data.quotation?.price_qty_4;
    }

    // Calculate total amount
    formData.total_amount = totalAmount.value;

    return formData;
};

// Watch for changes in quantities and checkboxes
watch([
    () => form.selected_qty_1, () => includeQty1.value,
    () => form.selected_qty_2, () => includeQty2.value,
    () => form.selected_qty_3, () => includeQty3.value,
    () => form.selected_qty_4, () => includeQty4.value
], calculateTotal);

// Calculate initial total
calculateTotal();
</script>

<template>
    <Head title="Orders" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 rounded-lg border">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                <div>
                    <h2 class="text-xl sm:text-2xl font-semibold leading-7 text-gray-900">
                        Edit Order - {{ data.order_number }}
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">
                        Client: {{ data.lead.client_name }}
                    </p>
                </div>
                <div class="flex justify-start sm:justify-end">
                    <span v-if="data.is_confirmed" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        ✓ Confirmed
                    </span>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <p class="text-sm font-semibold text-gray-900">Quotation</p>
                        <p class="text-sm text-gray-700">{{ data.quotation?.quotation_number || 'N/A' }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-semibold text-gray-900">Lead</p>
                        <p class="text-sm text-gray-700">{{ data.lead.lead_number || 'N/A' }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-semibold text-gray-900">Total Amount</p>
                        <p class="text-sm font-semibold text-green-700">{{ formatCurrency(data.total_amount) }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-semibold text-gray-900">Created By</p>
                        <p class="text-sm text-gray-700">{{ data.creator?.first_name }} {{ data.creator?.last_name }}</p>
                    </div>
                </div>
            </div>

            <!-- Selected Quantities -->
            <!-- <div class="mb-8 p-4 bg-blue-50 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Selected Quantities</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div v-if="data.selected_qty_1" class="bg-white p-3 rounded border">
                        <p class="text-sm font-semibold text-gray-900">Quantity 1</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_1).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_1) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_1 * data.price_qty_1) }}</p>
                    </div>
                    <div v-if="data.selected_qty_2" class="bg-white p-3 rounded border">
                        <p class="text-sm font-semibold text-gray-900">Quantity 2</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_2).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_2) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_2 * data.price_qty_2) }}</p>
                    </div>
                    <div v-if="data.selected_qty_3" class="bg-white p-3 rounded border">
                        <p class="text-sm font-semibold text-gray-900">Quantity 3</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_3).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_3) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_3 * data.price_qty_3) }}</p>
                    </div>
                    <div v-if="data.selected_qty_4" class="bg-white p-3 rounded border">
                        <p class="text-sm font-semibold text-gray-900">Quantity 4</p>
                        <p class="text-lg font-semibold text-gray-900">{{ parseInt(data.selected_qty_4).toLocaleString() }} pcs</p>
                        <p class="text-sm text-gray-600">{{ formatCurrency(data.price_qty_4) }} each</p>
                        <p class="text-sm font-medium text-green-600">{{ formatCurrency(data.selected_qty_4 * data.price_qty_4) }}</p>
                    </div>
                </div>
            </div> -->

            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <!-- Quantity Editing Section -->
                        <div class="sm:col-span-12">
                            <h3 class="text-lg font-semibold text-gray-900">Edit Order Quantities</h3>
                            <p class="text-sm text-gray-600">Current order quantities are shown. Check/uncheck to add/remove quantities. Use "Select All" to see all available options:</p>

                            <!-- Validation Error for Quantities -->
                            <div v-if="$page.props.errors.quantities" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                <p class="text-sm text-red-600">{{ $page.props.errors.quantities }}</p>
                            </div>
                        </div>
                        <div class="sm:col-span-3" v-if="data.lead?.qty_1 && data.quotation?.price_qty_1">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty1"
                                    @update:checked="(checked) => handleQtyCheckbox(1, checked)"
                                />
                                <InputLabel value="Client wants Quantity 1" class="text-base font-medium text-blue-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <InputLabel for="selected_qty_1" :value="`Order Qty 1 (Available: ${data.lead.qty_1})`"/>
                                    <TextInput
                                        id="selected_qty_1"
                                        type="number"
                                        v-model="form.selected_qty_1"
                                        :max="data.lead.qty_1"
                                        min="1"
                                        :placeholder="`Max: ${data.lead.qty_1}`"
                                        :disabled="true"
                                    />
                                    <InputError :message="form.errors.selected_qty_1"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: {{ formatCurrency(data.quotation.price_qty_1) }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <div class="sm:col-span-3" v-if="data.lead?.qty_2 && data.quotation?.price_qty_2">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty2"
                                    @update:checked="(checked) => handleQtyCheckbox(2, checked)"
                                />
                                <InputLabel value="Client wants Quantity 2" class="text-base font-medium text-green-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <InputLabel for="selected_qty_2" :value="`Order Qty 2 (Available: ${data.lead.qty_2})`"/>
                                    <TextInput
                                        id="selected_qty_2"
                                        type="number"
                                        v-model="form.selected_qty_2"
                                        :max="data.lead.qty_2"
                                        min="1"
                                        :placeholder="`Max: ${data.lead.qty_2}`"
                                        :disabled="true"
                                    />
                                    <InputError :message="form.errors.selected_qty_2"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: {{ formatCurrency(data.quotation.price_qty_2) }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <div class="sm:col-span-3" v-if="data.lead?.qty_3 && data.quotation?.price_qty_3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty3"
                                    @update:checked="(checked) => handleQtyCheckbox(3, checked)"
                                />
                                <InputLabel value="Client wants Quantity 3" class="text-base font-medium text-yellow-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <InputLabel for="selected_qty_3" :value="`Order Qty 3 (Available: ${data.lead.qty_3})`"/>
                                    <TextInput
                                        id="selected_qty_3"
                                        type="number"
                                        v-model="form.selected_qty_3"
                                        :max="data.lead.qty_3"
                                        min="1"
                                        :placeholder="`Max: ${data.lead.qty_3}`"
                                        :disabled="true"
                                    />
                                    <InputError :message="form.errors.selected_qty_3"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: {{ formatCurrency(data.quotation.price_qty_3) }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <div class="sm:col-span-3" v-if="data.lead?.qty_4 && data.quotation?.price_qty_4">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty4"
                                    @update:checked="(checked) => handleQtyCheckbox(4, checked)"
                                />
                                <InputLabel value="Client wants Quantity 4" class="text-base font-medium text-purple-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <InputLabel for="selected_qty_4" :value="`Order Qty 4 (Available: ${data.lead.qty_4})`"/>
                                    <TextInput
                                        id="selected_qty_4"
                                        type="number"
                                        v-model="form.selected_qty_4"
                                        :max="data.lead.qty_4"
                                        min="1"
                                        :placeholder="`Max: ${data.lead.qty_4}`"
                                        :disabled="true"
                                    />
                                    <InputError :message="form.errors.selected_qty_4"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: {{ formatCurrency(data.quotation.price_qty_4) }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <!-- Updated Total Display -->
                        <div class="sm:col-span-12" v-if="totalAmount > 0">
                            <div class="bg-green-50 border border-green-200 p-6 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-lg font-semibold text-green-800">
                                            Updated Order Total: {{ formatCurrency(totalAmount.toFixed(2)) }}
                                        </p>
                                        <p class="text-sm text-green-600 mt-1">
                                            Based on selected quantities and pricing
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Lead Information (Read-only) -->
                        <div class="sm:col-span-12 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Lead Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Client Name</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.client_name || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">County</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.county?.name || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Dimensions</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.dimensions || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Open Size</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.open_size || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Box Style</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.box_style || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Stock</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.stock || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Lamination</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.lamination || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Printing</p>
                                    <p class="text-sm text-gray-700">{{ data.lead?.printing || 'N/A' }}</p>
                                </div>
                                <div v-if="data.lead?.add_ons">
                                    <p class="text-sm font-semibold text-gray-900">Add-ons</p>
                                    <p class="text-sm text-gray-700">{{ data.lead.add_ons }}</p>
                                </div>
                            </div>
                        </div>


                        <!-- Tracking Number (only for shipped orders) -->
                        <div class="sm:col-span-4">
                            <InputLabel for="tracking_number" value="Tracking Number"/>
                            <TextInput
                                id="tracking_number"
                                type="text"
                                v-model="form.tracking_number"
                                placeholder="Enter tracking number"
                            />
                            <InputError :message="form.errors.tracking_number"/>
                            <p class="text-sm text-gray-500 mt-1">Add tracking number when order is shipped</p>
                        </div>

                        <!-- Expected Delivery -->
                        <div class="sm:col-span-4">
                            <InputLabel for="expected_delivery" value="Expected Delivery Date"/>
                            <input
                                type="date"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                :value="form.expected_delivery ? form.expected_delivery.slice(0, 10) : ''"
                                @input="form.expected_delivery = $event.target.value"
                            />
                            <InputError :message="form.errors.expected_delivery"/>
                        </div>

                        <!-- Actual Delivery (only for delivered orders) -->
                        <div class="sm:col-span-4">
                            <InputLabel for="actual_delivery" value="Actual Delivery Date"/>
                            <input
                                type="date"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                :value="form.actual_delivery ? form.actual_delivery.slice(0, 10) : ''"
                                @input="form.actual_delivery = $event.target.value"
                            />
                            <InputError :message="form.errors.actual_delivery"/>
                        </div>

                        <!-- Notes -->
                        <div class="sm:col-span-12">
                            <InputLabel for="notes" value="Order Notes"/>
                            <TextArea
                                id="notes"
                                v-model="form.notes"
                                rows="4"
                                placeholder="Add any notes about this order..."
                            />
                            <InputError :message="form.errors.notes"/>
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('orders.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
