import{r as k,b as l,d,e as r,u as N,f as m,F as f,Z as O,h as t,t as s,n as u,l as h,j as n,k as V,O as y}from"./app-e21f56bc.js";import{_ as z,a as v}from"./AdminLayout-46709983.js";import{M as F,_ as U}from"./Modal-04ee4cac.js";import{_ as E}from"./CreateButton-b93ed522.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const H={class:"animate-top"},I={class:"flex justify-between items-center mb-6"},P={class:"text-2xl font-semibold leading-7 text-gray-900"},q=t("p",{class:"text-sm text-gray-600 mt-1"},"Task Details",-1),Q={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},R={class:"lg:col-span-2 space-y-6"},Y={class:"bg-white border rounded-lg p-6 bg-white p-4 shadowrounded-lg border"},Z=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Task Information",-1),G={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},J=t("label",{class:"block text-sm font-semibold text-gray-900"},"Type",-1),K={class:"mt-1"},W=t("label",{class:"block text-sm font-semibold text-gray-900"},"Priority",-1),X={class:"mt-1"},tt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),et={class:"mt-1"},st=t("label",{class:"block text-sm font-semibold text-gray-900"},"Assigned To",-1),ot={class:"mt-1 text-sm text-gray-700"},at=t("label",{class:"block text-sm font-semibold text-gray-900"},"Due Date",-1),lt={key:0,class:"ml-2 text-red-500"},dt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),nt={class:"mt-1 text-sm text-gray-700"},it={key:0,class:"mt-6"},ct=t("label",{class:"block text-sm font-semibold text-gray-900 mb-2"},"Description",-1),rt={class:"bg-gray-50 rounded-lg p-4"},mt={class:"text-sm text-gray-700 whitespace-pre-wrap"},ut={key:1,class:"mt-6"},gt=t("label",{class:"block text-sm font-semibold text-gray-900 mb-2"},"Notes",-1),ht={class:"bg-gray-50 rounded-lg p-4"},xt={class:"text-sm text-gray-700 whitespace-pre-wrap"},_t={key:0,class:"bg-white border rounded-lg p-6"},bt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Related Lead",-1),kt={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},ft={class:"flex items-center"},yt=t("div",{class:"flex-shrink-0"},[t("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1),vt={class:"ml-4"},wt={class:"text-sm font-medium text-blue-900"},pt={class:"text-sm text-blue-700"},Ct={key:0},Tt={key:1},St={key:2},Dt={key:1,class:"bg-white border rounded-lg p-6"},Mt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Activity History",-1),jt={class:"space-y-4"},$t={class:"flex-shrink-0"},Bt={class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},Lt={class:"text-xs"},At={class:"flex-1"},Nt={class:"text-sm font-medium text-gray-900"},Ot={class:"text-sm text-gray-600"},Vt={class:"text-xs text-gray-500 mt-1"},zt={class:"space-y-6"},Ft={class:"bg-white border rounded-lg p-6"},Ut=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1),Et={class:"space-y-3"},Ht=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),h(" Edit Task ")],-1),It=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),h(" Back to List ")],-1),Pt={class:"bg-white border rounded-lg p-6"},qt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Timeline",-1),Qt={class:"space-y-3"},Rt={class:"flex items-center text-sm"},Yt=t("div",{class:"w-2 h-2 bg-blue-500 rounded-full mr-3"},null,-1),Zt=t("div",{class:"font-medium"},"Created",-1),Gt={class:"text-gray-700"},Jt={class:"flex items-center text-sm"},Kt=t("div",{class:"font-medium"},"Due Date",-1),Wt={key:0,class:"flex items-center text-sm"},Xt=t("div",{class:"w-2 h-2 bg-green-500 rounded-full mr-3"},null,-1),te=t("div",{class:"font-medium"},"Completed",-1),ee={class:"text-gray-700"},se={class:"p-6"},oe=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to complete this task? ",-1),ae={class:"mt-6 flex justify-end space-x-4"},me={__name:"Show",props:{task:Object},setup(e){const w=e,g=o=>{const a=new Date(o),i=String(a.getDate()).padStart(2,"0"),$=String(a.getMonth()+1).padStart(2,"0"),B=a.getFullYear(),L=String(a.getHours()).padStart(2,"0"),A=String(a.getMinutes()).padStart(2,"0");return`${i}/${$}/${B} ${L}:${A}`},p=o=>o.replace("_"," ").replace(/\b\w/g,a=>a.toUpperCase()),C=o=>o.replace("_"," ").replace(/\b\w/g,a=>a.toUpperCase()),c=o=>new Date(o)<new Date&&w.task.status!=="completed",T=o=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[o]||"bg-gray-100 text-gray-800",S=o=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",D=o=>({pending:"bg-yellow-100 text-yellow-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",x=k(!1),b=k(null),M=o=>{b.value=o,x.value=!0},_=()=>{x.value=!1},j=()=>{y.post(route("tasks.complete",b.value),{onSuccess:()=>{_(),y.reload()}})};return(o,a)=>(l(),d(f,null,[r(N(O),{title:"Tasks"}),r(z,null,{default:m(()=>[t("div",H,[t("div",I,[t("div",null,[t("h2",P,s(e.task.title),1),q])]),t("div",Q,[t("div",R,[t("div",Y,[Z,t("div",G,[t("div",null,[J,t("div",K,[t("span",{class:u(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",T(e.task.type)])},s(p(e.task.type)),3)])]),t("div",null,[W,t("div",X,[t("span",{class:u(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",S(e.task.priority)])},s(e.task.priority.toUpperCase()),3)])]),t("div",null,[tt,t("div",et,[t("span",{class:u(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",D(e.task.status)])},s(C(e.task.status)),3)])]),t("div",null,[st,t("div",ot,s(e.task.assigned_to.first_name)+" "+s(e.task.assigned_to.last_name),1)]),t("div",null,[at,t("div",{class:u(["mt-1 text-sm",{"text-red-600 font-semibold":c(e.task.due_date),"text-gray-900":!c(e.task.due_date)}])},[h(s(g(e.task.due_date))+" ",1),c(e.task.due_date)?(l(),d("span",lt,"⚠️ Overdue")):n("",!0)],2)]),t("div",null,[dt,t("div",nt,s(e.task.created_by.first_name)+" "+s(e.task.created_by.last_name),1)])]),e.task.description?(l(),d("div",it,[ct,t("div",rt,[t("p",mt,s(e.task.description),1)])])):n("",!0),e.task.notes?(l(),d("div",ut,[gt,t("div",ht,[t("p",xt,s(e.task.notes),1)])])):n("",!0)]),e.task.lead?(l(),d("div",_t,[bt,t("div",kt,[t("div",ft,[yt,t("div",vt,[t("div",wt," 📋 Lead: "+s(e.task.lead.client_name),1),t("div",pt,[e.task.lead.county?(l(),d("span",Ct,s(e.task.lead.county.name),1)):n("",!0),e.task.lead.phone?(l(),d("span",Tt,"📞 "+s(e.task.lead.phone),1)):n("",!0),e.task.lead.email?(l(),d("span",St,"📧 "+s(e.task.lead.email),1)):n("",!0)])])])])])):n("",!0),e.task.activity_logs&&e.task.activity_logs.length>0?(l(),d("div",Dt,[Mt,t("div",jt,[(l(!0),d(f,null,V(e.task.activity_logs,i=>(l(),d("div",{key:i.id,class:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"},[t("div",$t,[t("div",Bt,[t("span",Lt,s(i.user.first_name.charAt(0)),1)])]),t("div",At,[t("div",Nt,s(i.user.first_name)+" "+s(i.user.last_name),1),t("div",Ot,s(i.description),1),t("div",Vt,s(g(i.created_at)),1)])]))),128))])])):n("",!0)]),t("div",zt,[t("div",Ft,[Ut,t("div",Et,[e.task.status!=="completed"?(l(),d("button",{key:0,onClick:a[0]||(a[0]=i=>M(e.task.id)),class:"w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700"}," ✅ Mark as Complete ")):n("",!0),r(v,{href:o.route("tasks.edit",e.task.id),class:"w-full"},{svg:m(()=>[Ht]),_:1},8,["href"]),r(v,{href:o.route("tasks.index"),class:"w-full"},{svg:m(()=>[It]),_:1},8,["href"])])]),t("div",Pt,[qt,t("div",Qt,[t("div",Rt,[Yt,t("div",null,[Zt,t("div",Gt,s(g(e.task.created_at)),1)])]),t("div",Jt,[t("div",{class:u(["w-2 h-2 rounded-full mr-3",{"bg-red-500":c(e.task.due_date),"bg-yellow-500":!c(e.task.due_date)}])},null,2),t("div",null,[Kt,t("div",{class:u({"text-red-500":c(e.task.due_date),"text-gray-700":!c(e.task.due_date)})},s(g(e.task.due_date)),3)])]),e.task.completed_at?(l(),d("div",Wt,[Xt,t("div",null,[te,t("div",ee,s(g(e.task.completed_at)),1)])])):n("",!0)])])])])]),r(F,{show:x.value,onClose:_},{default:m(()=>[t("div",se,[oe,t("div",ae,[r(U,{onClick:_},{default:m(()=>[h("Cancel")]),_:1}),r(E,{class:"w-40",onClick:j},{default:m(()=>[h(" Complete Task ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{me as default};
