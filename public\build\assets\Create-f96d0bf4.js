import{T,b as y,d as f,e as s,u as l,f as m,F as C,Z as $,h as o,y as A,l as v,s as U,E as D,j as N}from"./app-e21f56bc.js";import{_ as O,a as B}from"./AdminLayout-46709983.js";import{_ as n}from"./InputLabel-d2dad70b.js";import{_ as b,a as d}from"./TextInput-1ecc3ccf.js";import{_ as x}from"./TextArea-b0545758.js";import{_ as c}from"./SearchableDropdownNew-b8de8067.js";import{P as F}from"./PrimaryButton-3e573a38.js";import"./_plugin-vue_export-helper-c27b6911.js";const j={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},q={class:"flex justify-between items-center mb-6"},P=o("div",null,[o("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create New Task"),o("p",{class:"text-sm text-gray-600 mt-1"},"Schedule a follow-up or reminder")],-1),Q=["onSubmit"],E={class:"grid grid-cols-1 md:grid-cols-12 gap-6"},H={class:"md:col-span-3"},I={class:"relative mt-2"},L={class:"md:col-span-3"},M={class:"md:col-span-3"},J={class:"relative mt-2"},R={class:"md:col-span-3"},Z={class:"relative mt-2"},z={class:"md:col-span-6"},G={class:"md:col-span-6"},K={class:"md:col-span-4"},W={class:"md:col-span-4"},X={class:"relative mt-2"},Y={class:"bg-gray-50 p-4 rounded-lg"},ee=o("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Quick Actions:",-1),te={class:"grid grid-cols-2 md:grid-cols-4 gap-2"},se={class:"flex mt-6 items-center justify-between"},oe={class:"ml-auto flex items-center justify-end gap-x-6"},le=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),ae={key:0,class:"text-sm text-gray-600"},ge={__name:"Create",props:{users:Array,types:Array,priority:Array,leads:Array},setup(r){const g=r,e=T({title:"",description:"",type:"",priority:"medium",due_date:"",reminder_date:"",assigned_to:"",lead_id:g.lead?g.lead.id:null,notes:""}),u=new Date;u.setDate(u.getDate()+1),u.setHours(9,0,0,0),e.due_date=u.toISOString().slice(0,16);const _=new Date(u);_.setHours(8,0,0,0),e.reminder_date=_.toISOString().slice(0,16);const p=(i,t,a)=>{e.type=i,e.title=t,e.priority=a},h=(i,t)=>{e.lead_id=i},V=(i,t)=>{e.type=i},w=(i,t)=>{e.priority=i},k=(i,t)=>{e.assigned_to=i},S=()=>{e.post(route("tasks.store"),{preserveScroll:!0})};return(i,t)=>(y(),f(C,null,[s(l($),{title:"Tasks"}),s(O,null,{default:m(()=>[o("div",j,[o("div",q,[P,s(l(A),{href:i.route("tasks.index"),class:"px-4 py-2 bg-slate-50 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100"},{default:m(()=>[v(" ← Back to Tasks ")]),_:1},8,["href"])]),o("form",{onSubmit:U(S,["prevent"]),class:"space-y-6"},[o("div",E,[o("div",H,[s(n,{for:"lead_id",value:"Leads"}),o("div",I,[s(c,{options:r.leads,modelValue:l(e).lead_id,"onUpdate:modelValue":t[0]||(t[0]=a=>l(e).lead_id=a),onOnchange:h},null,8,["options","modelValue"])])]),o("div",L,[s(n,{for:"title",value:"Task Title *"}),s(b,{id:"title",type:"text",modelValue:l(e).title,"onUpdate:modelValue":t[1]||(t[1]=a=>l(e).title=a),class:"mt-1 block w-full",placeholder:"e.g., Call John about quotation follow-up",required:""},null,8,["modelValue"]),s(d,{message:l(e).errors.title},null,8,["message"])]),o("div",M,[s(n,{for:"type",value:"Task Type *"}),o("div",J,[s(c,{options:r.types,modelValue:l(e).type,"onUpdate:modelValue":t[2]||(t[2]=a=>l(e).type=a),onOnchange:V},null,8,["options","modelValue"])]),s(d,{message:l(e).errors.type},null,8,["message"])]),o("div",R,[s(n,{for:"priority",value:"Priority *"}),o("div",Z,[s(c,{options:r.priority,modelValue:l(e).priority,"onUpdate:modelValue":t[3]||(t[3]=a=>l(e).priority=a),onOnchange:w},null,8,["options","modelValue"])]),s(d,{message:l(e).errors.priority},null,8,["message"])]),o("div",z,[s(n,{for:"description",value:"Description"}),s(x,{id:"description",type:"text",rows:3,modelValue:l(e).description,"onUpdate:modelValue":t[4]||(t[4]=a=>l(e).description=a),autocomplete:"description",onChange:t[5]||(t[5]=a=>l(e).validate("description"))},null,8,["modelValue"]),s(d,{message:l(e).errors.description},null,8,["message"])]),o("div",G,[s(n,{for:"notes",value:"Additional Notes"}),s(x,{id:"notes",type:"text",rows:3,modelValue:l(e).notes,"onUpdate:modelValue":t[6]||(t[6]=a=>l(e).notes=a),placeholder:"Any additional notes or instructions..."},null,8,["modelValue"]),s(d,{message:l(e).errors.notes},null,8,["message"])]),o("div",K,[s(n,{for:"due_date",value:"Due Date & Time *"}),s(b,{id:"due_date",type:"datetime-local",modelValue:l(e).due_date,"onUpdate:modelValue":t[7]||(t[7]=a=>l(e).due_date=a),class:"mt-1 block w-full",required:""},null,8,["modelValue"]),s(d,{message:l(e).errors.due_date},null,8,["message"])]),o("div",W,[s(n,{for:"assigned_to",value:"Assign To *"}),o("div",X,[s(c,{options:r.users,modelValue:l(e).assigned_to,"onUpdate:modelValue":t[8]||(t[8]=a=>l(e).assigned_to=a),onOnchange:k},null,8,["options","modelValue"])]),s(d,{message:l(e).errors.assigned_to},null,8,["message"])])]),o("div",Y,[ee,o("div",te,[o("button",{type:"button",onClick:t[9]||(t[9]=a=>p("call","Call client","high")),class:"px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"}," 📞 Schedule Call "),o("button",{type:"button",onClick:t[10]||(t[10]=a=>p("follow_up","Follow up on quotation","medium")),class:"px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200"}," 💰 Quote Follow-up "),o("button",{type:"button",onClick:t[11]||(t[11]=a=>p("meeting","Schedule meeting","high")),class:"px-3 py-2 text-xs bg-purple-100 text-purple-800 rounded-md hover:bg-purple-200"}," 🤝 Schedule Meeting "),o("button",{type:"button",onClick:t[12]||(t[12]=a=>p("reminder","Follow up reminder","low")),class:"px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200"}," ⏰ Set Reminder ")])]),o("div",se,[o("div",oe,[s(B,{href:i.route("tasks.index")},{svg:m(()=>[le]),_:1},8,["href"]),s(F,{disabled:l(e).processing},{default:m(()=>[v("Save")]),_:1},8,["disabled"]),s(D,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:m(()=>[l(e).recentlySuccessful?(y(),f("p",ae,"Saved.")):N("",!0)]),_:1})])])],40,Q)])]),_:1})],64))}};export{ge as default};
