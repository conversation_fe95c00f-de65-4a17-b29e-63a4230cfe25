import{r as v,c as M,b as a,d as i,e as n,u as g,f as r,F as w,Z as oe,g as e,h as V,v as ae,E as ne,i as f,j as $,m as N,k as O,t as d,G as ie,y as re,n as de,O as j}from"./app-86b1b392.js";import{_ as ce,b as ue,a as B}from"./AdminLayout-ee551a2f.js";import{M as _e,_ as me}from"./Modal-fce2fc52.js";import{D as pe}from"./DangerButton-169eabaf.js";import{s as he,_ as ve,a as ge}from"./ArrowIcon-565ac9e7.js";import{_ as D}from"./SearchableDropdownNew-e6e71334.js";import{_ as k}from"./InputLabel-091b714a.js";/* empty css                                                              */import"./_plugin-vue_export-helper-c27b6911.js";const fe={class:"animate-top"},ye={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},xe=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Quotations")],-1),be={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},we={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},ke=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),Ce={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Ae={class:"flex justify-between mb-2"},Se={class:"flex"},Me=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Ve={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},$e={key:0,class:"sm:col-span-4"},Ne={class:"relative mt-2"},Oe={class:"sm:col-span-4"},De={class:"relative mt-2"},Le={class:"sm:col-span-4"},Qe={class:"relative mt-2"},je={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Be={class:"shadow rounded-lg"},Ee={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ue={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Pe={class:"border-b-2"},Te=["onClick"],ze={key:0},Fe={class:"px-4 py-2.5 min-w-28"},Ie={class:"px-4 py-2.5 min-w-28"},Re={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},We={class:"px-4 py-2.5"},Ge={class:"px-4 py-2.5"},Ye={class:"px-4 py-2.5"},He={class:"px-4 py-2.5"},Ke={key:0,class:"flex items-center space-x-2"},Ze=["onUpdate:modelValue","onChange"],Je=["value"],Xe=["onClick"],qe={key:1,class:"flex items-center space-x-2"},et=["onClick"],tt={key:0,class:"px-4 py-2.5"},st={class:"items-center px-4 py-2.5"},lt={class:"flex items-center justify-start gap-4"},ot=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),at=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),nt=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),it=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),rt=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),dt=["onClick"],ct=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),ut=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),_t=[ct,ut],mt=["onClick"],pt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),ht=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),vt=[pt,ht],gt={key:1},ft=e("tr",{class:"bg-white"},[e("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),yt=[ft],xt={class:"p-6"},bt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this quotation? ",-1),wt={class:"mt-6 flex justify-end"},Lt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","isAdmin"],setup(c){const u=c,{form:C,search:x,sort:E,fetchData:kt,sortKey:U,sortDirection:P}=he("quotations.index"),A=v(!1),L=v(null),T=[{id:"pending",name:"Pending"},{id:"completed",name:"Completed"}],z=M(()=>[{id:"",name:"All Agents"},...u.agents]),F=M(()=>[{id:"",name:"All Counties"},...u.counties]),I=M(()=>[{id:"",name:"All Status"},...u.statusOptions]),R=[{field:"quotation_number",label:"QUOTATION NO",sortable:!0,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!1,visible:!0},{field:"dimensions",label:"DIMENSIONS",sortable:!0,visible:!0},{field:"qty",label:"QTY",sortable:!1,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:u.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],W=s=>{L.value=s,A.value=!0},S=()=>{A.value=!1},G=()=>{C.delete(route("quotations.destroy",{quotation:L.value}),{onSuccess:()=>S()})},Y=s=>({pending:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",_=v(u.agent_id||""),m=v(u.county_id||""),p=v(u.status||""),y=v(""),h=v({}),H=(s,l)=>{_.value=s,b(y.value,_.value,m.value,p.value)},K=(s,l)=>{m.value=s,b(y.value,_.value,m.value,p.value)},Z=(s,l)=>{p.value=s,b(y.value,_.value,m.value,p.value)},b=(s,l,t,o)=>{y.value=s;const Q=l===""?null:l,se=t===""?null:t,le=o===""?null:o;C.get(route("quotations.index",{search:s,agent_id:Q,county_id:se,status:le}),{preserveState:!0})},J=(s,l)=>{h.value[s]=l},X=s=>{delete h.value[s]},q=(s,l)=>{j.post(route("quotations.update-status",s),{status:l},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete h.value[s];const o=new URLSearchParams(window.location.search).get("page")||1;j.get(route("quotations.index"),{search:y.value,agent_id:_.value===""?null:_.value,county_id:m.value===""?null:m.value,status:p.value===""?null:p.value,page:o},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})},ee=s=>{window.open(route("quotations.pdf",s),"_blank")};function te(s){return[{label:"1",qty:(s==null?void 0:s.qty_1)??null,price:(s==null?void 0:s.price_qty_1)??null},{label:"2",qty:(s==null?void 0:s.qty_2)??null,price:(s==null?void 0:s.price_qty_2)??null},{label:"3",qty:(s==null?void 0:s.qty_3)??null,price:(s==null?void 0:s.price_qty_3)??null},{label:"4",qty:(s==null?void 0:s.qty_4)??null,price:(s==null?void 0:s.price_qty_4)??null}].filter(t=>t.qty!=null).map(t=>t.price!=null?`Qty ${t.label}: ${t.qty} — $${t.price}`:`Qty ${t.label}: ${t.qty}`).join(", ")||"N/A"}return(s,l)=>(a(),i(w,null,[n(g(oe),{title:"Quotations"}),n(ce,null,{default:r(()=>[e("div",fe,[e("div",ye,[xe,e("div",be,[e("div",we,[ke,V(e("input",{type:"text","onUpdate:modelValue":l[0]||(l[0]=t=>ne(x)?x.value=t:null),onInput:l[1]||(l[1]=t=>b(g(x),_.value,m.value,p.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for quotations..."},null,544),[[ae,g(x)]])])])]),e("div",Ce,[e("div",Ae,[e("div",Se,[Me,n(k,{for:"filters",value:"Filters"})])]),e("div",Ve,[u.isAdmin?(a(),i("div",$e,[n(k,{for:"agent_filter",value:"Agents"}),e("div",Ne,[n(D,{options:z.value,modelValue:_.value,"onUpdate:modelValue":l[2]||(l[2]=t=>_.value=t),onOnchange:H},null,8,["options","modelValue"])])])):f("",!0),e("div",Oe,[n(k,{for:"county_filter",value:"Counties"}),e("div",De,[n(D,{options:F.value,modelValue:m.value,"onUpdate:modelValue":l[3]||(l[3]=t=>m.value=t),onOnchange:K},null,8,["options","modelValue"])])]),e("div",Le,[n(k,{for:"status_filter",value:"Status"}),e("div",Qe,[n(D,{options:I.value,modelValue:p.value,"onUpdate:modelValue":l[4]||(l[4]=t=>p.value=t),onOnchange:Z},null,8,["options","modelValue"])])])])]),e("div",je,[e("div",Be,[e("table",Ee,[e("thead",Ue,[e("tr",Pe,[(a(),i(w,null,$(R,(t,o)=>V(e("th",{key:o,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:Q=>g(E)(t.field,t.sortable)},[O(d(t.label)+" ",1),t.sortable?(a(),N(ge,{key:0,isSorted:g(U)===t.field,direction:g(P)},null,8,["isSorted","direction"])):f("",!0)],8,Te),[[ie,t.visible]])),64))])]),c.data.data&&c.data.data.length>0?(a(),i("tbody",ze,[(a(!0),i(w,null,$(c.data.data,t=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Fe,d(t.quotation_number),1),e("td",Ie,d(t.lead?t.lead.lead_number:"N/A"),1),e("td",Re,d(t.client_name),1),e("td",We,d(t.county?t.county.name:"N/A"),1),e("td",Ge,d(t.dimensions),1),e("td",Ye,d(te(t)),1),e("td",He,[h.value[t.id]!==void 0?(a(),i("div",Ke,[V(e("select",{"onUpdate:modelValue":o=>h.value[t.id]=o,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:o=>q(t.id,h.value[t.id])},[(a(),i(w,null,$(T,o=>e("option",{key:o.id,value:o.id},d(o.name),9,Je)),64))],40,Ze),[[re,h.value[t.id]]]),e("button",{onClick:o=>X(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,Xe)])):(a(),i("div",qe,[e("span",{class:de(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",Y(t.status)]),onClick:o=>J(t.id,t.status),title:"Click to edit status"},d(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,et)]))]),u.isAdmin?(a(),i("td",tt,d(t.creator?t.creator.first_name:"N/A"),1)):f("",!0),e("td",st,[e("div",lt,[n(ue,{align:"right",width:"48"},{trigger:r(()=>[ot]),content:r(()=>[n(B,{href:s.route("quotations.show",{quotation:t.id})},{svg:r(()=>[at]),text:r(()=>[nt]),_:2},1032,["href"]),c.permissions.canEditQuotation?(a(),N(B,{key:0,href:s.route("quotations.edit",{quotation:t.id})},{svg:r(()=>[it]),text:r(()=>[rt]),_:2},1032,["href"])):f("",!0),c.permissions.canDeleteQuotation?(a(),i("button",{key:1,type:"button",onClick:o=>W(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},_t,8,dt)):f("",!0),e("button",{type:"button",onClick:o=>ee(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},vt,8,mt)]),_:2},1024)])])]))),128))])):(a(),i("tbody",gt,yt))])])]),c.data.data&&c.data.data.length>0?(a(),N(ve,{key:0,class:"mt-6",links:c.data.links},null,8,["links"])):f("",!0)]),n(_e,{show:A.value,onClose:S},{default:r(()=>[e("div",xt,[bt,e("div",wt,[n(me,{onClick:S},{default:r(()=>[O("Cancel")]),_:1}),n(pe,{class:"ml-3",onClick:G,disabled:g(C).processing},{default:r(()=>[O(" Delete Quotation ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Lt as default};
