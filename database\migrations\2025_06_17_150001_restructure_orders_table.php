<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Remove duplicate fields that exist in leads table
            $table->dropColumn([
                'client_name',
                'county_id',
                'dimensions',
                'open_size',
                'box_style',
                'stock',
                'lamination',
                'printing',
                'add_ons'
            ]);
            
            // Add lead_id reference
            $table->foreignId('lead_id')->after('quotation_id')->constrained('leads');
        });
    }

    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add back the removed columns
            $table->string('client_name');
            $table->foreignId('county_id')->constrained('counties');
            $table->string('dimensions');
            $table->string('open_size');
            $table->string('box_style');
            $table->string('stock');
            $table->string('lamination');
            $table->string('printing');
            $table->string('add_ons')->nullable();
            
            // Remove lead_id
            $table->dropForeign(['lead_id']);
            $table->dropColumn('lead_id');
        });
    }
};
