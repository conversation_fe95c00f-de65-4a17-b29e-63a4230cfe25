<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Quotation extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected $table = 'quotations';

    protected static $logName = 'Quotation';

    public function getLogDescription(string $event): string
    {
        return "Quotation <strong>{$this->quotation_number}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'quotation_number', 'lead_id', 'qty_1', 'qty_2', 'qty_3', 'qty_4',
        'price_qty_1', 'price_qty_2', 'price_qty_3', 'price_qty_4',
        'notes', 'status', 'valid_until', 'created_by', 'updated_by'
    ];

    protected $fillable = [
        'quotation_number', 'lead_id', 'qty_1', 'qty_2', 'qty_3', 'qty_4',
        'price_qty_1', 'price_qty_2', 'price_qty_3', 'price_qty_4',
        'notes', 'status', 'valid_until',
        'created_by', 'updated_by', 'created_at', 'updated_at'
    ];

    protected $casts = [
        'valid_until' => 'date',
        'price_qty_1' => 'decimal:2',
        'price_qty_2' => 'decimal:2',
        'price_qty_3' => 'decimal:2',
        'price_qty_4' => 'decimal:2',
    ];

    public function lead()
    {
        return $this->belongsTo(Lead::class);
    }

    // Access county through lead relationship
    public function county()
    {
        return $this->hasOneThrough(County::class, Lead::class, 'id', 'id', 'lead_id', 'county_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id','lead_id')
            ->where('entity_type', 'lead');
    }

    // Calculate total amount based on quantities and prices
    public function calculateTotal()
    {
        $total = 0;

        // Only include quantities that have both qty and price values
        if (!empty($this->qty_1) && !empty($this->price_qty_1)) {
            $total += floatval($this->qty_1) * floatval($this->price_qty_1);
        }
        if (!empty($this->qty_2) && !empty($this->price_qty_2)) {
            $total += floatval($this->qty_2) * floatval($this->price_qty_2);
        }
        if (!empty($this->qty_3) && !empty($this->price_qty_3)) {
            $total += floatval($this->qty_3) * floatval($this->price_qty_3);
        }
        if (!empty($this->qty_4) && !empty($this->price_qty_4)) {
            $total += floatval($this->qty_4) * floatval($this->price_qty_4);
        }

        return $total;
    }

    // Auto-update total when saving
    protected static function boot()
    {
        parent::boot();

        // static::saving(function ($quotation) {
        //     $quotation->total_amount = $quotation->calculateTotal();
        // });
    }
}
