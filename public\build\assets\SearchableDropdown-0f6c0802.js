import{r as d,o as B,b as p,d as m,w as C,a as z,h as L,v as D,g as u,L as w,F as E,j as I,n as x,t as S,i as M}from"./app-631e032c.js";const A=["value"],J={__name:"TextArea",props:{modelValue:{type:String}},emits:["update:modelValue"],setup(g,{expose:f}){const o=d(null);return B(()=>{o.value.hasAttribute("autofocus")&&o.value.focus()}),f({focus:()=>o.value.focus()}),(i,l)=>(p(),m("textarea",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:g.modelValue,onInput:l[0]||(l[0]=c=>i.$emit("update:modelValue",c.target.value)),ref_key:"textarea",ref:o},null,40,A))}},F={class:"relative"},K=["onKeydown"],N=u("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[u("path",{"fill-rule":"evenodd",d:"M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z","clip-rule":"evenodd"})],-1),U=[N],j=["onClick","onMouseenter"],R=u("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[u("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z","clip-rule":"evenodd"})],-1),q=[R],P={__name:"SearchableDropdown",props:["options","modelValue","editMode"],emits:["onchange"],setup(g,{emit:f}){const o=g,i=d(o.options),l=d(""),c=d(!1),n=d(-1),r=d(null),b=()=>{const e=new RegExp(l.value,"i");i.value=o.options.filter(t=>e.test(t.name))},y=(e,t)=>{l.value=e,c.value=!1,f("onchange",t,e)},_=()=>{o.editMode||(c.value=!0)},H=e=>{n.value=e,T(e)},k=e=>{const t=i.value.length;e==="down"?n.value=(n.value+1)%t:e==="up"&&(n.value=(n.value-1+t)%t),T(n.value)},$=()=>{const e=i.value[n.value];e&&y(e.name,e.id)},T=e=>{if(!r.value)return;const t=r.value.children[e];if(t){const s=r.value.offsetHeight,a=t.offsetHeight,v=r.value.scrollTop,h=t.offsetTop,O=h+a;h<v?r.value.scrollTop=h:O>v+s&&(r.value.scrollTop=O-s)}};C(()=>o.options,()=>{b()}),C(()=>o.modelValue,e=>{e===""&&(l.value="")}),B(()=>{const e=o.options.find(t=>t.id===o.modelValue);e?l.value=e.name:l.value="",document.addEventListener("click",V)}),z(()=>{document.removeEventListener("click",V)});const V=e=>{e.target.closest(".relative")||(c.value=!1)};return(e,t)=>(p(),m("div",F,[L(u("input",{id:"combobox",type:"text",placeholder:"Search...",role:"combobox","onUpdate:modelValue":t[0]||(t[0]=s=>l.value=s),onInput:b,onFocus:_,autocomplete:"off",class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-7 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",onKeydown:[t[1]||(t[1]=w(s=>k("down"),["down"])),t[2]||(t[2]=w(s=>k("up"),["up"])),w($,["enter"])]},null,40,K),[[D,l.value]]),u("button",{type:"button",class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none",onClick:_},U),c.value&&i.value.length?(p(),m("ul",{key:0,class:"absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm cursor-pointer",id:"options",role:"listbox","aria-labelledby":"combobox",ref_key:"dropdown",ref:r},[(p(!0),m(E,null,I(i.value,(s,a)=>(p(),m("li",{class:x(["relative cursor-default select-none py-2 pl-3 pr-9 cursor-pointer",{"text-white bg-indigo-600":n.value===a,"text-gray-900":n.value!==a}]),key:a,onClick:v=>y(s.name,s.id),onMouseenter:v=>H(a),tabindex:"-1",role:"option"},[u("span",{class:x(["block truncate",{"font-semibold":s.name===l.value}])},S(s.name),3),s.name===l.value?(p(),m("span",{key:0,class:x(["absolute inset-y-0 right-0 flex items-center pr-4",{"text-white":n.value===a,"text-indigo-600":n.value!==a}])},q,2)):M("",!0)],42,j))),128))],512)):M("",!0)]))}};export{P as _,J as a};
