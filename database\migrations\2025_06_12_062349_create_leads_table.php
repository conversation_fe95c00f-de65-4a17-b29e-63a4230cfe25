<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->string('lead_number')->unique();
            $table->string('client_name');
            $table->foreignId('county_id')->constrained('counties');
            $table->string('dimensions');
            $table->string('open_size');
            $table->string('box_style');
            $table->string('stock');
            $table->string('lamination');
            $table->string('printing');
            $table->string('add_ons')->nullable();
            $table->integer('qty_1');
            $table->integer('qty_2')->nullable();
            $table->integer('qty_3')->nullable();
            $table->integer('qty_4')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['new', 'contacted', 'quotation', 'negotiation', 'won', 'lost'])->default('new');
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('leads');
    }
};
