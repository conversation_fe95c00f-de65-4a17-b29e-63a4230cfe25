import{r as c,b as a,d as i,g as n,F as v,j as y,t as p,i as u}from"./app-be4dd2ad.js";const h={class:"col-span-full"},_={class:"mt-2"},x=["id","name"],F={class:"flex flex-col gap-x-2"},k={key:1,class:"text-sm text-red-500"},w={__name:"MultipleFileUpload",props:{inputId:String,inputName:String},emits:["files"],setup(d,{emit:m}){const s=c([]),l=c(""),f=r=>{const t=r.target.files,e=["application/pdf","image/jpeg","image/jpg"],g=Array.from(t).filter(o=>e.includes(o.type));Array.from(t).filter(o=>!e.includes(o.type)).length>0?l.value="Only PDF, JPG, and JPEG files are allowed.":l.value="",s.value=g,m("files",s.value)};return(r,t)=>(a(),i("div",h,[n("div",_,[n("input",{type:"file",id:d.inputId,class:"hidden",ref:"fileInput",onChange:f,name:d.inputName,multiple:""},null,40,x),n("div",F,[n("label",{for:"photo",onClick:t[0]||(t[0]=e=>r.$refs.fileInput.click()),class:"rounded-md bg-white w-20 px-4 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 cursor-pointer hover:bg-gray-50"}," Upload "),s.value.length?(a(!0),i(v,{key:0},y(s.value,e=>(a(),i("span",{key:e.name,class:"text-sm text-gray-900"},p(e.name),1))),128)):u("",!0),l.value?(a(),i("span",k,p(l.value),1)):u("",!0)])])]))}};export{w as _};
