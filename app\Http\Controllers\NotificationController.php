<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        $query = Notification::with(['notifiable'])
            ->where('user_id', auth()->id())
            ->when($request->type, fn($q) => $q->where('type', $request->type))
            ->when($request->unread_only, fn($q) => $q->unread());

        $notifications = $query->orderBy('created_at', 'desc')->paginate(20);

        $stats = [
            'total' => Notification::where('user_id', auth()->id())->count(),
            'unread' => Notification::where('user_id', auth()->id())->unread()->count(),
            'high_priority' => Notification::where('user_id', auth()->id())
                ->whereIn('priority', ['high', 'urgent'])->unread()->count(),
        ];

        return Inertia::render('Notifications/Index', compact('notifications', 'stats'));
    }

    public function markAsRead(Notification $notification)
    {
        // Check authorization
        if ($notification->user_id !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $notification->markAsRead();
        return response()->json(['message' => 'Notification marked as read']);
    }

    public function markAllAsRead()
    {
        Notification::where('user_id', auth()->id())
            ->unread()
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return response()->json(['message' => 'All notifications marked as read']);
    }

    public function getUnreadCount()
    {
        $count = Notification::where('user_id', auth()->id())->unread()->count();
        return response()->json(['count' => $count]);
    }

    public function getRecent()
    {
        $notifications = Notification::with(['notifiable'])
            ->where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json($notifications);
    }

    public function destroy(Notification $notification)
    {
        // Check authorization
        if ($notification->user_id !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $notification->delete();
        return response()->json(['message' => 'Notification deleted']);
    }
}
