<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lead extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected $table = 'leads';

    protected static $logName = 'Lead';

    public function getLogDescription(string $event): string
    {
        return "Lead <strong>{$this->lead_number}</strong> has been {$event} by";
    }

    protected static $logAttributes = [
        'client_name',
        'lead_number', 'county_id', 'dimensions', 'open_size',
        'box_style', 'stock', 'lamination', 'printing', 'add_ons',
        'qty_1', 'qty_2', 'qty_3', 'qty_4', 'notes', 'status',
        'confirmed_qty_1', 'confirmed_qty_2', 'confirmed_qty_3', 'confirmed_qty_4', 'order_confirmed_at'
    ];

    protected $fillable = [
        'client_name',
        'lead_number', 'county_id', 'dimensions', 'open_size',
        'box_style', 'stock', 'lamination', 'printing', 'add_ons',
        'qty_1', 'qty_2', 'qty_3', 'qty_4', 'notes', 'status',
        'confirmed_qty_1', 'confirmed_qty_2', 'confirmed_qty_3', 'confirmed_qty_4', 'order_confirmed_at',
        'created_by', 'updated_by'
    ];

    protected $casts = [
        'order_confirmed_at' => 'datetime',
    ];

    public function county()
    {
        return $this->belongsTo(County::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function documents()
    {
        return $this->hasMany(Document::class, 'entity_id')
            ->where('entity_type', 'lead');
    }

    public function quotations()
    {
        return $this->hasMany(Quotation::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'taskable');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifiable');
    }

    public function activityLogs()
    {
        return $this->morphMany(ActivityLog::class, 'loggable');
    }
}
