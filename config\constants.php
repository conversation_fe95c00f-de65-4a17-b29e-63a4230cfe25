<?php

$defaultPath = public_path() . '/uploads/';
$viewPath = '/uploads/';

//$monthNameList = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

return [

    'perPage' => 20,

    'uploadFilePath' => [
        'companyDocument'       => ['default' => $defaultPath. 'companyprofile/',         'view' => $viewPath. 'companyprofile/'],
        'leadDocument'          => ['default' => $defaultPath. 'leads/',          'view' => $viewPath. 'leads/'],
    ],

    //'months' => array_map(fn($month) => ['id' => $month, 'name' => $month], $monthNameList),
    'months' => [
        [ "id"=> "January",     "name"=> "January"],
        [ "id"=> "February",    "name"=> "February"],
        [ "id"=> "March",       "name"=> "March"],
        [ "id"=> "April",       "name"=> "April"],
        [ "id"=> "May",         "name"=> "May"],
        [ "id"=> "June",        "name"=> "June"],
        [ "id"=> "July",        "name"=> "July"],
        [ "id"=> "August",      "name"=> "August"],
        [ "id"=> "September",   "name"=> "September"],
        [ "id"=> "October",     "name"=> "October"],
        [ "id"=> "November",    "name"=> "November"],
        [ "id"=> "December",    "name"=> "December"]
    ],

    'pageTypes' => [
        ['id' => 'portrait', 'name' => 'Portrait'],
        ['id' => 'landscape', 'name' => 'Landscape']
    ]

];
