import{b as o,q as i,f as s,B as r,u as n,y as l}from"./app-e1dc0e85.js";const a={__name:"CreateButton",props:{href:{type:String,required:!0},active:{type:<PERSON>olean}},setup(e){return(t,u)=>(o(),i(n(l),{href:e.href,class:"flex w-full justify-center rounded-md bg-indigo-600 px-6 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"},{default:s(()=>[r(t.$slots,"default")]),_:3},8,["href"]))}};export{a as _};
