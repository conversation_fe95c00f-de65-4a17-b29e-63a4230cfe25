import{r as v,c as V,b as l,d as i,e as n,u as p,f as r,F as k,Z as oe,h as e,i as z,v as ae,G as le,n as $,l as C,j as f,k as N,q as B,t as c,H as ne,z as ie,O as U}from"./app-e21f56bc.js";import{_ as re,b as de,a as T}from"./AdminLayout-46709983.js";import{_ as ce}from"./CreateButton-b93ed522.js";import{M as ue,_ as me}from"./Modal-04ee4cac.js";import{D as he}from"./DangerButton-12e50229.js";import{_ as ve}from"./Pagination-eae9c7b2.js";import{_ as E}from"./SearchableDropdownNew-b8de8067.js";import{_ as S}from"./InputLabel-d2dad70b.js";import{s as _e,_ as ge}from"./ArrowIcon-434878f3.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const pe={class:"animate-top"},fe={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},xe=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Leads")],-1),ye={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},be={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},we=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ke={class:"flex rounded-lg border border-gray-200 bg-white"},Ce=e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})],-1),Se=[Ce],Me=e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h3M3 20h18a1 1 0 001-1V5a1 1 0 00-1-1H3a1 1 0 00-1 1v14a1 1 0 001 1z"})],-1),Ae=[Me],Le={key:0,class:"sm:flex-none"},Ve={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},ze={class:"flex justify-between mb-2"},$e={class:"flex"},Ne=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Be={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ee={key:0,class:"sm:col-span-4"},Oe={class:"relative mt-2"},je={class:"sm:col-span-4"},De={class:"relative mt-2"},Ue={class:"sm:col-span-4"},Te={class:"relative mt-2"},Pe={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},He={class:"shadow rounded-lg"},Fe={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ie={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},We={class:"border-b-2"},qe=["onClick"],Re={key:0},Ge={class:"px-4 py-2.5 min-w-28"},Ke={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ye={class:"px-4 py-2.5"},Ze={class:"px-4 py-2.5 min-w-28"},Qe={class:"px-4 py-2.5 min-w-28"},Xe={class:"px-4 py-2.5 min-w-28"},Je={class:"px-4 py-2.5"},et={key:0,class:"flex items-center space-x-2 w-full"},tt=["onUpdate:modelValue","onChange"],st=["value"],ot=["onClick"],at={key:1,class:"flex items-center space-x-2"},lt=["onClick"],nt={key:0,class:"px-4 py-2.5"},it={class:"items-center px-4 py-2.5"},rt={class:"flex items-center justify-start gap-4"},dt=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),ct=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),ut=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),mt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),ht=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),vt=["onClick"],_t=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),gt=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),pt=[_t,gt],ft={key:1},xt=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),yt=[xt],bt={class:"p-6"},wt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this lead? ",-1),kt={class:"mt-6 flex justify-end"},jt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","status","isAdmin"],setup(d){const h=d,{form:M,search:y,sort:P,fetchData:Ct,sortKey:H,sortDirection:F}=_e("leads.index"),A=v(!1),O=v(null),b=v("card"),j=[{id:"new",name:"New"},{id:"contacted",name:"Contacted"},{id:"quotation",name:"Quotation"},{id:"negotiation",name:"Negotiation"},{id:"won",name:"Won"},{id:"lost",name:"Lost"}],I=V(()=>[{id:"",name:"All Agents"},...h.agents]),W=V(()=>[{id:"",name:"All Country"},...h.counties]),q=V(()=>[{id:"",name:"All Status"},...j]),R=[{field:"lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!1,visible:!0},{field:"open_size",label:"OPEN SIZE",sortable:!0,visible:!0},{field:"box_style",label:"BOX STYLE",sortable:!0,visible:!0},{field:"stock",label:"STOCK",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:h.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],G=o=>{O.value=o,A.value=!0},L=()=>{A.value=!1},K=()=>{M.delete(route("leads.destroy",{lead:O.value}),{onSuccess:()=>L()})},Y=o=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",u=v(h.agent_id||""),m=v(h.county_id||""),_=v(h.status||""),x=v(""),g=v({}),Z=(o,s)=>{u.value=o,w(x.value,u.value,m.value,_.value)},Q=(o,s)=>{m.value=o,w(x.value,u.value,m.value,_.value)},X=(o,s)=>{_.value=o,w(x.value,u.value,m.value,_.value)},w=(o,s,t,a)=>{x.value=o,console.log("agentId",s),console.log("countyId",t),console.log("status",a);const D=s===""?null:s,se=t===""?null:t;M.get(route("leads.index",{search:o,agent_id:D,county_id:se,status:a}),{preserveState:!0})},J=(o,s)=>{g.value[o]=s},ee=o=>{delete g.value[o]},te=(o,s)=>{U.post(route("leads.update-status",o),{status:s},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete g.value[o];const a=new URLSearchParams(window.location.search).get("page")||1;U.get(route("leads.index"),{search:x.value,agent_id:u.value===""?null:u.value,county_id:m.value===""?null:m.value,page:a},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})};return(o,s)=>(l(),i(k,null,[n(p(oe),{title:"Leads"}),n(re,null,{default:r(()=>[e("div",pe,[e("div",fe,[xe,e("div",ye,[e("div",be,[we,z(e("input",{type:"text","onUpdate:modelValue":s[0]||(s[0]=t=>le(y)?y.value=t:null),onInput:s[1]||(s[1]=t=>w(p(y),u.value,m.value,_.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for leads..."},null,544),[[ae,p(y)]])]),e("div",ke,[e("button",{onClick:s[2]||(s[2]=t=>b.value="card"),class:$(["px-3 py-2 text-sm font-medium rounded-l-lg",b.value==="card"?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-50"])},Se,2),e("button",{onClick:s[3]||(s[3]=t=>b.value="table"),class:$(["px-3 py-2 text-sm font-medium rounded-r-lg",b.value==="table"?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-50"])},Ae,2)]),d.permissions.canCreateLead?(l(),i("div",Le,[n(ce,{href:o.route("leads.create")},{default:r(()=>[C(" Add Lead ")]),_:1},8,["href"])])):f("",!0)])]),e("div",Ve,[e("div",ze,[e("div",$e,[Ne,n(S,{for:"customer_id",value:"Filters"})])]),e("div",Be,[h.isAdmin?(l(),i("div",Ee,[n(S,{for:"agent_filter",value:"Agents"}),e("div",Oe,[n(E,{options:I.value,modelValue:u.value,"onUpdate:modelValue":s[4]||(s[4]=t=>u.value=t),onOnchange:Z},null,8,["options","modelValue"])])])):f("",!0),e("div",je,[n(S,{for:"county_filter",value:"Country"}),e("div",De,[n(E,{options:W.value,modelValue:m.value,"onUpdate:modelValue":s[5]||(s[5]=t=>m.value=t),onOnchange:Q},null,8,["options","modelValue"])])]),e("div",Ue,[n(S,{for:"county_filter",value:"Status"}),e("div",Te,[n(E,{options:q.value,modelValue:_.value,"onUpdate:modelValue":s[6]||(s[6]=t=>_.value=t),onOnchange:X},null,8,["options","modelValue"])])])])]),e("div",Pe,[e("div",He,[e("table",Fe,[e("thead",Ie,[e("tr",We,[(l(),i(k,null,N(R,(t,a)=>z(e("th",{key:a,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:D=>p(P)(t.field,t.sortable)},[C(c(t.label)+" ",1),t.sortable?(l(),B(ge,{key:0,isSorted:p(H)===t.field,direction:p(F)},null,8,["isSorted","direction"])):f("",!0)],8,qe),[[ne,t.visible]])),64))])]),d.data.data&&d.data.data.length>0?(l(),i("tbody",Re,[(l(!0),i(k,null,N(d.data.data,t=>(l(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ge,c(t.lead_number),1),e("td",Ke,c(t.client_name),1),e("td",Ye,c(t.county?t.county.name:"N/A"),1),e("td",Ze,c(t.open_size),1),e("td",Qe,c(t.box_style),1),e("td",Xe,c(t.stock),1),e("td",Je,[g.value[t.id]!==void 0?(l(),i("div",et,[z(e("select",{"onUpdate:modelValue":a=>g.value[t.id]=a,class:"text-sm border-gray-300 rounded px-2 py-1",onChange:a=>te(t.id,g.value[t.id])},[(l(),i(k,null,N(j,a=>e("option",{class:"text-sm text-gray-900 text-bold",key:a.id,value:a.id},c(a.name),9,st)),64))],40,tt),[[ie,g.value[t.id]]]),e("button",{onClick:a=>ee(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,ot)])):(l(),i("div",at,[e("span",{class:$(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",Y(t.status)]),onClick:a=>J(t.id,t.status),title:"Click to edit status"},c(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,lt)]))]),h.isAdmin?(l(),i("td",nt,c(t.creator?t.creator.first_name:"N/A"),1)):f("",!0),e("td",it,[e("div",rt,[n(de,{align:"right",width:"48"},{trigger:r(()=>[dt]),content:r(()=>[n(T,{href:o.route("leads.show",{id:t.id})},{svg:r(()=>[ct]),text:r(()=>[ut]),_:2},1032,["href"]),d.permissions.canEditLead?(l(),B(T,{key:0,href:o.route("leads.edit",{id:t.id})},{svg:r(()=>[mt]),text:r(()=>[ht]),_:2},1032,["href"])):f("",!0),d.permissions.canDeleteLead?(l(),i("button",{key:1,type:"button",onClick:a=>G(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},pt,8,vt)):f("",!0)]),_:2},1024)])])]))),128))])):(l(),i("tbody",ft,yt))])])]),d.data.data&&d.data.data.length>0?(l(),B(ve,{key:0,class:"mt-6",links:d.data.links},null,8,["links"])):f("",!0)]),n(ue,{show:A.value,onClose:L},{default:r(()=>[e("div",bt,[wt,e("div",kt,[n(me,{onClick:L},{default:r(()=>[C("Cancel")]),_:1}),n(he,{class:"ml-3",onClick:K,disabled:p(M).processing},{default:r(()=>[C(" Delete Lead ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{jt as default};
