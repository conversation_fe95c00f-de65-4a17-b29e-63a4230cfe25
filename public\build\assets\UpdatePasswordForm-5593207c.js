import{r as m,T as _,b as p,d as c,g as a,e,u as r,f as w,D as g,q as y,k as x,i as v}from"./app-b86547fe.js";import{_ as n,a as d}from"./TextInput-b0990503.js";import{_ as l}from"./InputLabel-7201704d.js";import{P as V}from"./PrimaryButton-14b6c0ae.js";import"./_plugin-vue_export-helper-c27b6911.js";const b=a("header",null,[a("h2",{class:"text-lg font-medium text-gray-900"},"Update Password"),a("p",{class:"text-sm text-gray-500"}," Ensure your account is using a long, random password to stay secure. ")],-1),P=["onSubmit"],h={class:"flex items-center gap-4"},k={key:0,class:"text-sm text-gray-600"},T={__name:"UpdatePasswordForm",setup(S){const u=m(null),i=m(null),s=_({current_password:"",password:"",password_confirmation:""}),f=()=>{s.put(route("password.update"),{preserveScroll:!0,onSuccess:()=>s.reset(),onError:()=>{s.errors.password&&(s.reset("password","password_confirmation"),u.value.focus()),s.errors.current_password&&(s.reset("current_password"),i.value.focus())}})};return(N,o)=>(p(),c("section",null,[b,a("form",{onSubmit:y(f,["prevent"]),class:"mt-6 space-y-4"},[a("div",null,[e(l,{for:"current_password",value:"Current Password"}),e(n,{id:"current_password",ref_key:"currentPasswordInput",ref:i,modelValue:r(s).current_password,"onUpdate:modelValue":o[0]||(o[0]=t=>r(s).current_password=t),type:"password",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",autocomplete:"current-password"},null,8,["modelValue"]),e(d,{message:r(s).errors.current_password,class:"mt-2"},null,8,["message"])]),a("div",null,[e(l,{for:"password",value:"New Password"}),e(n,{id:"password",ref_key:"passwordInput",ref:u,modelValue:r(s).password,"onUpdate:modelValue":o[1]||(o[1]=t=>r(s).password=t),type:"password",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",autocomplete:"new-password"},null,8,["modelValue"]),e(d,{message:r(s).errors.password,class:"mt-2"},null,8,["message"])]),a("div",null,[e(l,{for:"password_confirmation",value:"Confirm Password"}),e(n,{id:"password_confirmation",modelValue:r(s).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=t=>r(s).password_confirmation=t),type:"password",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",autocomplete:"new-password"},null,8,["modelValue"]),e(d,{message:r(s).errors.password_confirmation,class:"mt-2"},null,8,["message"])]),a("div",h,[e(V,{disabled:r(s).processing},{default:w(()=>[x("Save")]),_:1},8,["disabled"]),e(g,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:w(()=>[r(s).recentlySuccessful?(p(),c("p",k,"Saved.")):v("",!0)]),_:1})])],40,P)]))}};export{T as default};
