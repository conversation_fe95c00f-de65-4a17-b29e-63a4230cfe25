<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            
            // Activity details
            $table->string('action'); // 'call_made', 'email_sent', 'meeting_held', etc.
            $table->text('description');
            
            // User who performed the action
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // Related entity (polymorphic)
            $table->morphs('loggable'); // loggable_type, loggable_id
            
            // Activity metadata
            $table->json('metadata')->nullable(); // Call duration, outcome, etc.
            $table->enum('outcome', ['positive', 'neutral', 'negative', 'no_response'])->nullable();
            
            // Related task (if activity was from a task)
            $table->foreignId('task_id')->nullable()->constrained()->onDelete('set null');
            
            $table->timestamps();
            
            // Indexes
            $table->index(['loggable_type', 'loggable_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['task_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('activity_logs');
    }
};
