<template>
    <Head title="Task Details" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">{{ task.title }}</h2>
                    <p class="text-sm text-gray-600 mt-1">Task Details</p>
                </div>
                <div class="flex space-x-2">
                    <Link :href="route('tasks.edit', task.id)" 
                          class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                        ✏️ Edit Task
                    </Link>
                    <button v-if="task.status !== 'completed'" @click="completeTask"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                        ✅ Mark Complete
                    </button>
                    <Link :href="route('tasks.index')" 
                          class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700">
                        ← Back to Tasks
                    </Link>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Task Details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Task Info Card -->
                    <div class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Task Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Type</label>
                                <div class="mt-1">
                                    <span class="inline-flex px-3 py-1 text-sm rounded-full"
                                          :class="getTypeColor(task.type)">
                                        {{ formatType(task.type) }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Priority</label>
                                <div class="mt-1">
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                                          :class="getPriorityColor(task.priority)">
                                        {{ task.priority.toUpperCase() }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Status</label>
                                <div class="mt-1">
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                                          :class="getStatusColor(task.status)">
                                        {{ formatStatus(task.status) }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Assigned To</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    {{ task.assigned_to.first_name }} {{ task.assigned_to.last_name }}
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Due Date</label>
                                <div class="mt-1 text-sm" 
                                     :class="{ 'text-red-600 font-semibold': isOverdue(task.due_date), 'text-gray-900': !isOverdue(task.due_date) }">
                                    {{ formatDateTime(task.due_date) }}
                                    <span v-if="isOverdue(task.due_date)" class="ml-2 text-red-500">⚠️ Overdue</span>
                                </div>
                            </div>

                            <div v-if="task.reminder_date">
                                <label class="block text-sm font-medium text-gray-700">Reminder</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    {{ formatDateTime(task.reminder_date) }}
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Created By</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    {{ task.created_by.first_name }} {{ task.created_by.last_name }}
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Created</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    {{ formatDateTime(task.created_at) }}
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div v-if="task.description" class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ task.description }}</p>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div v-if="task.notes" class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ task.notes }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Related Entity -->
                    <div v-if="task.taskable" class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Entity</h3>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-blue-900">
                                        <span v-if="task.taskable.client_name">
                                            📋 Lead: {{ task.taskable.client_name }}
                                        </span>
                                        <span v-else-if="task.taskable.quotation_number">
                                            💰 Quotation: {{ task.taskable.quotation_number }}
                                        </span>
                                        <span v-else-if="task.taskable.order_number">
                                            📦 Order: {{ task.taskable.order_number }}
                                        </span>
                                    </div>
                                    <div class="text-sm text-blue-700">
                                        <span v-if="task.taskable.company_name">{{ task.taskable.company_name }}</span>
                                        <span v-if="task.taskable.phone">📞 {{ task.taskable.phone }}</span>
                                        <span v-if="task.taskable.email">📧 {{ task.taskable.email }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Log -->
                    <div v-if="task.activity_logs && task.activity_logs.length > 0" class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Activity History</h3>
                        <div class="space-y-4">
                            <div v-for="activity in task.activity_logs" :key="activity.id" 
                                 class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-xs">{{ activity.user.first_name.charAt(0) }}</span>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ activity.user.first_name }} {{ activity.user.last_name }}
                                    </div>
                                    <div class="text-sm text-gray-600">{{ activity.description }}</div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {{ formatDateTime(activity.created_at) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button v-if="task.status !== 'completed'" @click="completeTask"
                                    class="w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700">
                                ✅ Mark as Complete
                            </button>
                            <Link :href="route('tasks.edit', task.id)"
                                  class="w-full block text-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                                ✏️ Edit Task
                            </Link>
                            <button @click="createFollowUp"
                                    class="w-full px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-md hover:bg-yellow-700">
                                🔄 Create Follow-up
                            </button>
                        </div>
                    </div>

                    <!-- Task Timeline -->
                    <div class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
                        <div class="space-y-3">
                            <div class="flex items-center text-sm">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium">Created</div>
                                    <div class="text-gray-500">{{ formatDateTime(task.created_at) }}</div>
                                </div>
                            </div>
                            <div v-if="task.reminder_date" class="flex items-center text-sm">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium">Reminder</div>
                                    <div class="text-gray-500">{{ formatDateTime(task.reminder_date) }}</div>
                                </div>
                            </div>
                            <div class="flex items-center text-sm">
                                <div class="w-2 h-2 rounded-full mr-3"
                                     :class="{ 'bg-red-500': isOverdue(task.due_date), 'bg-green-500': !isOverdue(task.due_date) }"></div>
                                <div>
                                    <div class="font-medium">Due Date</div>
                                    <div :class="{ 'text-red-500': isOverdue(task.due_date), 'text-gray-500': !isOverdue(task.due_date) }">
                                        {{ formatDateTime(task.due_date) }}
                                    </div>
                                </div>
                            </div>
                            <div v-if="task.completed_at" class="flex items-center text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium">Completed</div>
                                    <div class="text-gray-500">{{ formatDateTime(task.completed_at) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'

const props = defineProps({
    task: Object
})

const completeTask = () => {
    if (confirm('Mark this task as completed?')) {
        router.post(route('tasks.complete', props.task.id), {}, {
            onSuccess: () => {
                router.reload()
            }
        })
    }
}

const createFollowUp = () => {
    router.get(route('tasks.create'), {
        type: props.task.taskable_type?.toLowerCase(),
        id: props.task.taskable_id,
        follow_up: true
    })
}

const formatDateTime = (date) => {
    return new Date(date).toLocaleString()
}

const formatType = (type) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const isOverdue = (dueDate) => {
    return new Date(dueDate) < new Date() && props.task.status !== 'completed'
}

const getTypeColor = (type) => {
    const colors = {
        call: 'bg-blue-100 text-blue-800',
        follow_up: 'bg-yellow-100 text-yellow-800',
        meeting: 'bg-purple-100 text-purple-800',
        email: 'bg-green-100 text-green-800',
        quote_follow_up: 'bg-orange-100 text-orange-800',
        order_follow_up: 'bg-red-100 text-red-800',
        general: 'bg-gray-100 text-gray-800',
        reminder: 'bg-pink-100 text-pink-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
}

const getPriorityColor = (priority) => {
    const colors = {
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
}

const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        in_progress: 'bg-blue-100 text-blue-800',
        completed: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}
</script>
