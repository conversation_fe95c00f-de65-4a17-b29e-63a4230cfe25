import{T as G,r as q,w as H,b as _,d as p,e as t,u as l,f as U,F as J,Z as K,g as n,t as b,k as L,i as f,q as W}from"./app-7a2cf3c5.js";import{_ as X,a as ee}from"./AdminLayout-61d8de5b.js";import{_ as r,a as i}from"./TextInput-e7255592.js";import{_ as a}from"./InputLabel-be4ef156.js";import{P as te}from"./PrimaryButton-8b39253a.js";import{_ as le}from"./TextArea-2182b4b1.js";import{_ as se}from"./SearchableDropdownNew-740d57c8.js";import{_ as x}from"./Checkbox-d5ed69f4.js";import"./_plugin-vue_export-helper-c27b6911.js";const oe={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ne={class:"text-2xl font-semibold leading-7 text-gray-900"},ae={key:0,class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},de={class:"text-sm text-blue-800"},ie=n("strong",null,"Converting from Lead:",-1),re=["onSubmit"],ue={class:"border-b border-gray-900/10 pb-12"},me={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},_e={class:"sm:col-span-4"},pe={class:"sm:col-span-4"},ce={class:"relative mt-2"},ye={class:"sm:col-span-4"},ge={class:"sm:col-span-4"},ve={class:"sm:col-span-4"},qe={class:"sm:col-span-4"},fe={class:"sm:col-span-4"},Ve={class:"sm:col-span-4"},be={class:"sm:col-span-4"},xe=n("div",{class:"sm:col-span-12 mt-6"},[n("h3",{class:"text-lg font-bold text-gray-900"},"Quantities and Pricing")],-1),ke={class:"sm:col-span-3"},Ue={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Qe={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Ce={class:"sm:col-span-3"},Te={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},we={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},he={class:"sm:col-span-3"},Fe={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},Se={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},$e={class:"sm:col-span-3"},Ie={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},ze={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Pe={class:"sm:col-span-12"},Ye={class:"bg-gray-50 p-4 rounded-lg"},Ae={class:"text-lg font-semibold text-gray-900"},Ee={class:"sm:col-span-12"},Ne={class:"flex mt-6 items-center justify-between"},Be={class:"ml-auto flex items-center justify-end gap-x-6"},De=n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Ke={__name:"Add",props:{counties:{type:Array,required:!0},lead:{type:Object,default:null}},setup(m){var T,w,h,F,S,$,I,z,P,Y,A,E,N,B,D,O,R,j;const d=m,e=G({client_name:((T=d.lead)==null?void 0:T.client_name)||"",county_id:((w=d.lead)==null?void 0:w.county_id)||"",lead_id:((h=d.lead)==null?void 0:h.id)||null,dimensions:((F=d.lead)==null?void 0:F.dimensions)||"",open_size:((S=d.lead)==null?void 0:S.open_size)||"",box_style:(($=d.lead)==null?void 0:$.box_style)||"",stock:((I=d.lead)==null?void 0:I.stock)||"",lamination:((z=d.lead)==null?void 0:z.lamination)||"",printing:((P=d.lead)==null?void 0:P.printing)||"",add_ons:((Y=d.lead)==null?void 0:Y.add_ons)||"",qty_1:((A=d.lead)==null?void 0:A.qty_1)||"",qty_2:((E=d.lead)==null?void 0:E.qty_2)||"",qty_3:((N=d.lead)==null?void 0:N.qty_3)||"",qty_4:((B=d.lead)==null?void 0:B.qty_4)||"",price_qty_1:"",price_qty_2:"",price_qty_3:"",price_qty_4:"",notes:((D=d.lead)==null?void 0:D.notes)||"",valid_until:""}),c=q(!0),y=q(!!((O=d.lead)!=null&&O.qty_2)),g=q(!!((R=d.lead)!=null&&R.qty_3)),v=q(!!((j=d.lead)!=null&&j.qty_4)),Q=q(0),M=()=>{e.post(route("quotations.store"),{preserveScroll:!0,onSuccess:()=>e.reset()})},Z=(u,s)=>{e.county_id=u},C=()=>{let u=0;c.value&&e.qty_1&&e.price_qty_1&&(u+=parseFloat(e.qty_1)*parseFloat(e.price_qty_1)),y.value&&e.qty_2&&e.price_qty_2&&(u+=parseFloat(e.qty_2)*parseFloat(e.price_qty_2)),g.value&&e.qty_3&&e.price_qty_3&&(u+=parseFloat(e.qty_3)*parseFloat(e.price_qty_3)),v.value&&e.qty_4&&e.price_qty_4&&(u+=parseFloat(e.qty_4)*parseFloat(e.price_qty_4)),Q.value=u},V=(u,s)=>{s||(e[`qty_${u}`]="",e[`price_qty_${u}`]=""),C()};H([()=>e.qty_1,()=>e.price_qty_1,()=>c.value,()=>e.qty_2,()=>e.price_qty_2,()=>y.value,()=>e.qty_3,()=>e.price_qty_3,()=>g.value,()=>e.qty_4,()=>e.price_qty_4,()=>v.value],C);const k=new Date;return k.setDate(k.getDate()+30),e.valid_until=k.toISOString().split("T")[0],(u,s)=>(_(),p(J,null,[t(l(K),{title:"Add Quotation"}),t(X,null,{default:U(()=>[n("div",oe,[n("h2",ne,b(m.lead?"Convert Lead to Quotation":"Add New Quotation"),1),m.lead?(_(),p("div",ae,[n("p",de,[ie,L(" "+b(m.lead.lead_number)+" - "+b(m.lead.client_name),1)])])):f("",!0),n("form",{onSubmit:W(M,["prevent"])},[n("div",ue,[n("div",me,[n("div",_e,[t(a,{for:"client_name",value:"Client Name *"}),t(r,{id:"client_name",type:"text",modelValue:l(e).client_name,"onUpdate:modelValue":s[0]||(s[0]=o=>l(e).client_name=o),required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.client_name},null,8,["message"])]),n("div",pe,[t(a,{for:"county_id",value:"Country *"}),n("div",ce,[t(se,{options:m.counties,modelValue:l(e).county_id,"onUpdate:modelValue":s[1]||(s[1]=o=>l(e).county_id=o),onOnchange:Z},null,8,["options","modelValue"])]),t(i,{message:l(e).errors.county_id},null,8,["message"])]),n("div",ye,[t(a,{for:"dimensions",value:"Dimensions *"}),t(r,{id:"dimensions",type:"text",modelValue:l(e).dimensions,"onUpdate:modelValue":s[2]||(s[2]=o=>l(e).dimensions=o),required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.dimensions},null,8,["message"])]),n("div",ge,[t(a,{for:"open_size",value:"Open Size *"}),t(r,{id:"open_size",type:"text",modelValue:l(e).open_size,"onUpdate:modelValue":s[3]||(s[3]=o=>l(e).open_size=o),required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.open_size},null,8,["message"])]),n("div",ve,[t(a,{for:"box_style",value:"Box Style *"}),t(r,{id:"box_style",type:"text",modelValue:l(e).box_style,"onUpdate:modelValue":s[4]||(s[4]=o=>l(e).box_style=o),required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.box_style},null,8,["message"])]),n("div",qe,[t(a,{for:"stock",value:"Stock *"}),t(r,{id:"stock",type:"text",modelValue:l(e).stock,"onUpdate:modelValue":s[5]||(s[5]=o=>l(e).stock=o),required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.stock},null,8,["message"])]),n("div",fe,[t(a,{for:"lamination",value:"Lamination *"}),t(r,{id:"lamination",type:"text",modelValue:l(e).lamination,"onUpdate:modelValue":s[6]||(s[6]=o=>l(e).lamination=o),required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.lamination},null,8,["message"])]),n("div",Ve,[t(a,{for:"printing",value:"Printing *"}),t(r,{id:"printing",type:"text",modelValue:l(e).printing,"onUpdate:modelValue":s[7]||(s[7]=o=>l(e).printing=o),required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.printing},null,8,["message"])]),n("div",be,[t(a,{for:"add_ons",value:"Add-ons"}),t(r,{id:"add_ons",type:"text",modelValue:l(e).add_ons,"onUpdate:modelValue":s[8]||(s[8]=o=>l(e).add_ons=o)},null,8,["modelValue"]),t(i,{message:l(e).errors.add_ons},null,8,["message"])]),xe,n("div",ke,[n("div",Ue,[t(x,{checked:c.value,"onUpdate:checked":[s[9]||(s[9]=o=>c.value=o),s[10]||(s[10]=o=>V(1,o))]},null,8,["checked"]),t(a,{value:"Include Quantity 1",class:"text-base font-medium text-blue-800"})]),c.value?(_(),p("div",Qe,[n("div",null,[t(a,{for:"qty_1",value:"QTY 1 *"}),t(r,{id:"qty_1",type:"number",modelValue:l(e).qty_1,"onUpdate:modelValue":s[11]||(s[11]=o=>l(e).qty_1=o),min:"1",required:""},null,8,["modelValue"]),t(i,{message:l(e).errors.qty_1},null,8,["message"])]),n("div",null,[t(a,{for:"price_qty_1",value:"PRICE QTY 1 *"}),t(r,{id:"price_qty_1",type:"number",step:"0.01",modelValue:l(e).price_qty_1,"onUpdate:modelValue":s[12]||(s[12]=o=>l(e).price_qty_1=o),min:"0"},null,8,["modelValue"]),t(i,{message:l(e).errors.price_qty_1},null,8,["message"])])])):f("",!0)]),n("div",Ce,[n("div",Te,[t(x,{checked:y.value,"onUpdate:checked":[s[13]||(s[13]=o=>y.value=o),s[14]||(s[14]=o=>V(2,o))]},null,8,["checked"]),t(a,{value:"Include Quantity 2",class:"text-base font-medium text-green-800"})]),y.value?(_(),p("div",we,[n("div",null,[t(a,{for:"qty_2",value:"QTY 2"}),t(r,{id:"qty_2",type:"number",modelValue:l(e).qty_2,"onUpdate:modelValue":s[15]||(s[15]=o=>l(e).qty_2=o),min:"1"},null,8,["modelValue"]),t(i,{message:l(e).errors.qty_2},null,8,["message"])]),n("div",null,[t(a,{for:"price_qty_2",value:"PRICE QTY 2"}),t(r,{id:"price_qty_2",type:"number",step:"0.01",modelValue:l(e).price_qty_2,"onUpdate:modelValue":s[16]||(s[16]=o=>l(e).price_qty_2=o),min:"0"},null,8,["modelValue"]),t(i,{message:l(e).errors.price_qty_2},null,8,["message"])])])):f("",!0)]),n("div",he,[n("div",Fe,[t(x,{checked:g.value,"onUpdate:checked":[s[17]||(s[17]=o=>g.value=o),s[18]||(s[18]=o=>V(3,o))]},null,8,["checked"]),t(a,{value:"Include Quantity 3",class:"text-base font-medium text-yellow-800"})]),g.value?(_(),p("div",Se,[n("div",null,[t(a,{for:"qty_3",value:"QTY 3"}),t(r,{id:"qty_3",type:"number",modelValue:l(e).qty_3,"onUpdate:modelValue":s[19]||(s[19]=o=>l(e).qty_3=o),min:"1"},null,8,["modelValue"]),t(i,{message:l(e).errors.qty_3},null,8,["message"])]),n("div",null,[t(a,{for:"price_qty_3",value:"PRICE QTY 3"}),t(r,{id:"price_qty_3",type:"number",step:"0.01",modelValue:l(e).price_qty_3,"onUpdate:modelValue":s[20]||(s[20]=o=>l(e).price_qty_3=o),min:"0"},null,8,["modelValue"]),t(i,{message:l(e).errors.price_qty_3},null,8,["message"])])])):f("",!0)]),n("div",$e,[n("div",Ie,[t(x,{checked:v.value,"onUpdate:checked":[s[21]||(s[21]=o=>v.value=o),s[22]||(s[22]=o=>V(4,o))]},null,8,["checked"]),t(a,{value:"Include Quantity 4",class:"text-base font-medium text-purple-800"})]),v.value?(_(),p("div",ze,[n("div",null,[t(a,{for:"qty_4",value:"QTY 4"}),t(r,{id:"qty_4",type:"number",modelValue:l(e).qty_4,"onUpdate:modelValue":s[23]||(s[23]=o=>l(e).qty_4=o),min:"1"},null,8,["modelValue"]),t(i,{message:l(e).errors.qty_4},null,8,["message"])]),n("div",null,[t(a,{for:"price_qty_4",value:"PRICE QTY 4"}),t(r,{id:"price_qty_4",type:"number",step:"0.01",modelValue:l(e).price_qty_4,"onUpdate:modelValue":s[24]||(s[24]=o=>l(e).price_qty_4=o),min:"0"},null,8,["modelValue"]),t(i,{message:l(e).errors.price_qty_4},null,8,["message"])])])):f("",!0)]),n("div",Pe,[n("div",Ye,[n("p",Ae," Estimated Total: $"+b(Q.value.toFixed(2)),1)])]),n("div",Ee,[t(a,{for:"notes",value:"Notes"}),t(le,{id:"notes",modelValue:l(e).notes,"onUpdate:modelValue":s[25]||(s[25]=o=>l(e).notes=o),rows:"4"},null,8,["modelValue"]),t(i,{message:l(e).errors.notes},null,8,["message"])])])]),n("div",Ne,[n("div",Be,[t(ee,{href:u.route("quotations.index")},{svg:U(()=>[De]),_:1},8,["href"]),t(te,{disabled:l(e).processing},{default:U(()=>[L(" Save ")]),_:1},8,["disabled"])])])],40,re)])]),_:1})],64))}};export{Ke as default};
