import{b as r,d as c,e as o,u as s,f as p,F as y,Z as b,g as l,q as V,m as d,i as n,h as x,v as w,n as k,k as $,E as C}from"./app-df1bcebc.js";import{_ as h,a as U}from"./AdminLayout-e6738fa9.js";import{_ as u,a as i}from"./TextInput-ff2a6c73.js";import{_ as m}from"./InputLabel-df28ac37.js";import{P as N}from"./PrimaryButton-2296ddc6.js";import{_ as S}from"./TextArea-65f0749e.js";import{_ as q}from"./SearchableDropdown-87aeea05.js";import{u as B}from"./index-1360da53.js";import"./_plugin-vue_export-helper-c27b6911.js";const A={class:"animate-top"},F={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},E=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New User",-1),P=["onSubmit"],T={class:"border-b border-gray-900/10 pb-12"},j={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},D={class:"sm:col-span-2"},L={class:"sm:col-span-2"},M={class:"sm:col-span-2"},R={class:"sm:col-span-3"},z={class:"sm:col-span-3"},O={class:"sm:col-span-3"},Z={class:"sm:col-span-3"},G={class:"relative mt-2"},H={class:"sm:col-span-6"},I={class:"flex mt-6 items-center justify-between"},J={class:"ml-auto flex items-center justify-end gap-x-6"},K=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Q={key:0,class:"text-sm text-gray-600"},re={__name:"Add",props:{roles:{type:Array}},setup(v){const e=B("post","/users",{role_id:"",first_name:"",last_name:"",email:"",contact_no:"",dob:"",address:"",password:""}),f=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),g=(_,a)=>{e.role_id=_};return(_,a)=>(r(),c(y,null,[o(s(b),{title:"Users"}),o(h,null,{default:p(()=>[l("div",A,[l("div",F,[E,l("form",{onSubmit:V(f,["prevent"]),class:""},[l("div",T,[l("div",j,[l("div",D,[o(m,{for:"first_name",value:"First Name *"}),o(u,{id:"first_name",type:"text",modelValue:s(e).first_name,"onUpdate:modelValue":a[0]||(a[0]=t=>s(e).first_name=t),required:"",onChange:a[1]||(a[1]=t=>s(e).validate("first_name"))},null,8,["modelValue"]),s(e).invalid("first_name")?(r(),d(i,{key:0,class:"",message:s(e).errors.first_name},null,8,["message"])):n("",!0)]),l("div",L,[o(m,{for:"last_name",value:"Last Name *"}),o(u,{id:"last_name",type:"text",modelValue:s(e).last_name,"onUpdate:modelValue":a[2]||(a[2]=t=>s(e).last_name=t),required:"",onChange:a[3]||(a[3]=t=>s(e).validate("last_name"))},null,8,["modelValue"]),s(e).invalid("last_name")?(r(),d(i,{key:0,class:"",message:s(e).errors.last_name},null,8,["message"])):n("",!0)]),l("div",M,[o(m,{for:"dob",value:"Date of Birth"}),x(l("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[4]||(a[4]=t=>s(e).dob=t),onChange:a[5]||(a[5]=t=>s(e).validate("dob"))},null,544),[[w,s(e).dob]]),s(e).invalid("dob")?(r(),d(i,{key:0,class:"",message:s(e).errors.dob},null,8,["message"])):n("",!0)]),l("div",R,[o(m,{for:"email",value:"Email *"}),o(u,{id:"email",type:"email",modelValue:s(e).email,"onUpdate:modelValue":a[6]||(a[6]=t=>s(e).email=t),required:"",onChange:a[7]||(a[7]=t=>s(e).validate("email"))},null,8,["modelValue"]),s(e).invalid("email")?(r(),d(i,{key:0,class:"",message:s(e).errors.email},null,8,["message"])):n("",!0)]),l("div",z,[o(m,{for:"password",value:"Password *"}),o(u,{id:"password",type:"password",modelValue:s(e).password,"onUpdate:modelValue":a[8]||(a[8]=t=>s(e).password=t),required:"",onChange:a[9]||(a[9]=t=>s(e).validate("password"))},null,8,["modelValue"]),s(e).invalid("password")?(r(),d(i,{key:0,class:"",message:s(e).errors.password},null,8,["message"])):n("",!0)]),l("div",O,[o(m,{for:"contact_no",value:"Contact No"}),o(u,{id:"contact_no",type:"text",numeric:!0,maxLength:"10",modelValue:s(e).contact_no,"onUpdate:modelValue":a[10]||(a[10]=t=>s(e).contact_no=t),onChange:a[11]||(a[11]=t=>s(e).validate("contact_no"))},null,8,["modelValue"]),s(e).invalid("contact_no")?(r(),d(i,{key:0,class:"",message:s(e).errors.contact_no},null,8,["message"])):n("",!0)]),l("div",Z,[o(m,{for:"role_id",value:"Role *"}),l("div",G,[o(q,{options:v.roles,modelValue:s(e).role_id,"onUpdate:modelValue":a[12]||(a[12]=t=>s(e).role_id=t),onOnchange:g,required:"",class:k({"error rounded-md":s(e).errors.company_id})},null,8,["options","modelValue","class"])]),s(e).invalid("role_id")?(r(),d(i,{key:0,class:"",message:s(e).errors.role_id},null,8,["message"])):n("",!0)]),l("div",H,[o(m,{for:"address",value:"Address"}),o(S,{id:"address",type:"text",rows:4,modelValue:s(e).address,"onUpdate:modelValue":a[13]||(a[13]=t=>s(e).address=t),onChange:a[14]||(a[14]=t=>s(e).validate("address"))},null,8,["modelValue"]),s(e).invalid("address")?(r(),d(i,{key:0,class:"",message:s(e).errors.address},null,8,["message"])):n("",!0)])])]),l("div",I,[l("div",J,[o(U,{href:_.route("users.index")},{svg:p(()=>[K]),_:1},8,["href"]),o(N,{disabled:s(e).processing},{default:p(()=>[$("Save")]),_:1},8,["disabled"]),o(C,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[s(e).recentlySuccessful?(r(),c("p",Q,"Saved.")):n("",!0)]),_:1})])])],40,P)])])]),_:1})],64))}};export{re as default};
