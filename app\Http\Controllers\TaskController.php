<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\Lead;
use App\Models\Quotation;
use App\Models\Order;
use App\Models\User;
use App\Models\ActivityLog;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class TaskController extends Controller
{
    public function index(Request $request)
    {
        $query = Task::with(['assignedTo', 'createdBy', 'taskable'])
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->type, fn($q) => $q->where('type', $request->type))
            ->when($request->priority, fn($q) => $q->where('priority', $request->priority))
            ->when($request->assigned_to, fn($q) => $q->where('assigned_to', $request->assigned_to));

        // Filter by user role
        if (!auth()->user()->hasRole('Admin')) {
            $query->where('assigned_to', auth()->id());
        }

        $tasks = $query->orderBy('due_date', 'asc')->paginate(15);

        // Get filter options
        $users = User::select('id', 'first_name', 'last_name')->get();
        $stats = [
            'total' => Task::count(),
            'pending' => Task::where('status', 'pending')->count(),
            'overdue' => Task::overdue()->count(),
            'due_today' => Task::dueToday()->count(),
        ];

        return Inertia::render('Tasks/Index', compact('tasks', 'users', 'stats'));
    }

    public function create(Request $request)
    {
        $users = User::select('id', 'first_name', 'last_name')->get();

        // Get related entity if specified
        $relatedEntity = null;
        if ($request->type && $request->id) {
            $relatedEntity = match($request->type) {
                'lead' => Lead::find($request->id),
                'quotation' => Quotation::find($request->id),
                'order' => Order::find($request->id),
                default => null
            };
        }

        return Inertia::render('Tasks/Create', compact('users', 'relatedEntity'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:call,follow_up,meeting,email,quote_follow_up,order_follow_up,general,reminder',
            'priority' => 'required|in:low,medium,high,urgent',
            'due_date' => 'required|date|after:now',
            'reminder_date' => 'nullable|date|before:due_date',
            'assigned_to' => 'required|exists:users,id',
            'taskable_type' => 'nullable|string',
            'taskable_id' => 'nullable|integer',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $task = Task::create([
                ...$validated,
                'created_by' => auth()->id(),
            ]);

            // Create notification for assigned user
            if ($task->assigned_to !== auth()->id()) {
                NotificationService::createTaskAssigned($task);
            }

            // Log activity
            if ($task->taskable) {
                ActivityLog::create([
                    'action' => 'task_created',
                    'description' => "Task '{$task->title}' created",
                    'user_id' => auth()->id(),
                    'loggable_type' => $task->taskable_type,
                    'loggable_id' => $task->taskable_id,
                    'task_id' => $task->id,
                ]);
            }

            DB::commit();
            return Redirect::route('tasks.index')->with('success', 'Task created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::back()->with('error', 'Failed to create task: ' . $e->getMessage());
        }
    }

    public function show(Task $task)
    {
        $task->load(['assignedTo', 'createdBy', 'taskable', 'activityLogs.user']);

        // Check authorization
        if (!auth()->user()->isAdmin() && $task->assigned_to !== auth()->id()) {
            return Redirect::route('tasks.index')->with('error', 'Unauthorized access');
        }

        return Inertia::render('Tasks/Show', compact('task'));
    }

    public function edit(Task $task)
    {
        // Check authorization
        if (!auth()->user()->isAdmin() && $task->assigned_to !== auth()->id()) {
            return Redirect::route('tasks.index')->with('error', 'Unauthorized access');
        }

        $users = User::select('id', 'first_name', 'last_name')->get();
        return Inertia::render('Tasks/Edit', compact('task', 'users'));
    }

    public function update(Request $request, Task $task)
    {
        // Check authorization
        if (!auth()->user()->isAdmin() && $task->assigned_to !== auth()->id()) {
            return Redirect::route('tasks.index')->with('error', 'Unauthorized access');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:call,follow_up,meeting,email,quote_follow_up,order_follow_up,general,reminder',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'due_date' => 'required|date',
            'reminder_date' => 'nullable|date|before:due_date',
            'assigned_to' => 'required|exists:users,id',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $oldStatus = $task->status;
            $task->update($validated);

            // If status changed to completed, set completed_at
            if ($validated['status'] === 'completed' && $oldStatus !== 'completed') {
                $task->update(['completed_at' => now()]);
            }

            // Log status change
            if ($oldStatus !== $validated['status']) {
                ActivityLog::logStatusChange($task->taskable ?: $task, auth()->id(), $oldStatus, $validated['status']);
            }

            DB::commit();
            return Redirect::route('tasks.show', $task)->with('success', 'Task updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', 'Failed to update task: ' . $e->getMessage());
        }
    }

    public function complete(Task $task)
    {
        // Check authorization
        if (!auth()->user()->isAdmin() && $task->assigned_to !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $task->markAsCompleted();

        // Log completion
        if ($task->taskable) {
            ActivityLog::create([
                'action' => 'task_completed',
                'description' => "Task '{$task->title}' completed",
                'user_id' => auth()->id(),
                'loggable_type' => $task->taskable_type,
                'loggable_id' => $task->taskable_id,
                'task_id' => $task->id,
                'outcome' => 'positive'
            ]);
        }

        return response()->json(['message' => 'Task completed successfully']);
    }

    public function destroy(Task $task)
    {
        // Only admin can delete tasks
        if (!auth()->user()->isAdmin()) {
            return Redirect::route('tasks.index')->with('error', 'Unauthorized access');
        }

        $task->delete();
        return Redirect::route('tasks.index')->with('success', 'Task deleted successfully');
    }

    public function dashboard()
    {
        $userId = auth()->id();
        $isAdmin = auth()->user()->hasRole('Admin');

        // Get tasks based on role
        $tasksQuery = $isAdmin ? Task::query() : Task::where('assigned_to', $userId);

        $stats = [
            'total_tasks' => $tasksQuery->count(),
            'pending_tasks' => (clone $tasksQuery)->where('status', 'pending')->count(),
            'overdue_tasks' => (clone $tasksQuery)->overdue()->count(),
            'due_today' => (clone $tasksQuery)->dueToday()->count(),
            'completed_this_week' => (clone $tasksQuery)->where('status', 'completed')
                ->whereBetween('completed_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
        ];

        // Recent tasks
        $recentTasks = (clone $tasksQuery)->with(['assignedTo', 'taskable'])
            ->orderBy('created_at', 'desc')->limit(5)->get();

        // Upcoming tasks
        $upcomingTasks = (clone $tasksQuery)->with(['assignedTo', 'taskable'])
            ->where('status', 'pending')
            ->orderBy('due_date', 'asc')->limit(5)->get();

        return Inertia::render('Tasks/Dashboard', compact('stats', 'recentTasks', 'upcomingTasks'));
    }
}
