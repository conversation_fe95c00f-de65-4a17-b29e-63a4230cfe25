import{r as h,c as V,b as l,d as i,e as n,u,f as c,F as v,Z as H,g as e,h as B,v as Q,E as X,k as y,i as p,j as S,m as A,t as d,y as J,n as ee,O as D}from"./app-631e032c.js";import{_ as te,b as se,a as oe}from"./AdminLayout-8a25d743.js";import{_ as ae}from"./CreateButton-7875a006.js";import{M as le,_ as ne}from"./Modal-ce0fa11f.js";import{D as ie}from"./DangerButton-1239c4db.js";import{s as re,_ as de,a as ce}from"./ArrowIcon-827a9444.js";import{_ as O}from"./SearchableDropdownNew-134fe703.js";import{_ as M}from"./InputLabel-1e9f0b7d.js";import"./_plugin-vue_export-helper-c27b6911.js";const ue={class:"animate-top"},me={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},_e=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Leads")],-1),ge={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},he={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},pe=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),fe={key:0,class:"sm:flex-none"},xe={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ve={class:"flex justify-between mb-2"},ye={class:"flex"},be=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),we={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ke={class:"sm:col-span-4"},Ce={class:"relative mt-2"},Le={class:"sm:col-span-4"},Se={class:"relative mt-2"},Ae={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Me={class:"shadow rounded-lg"},Ee={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ne={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},$e={class:"border-b-2"},Ve=["onClick"],Be={key:0},De={class:"px-4 py-2.5"},Oe={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Te={class:"px-4 py-2.5"},Ue={class:"px-4 py-2.5"},ze={class:"px-4 py-2.5"},je={class:"px-4 py-2.5"},Ie={class:"px-4 py-2.5"},Fe={key:0,class:"flex items-center space-x-2"},Pe=["onUpdate:modelValue","onChange"],Re=["value"],We=["onClick"],qe={key:1,class:"flex items-center space-x-2"},Ke=["onClick"],Ye=["onClick"],Ze={class:"px-4 py-2.5"},Ge={class:"items-center px-4 py-2.5"},He={class:"flex items-center justify-start gap-4"},Qe=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Xe=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Je=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),et=["onClick"],tt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),st=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),ot=[tt,st],at={key:1},lt=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),nt=[lt],it={class:"p-6"},rt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this lead? ",-1),dt={class:"mt-6 flex justify-end"},yt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id"],setup(r){const f=r,{form:b,search:x,sort:T,fetchData:ct,sortKey:U,sortDirection:z}=re("leads.index"),w=h(!1),E=h(null),j=[{id:"new",name:"New"},{id:"contacted",name:"Contacted"},{id:"quotation",name:"Quotation"},{id:"negotiation",name:"Negotiation"},{id:"won",name:"Won"},{id:"lost",name:"Lost"}],I=V(()=>[{id:"",name:"All Agents"},...f.agents]),F=V(()=>[{id:"",name:"All Counties"},...f.counties]),P=[{field:"lead_number",label:"LEAD NUMBER",sortable:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0},{field:"county.name",label:"COUNTRY",sortable:!1},{field:"open_size",label:"OPEN SIZE",sortable:!0},{field:"box_style",label:"BOX STYLE",sortable:!0},{field:"stock",label:"STOCK",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"creator.first_name",label:"AGENT",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],R=s=>{E.value=s,w.value=!0},k=()=>{w.value=!1},W=()=>{b.delete(route("leads.destroy",{lead:E.value}),{onSuccess:()=>k()})},q=s=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",m=h(f.agent_id||""),_=h(f.county_id||""),C=h(""),g=h({}),K=(s,o)=>{m.value=s,L(C.value,m.value,_.value)},Y=(s,o)=>{_.value=s,L(C.value,m.value,_.value)},L=(s,o,t)=>{C.value=s;const a=o===""?null:o,$=t===""?null:t;b.get(route("leads.index",{search:s,agent_id:a,county_id:$}),{preserveState:!0})},N=(s,o)=>{g.value[s]=o},Z=s=>{delete g.value[s]},G=(s,o)=>{D.post(route("leads.update-status",s),{status:o},{preserveScroll:!0,onSuccess:()=>{D.reload({only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update number. Please try again.")}})};return(s,o)=>(l(),i(v,null,[n(u(H),{title:"Leads"}),n(te,null,{default:c(()=>[e("div",ue,[e("div",me,[_e,e("div",ge,[e("div",he,[pe,B(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>X(x)?x.value=t:null),onInput:o[1]||(o[1]=t=>L(u(x),m.value,_.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-gray-50",placeholder:"Search for leads..."},null,544),[[Q,u(x)]])]),r.permissions.canCreateLead?(l(),i("div",fe,[n(ae,{href:s.route("leads.create")},{default:c(()=>[y(" Add Lead ")]),_:1},8,["href"])])):p("",!0)])]),e("div",xe,[e("div",ve,[e("div",ye,[be,n(M,{for:"customer_id",value:"Filters"})])]),e("div",we,[e("div",ke,[n(M,{for:"agent_filter",value:"Agents"}),e("div",Ce,[n(O,{options:I.value,modelValue:m.value,"onUpdate:modelValue":o[2]||(o[2]=t=>m.value=t),onOnchange:K},null,8,["options","modelValue"])])]),e("div",Le,[n(M,{for:"county_filter",value:"Country"}),e("div",Se,[n(O,{options:F.value,modelValue:_.value,"onUpdate:modelValue":o[3]||(o[3]=t=>_.value=t),onOnchange:Y},null,8,["options","modelValue"])])])])]),e("div",Ae,[e("div",Me,[e("table",Ee,[e("thead",Ne,[e("tr",$e,[(l(),i(v,null,S(P,(t,a)=>e("th",{key:a,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:$=>u(T)(t.field,t.sortable)},[y(d(t.label)+" ",1),t.sortable?(l(),A(ce,{key:0,isSorted:u(U)===t.field,direction:u(z)},null,8,["isSorted","direction"])):p("",!0)],8,Ve)),64))])]),r.data.data&&r.data.data.length>0?(l(),i("tbody",Be,[(l(!0),i(v,null,S(r.data.data,t=>(l(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",De,d(t.lead_number),1),e("td",Oe,d(t.client_name),1),e("td",Te,d(t.county?t.county.name:"N/A"),1),e("td",Ue,d(t.open_size),1),e("td",ze,d(t.box_style),1),e("td",je,d(t.stock),1),e("td",Ie,[g.value[t.id]!==void 0?(l(),i("div",Fe,[B(e("select",{"onUpdate:modelValue":a=>g.value[t.id]=a,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:a=>G(t.id,g.value[t.id])},[(l(),i(v,null,S(j,a=>e("option",{key:a.id,value:a.id},d(a.name),9,Re)),64))],40,Pe),[[J,g.value[t.id]]]),e("button",{onClick:a=>Z(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,We)])):(l(),i("div",qe,[e("span",{class:ee(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",q(t.status)]),onClick:a=>N(t.id,t.status),title:"Click to edit status"},d(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,Ke),e("button",{onClick:a=>N(t.id,t.status),class:"text-gray-400 hover:text-gray-600 text-xs",title:"Edit status"}," ✏️ ",8,Ye)]))]),e("td",Ze,d(t.creator?t.creator.first_name:"N/A"),1),e("td",Ge,[e("div",He,[n(se,{align:"right",width:"48"},{trigger:c(()=>[Qe]),content:c(()=>[r.permissions.canEditLead?(l(),A(oe,{key:0,href:s.route("leads.edit",{id:t.id})},{svg:c(()=>[Xe]),text:c(()=>[Je]),_:2},1032,["href"])):p("",!0),r.permissions.canDeleteLead?(l(),i("button",{key:1,type:"button",onClick:a=>R(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ot,8,et)):p("",!0)]),_:2},1024)])])]))),128))])):(l(),i("tbody",at,nt))])])]),r.data.data&&r.data.data.length>0?(l(),A(de,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):p("",!0)]),n(le,{show:w.value,onClose:k},{default:c(()=>[e("div",it,[rt,e("div",dt,[n(ne,{onClick:k},{default:c(()=>[y("Cancel")]),_:1}),n(ie,{class:"ml-3",onClick:W,disabled:u(b).processing},{default:c(()=>[y(" Delete Lead ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{yt as default};
