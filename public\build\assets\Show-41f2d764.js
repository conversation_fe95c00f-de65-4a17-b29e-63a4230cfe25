import{_ as f,a as r}from"./AdminLayout-49609d61.js";import{P as w}from"./PrimaryButton-a4c718ca.js";import{b as l,d as o,e as d,u as p,f as i,F as u,Z as v,g as t,t as s,k as x,n as k,i as n,j as L}from"./app-be4dd2ad.js";import"./_plugin-vue_export-helper-c27b6911.js";const S={class:"animate-top"},C={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},B={class:"text-3xl font-bold text-gray-900"},j=t("p",{class:"text-gray-600 mt-1"},"Leads Details",-1),z={class:"flex flex-col sm:flex-row gap-3"},D=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),N={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},A={class:"lg:col-span-2 space-y-8"},V={class:"bg-white shadow rounded-lg p-6"},Q=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1),T={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},q=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),I={class:"mt-1 text-sm text-gray-700"},M=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),P={class:"mt-1 text-sm text-gray-700"},Y=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),E=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),H={class:"mt-1 text-sm text-gray-700"},U={class:"bg-white shadow rounded-lg p-6"},$=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),F={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},O=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),Z={class:"mt-1 text-sm text-gray-700"},G=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),J={class:"mt-1 text-sm text-gray-700"},K=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),R={class:"mt-1 text-sm text-gray-700"},W=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),X={class:"mt-1 text-sm text-gray-700"},tt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),et={class:"mt-1 text-sm text-gray-700"},st=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),at={class:"mt-1 text-sm text-gray-700"},lt={key:0,class:"md:col-span-2"},ot=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),dt={class:"mt-1 text-sm text-gray-700"},it={class:"bg-white shadow rounded-lg p-6"},nt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Qty Details",-1),ct={class:"overflow-x-auto"},rt={class:"min-w-full divide-y divide-gray-200"},xt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"QTY 1"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"QTY 2"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"QTY 3"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"QTY 4")])],-1),mt={class:"bg-white divide-y divide-gray-200"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},gt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},yt={key:0,class:"bg-white shadow rounded-lg p-6"},bt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),ft={class:"text-sm text-gray-700 whitespace-pre-wrap"},wt={class:"space-y-6"},pt={class:"bg-white shadow rounded-lg p-6"},vt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),kt={class:"space-y-3 w-full"},Lt=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),x(" Edit Lead ")],-1),St=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),x(" Back to List ")],-1),Ct={key:0,class:"bg-white shadow rounded-lg p-6"},Bt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Documents",-1),jt={class:"space-y-3"},zt={class:"flex items-center space-x-3"},Dt=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1),Nt={class:"text-sm text-gray-700"},At=["href"],Vt={class:"bg-white shadow rounded-lg p-6"},Qt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),Tt={class:"space-y-4"},qt={class:"flex items-start space-x-3"},It=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),Mt=t("p",{class:"text-sm font-semibold text-gray-900"},"leads Created",-1),Pt={class:"text-xs text-gray-500"},Yt={key:0,class:"flex items-start space-x-3"},Et=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),Ht=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),Ut={class:"text-xs text-gray-500"},Gt={__name:"Show",props:{leads:{type:Object,required:!0}},setup(e){const y=a=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",accepted:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",expired:"bg-yellow-100 text-yellow-800"})[a]||"bg-gray-100 text-gray-800",m=a=>a?new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A";return(a,b)=>(l(),o(u,null,[d(p(v),{title:`Lead - ${e.leads.lead_number}`},null,8,["title"]),d(f,null,{default:i(()=>{var h,g,_;return[t("div",S,[t("div",C,[t("div",null,[t("h1",B,s(e.leads.lead_number),1),j]),t("div",z,[d(r,{href:a.route("leads.edit",e.leads.id)},{svg:i(()=>[d(w,{class:"w-full items-center"},{default:i(()=>[D,x(" Edit leads ")]),_:1})]),_:1},8,["href"])])]),t("div",N,[t("div",A,[t("div",V,[Q,t("div",T,[t("div",null,[q,t("p",I,s(e.leads.client_name),1)]),t("div",null,[M,t("p",P,s(((h=e.leads.county)==null?void 0:h.name)||"N/A"),1)]),t("div",null,[Y,t("span",{class:k(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",y(e.leads.status)])},s(e.leads.status.charAt(0).toUpperCase()+e.leads.status.slice(1)),3)]),t("div",null,[E,t("p",H,s((g=e.leads.creator)==null?void 0:g.first_name)+" "+s((_=e.leads.creator)==null?void 0:_.last_name),1)])])]),t("div",U,[$,t("div",F,[t("div",null,[O,t("p",Z,s(e.leads.dimensions),1)]),t("div",null,[G,t("p",J,s(e.leads.open_size),1)]),t("div",null,[K,t("p",R,s(e.leads.box_style),1)]),t("div",null,[W,t("p",X,s(e.leads.stock),1)]),t("div",null,[tt,t("p",et,s(e.leads.lamination),1)]),t("div",null,[st,t("p",at,s(e.leads.printing),1)]),e.leads.add_ons?(l(),o("div",lt,[ot,t("p",dt,s(e.leads.add_ons),1)])):n("",!0)])]),t("div",it,[nt,t("div",ct,[t("table",rt,[xt,t("tbody",mt,[t("tr",null,[t("td",ht,s(parseInt(e.leads.qty_1).toLocaleString())+" pcs",1),t("td",gt,s(parseInt(e.leads.qty_2).toLocaleString())+" pcs",1),t("td",_t,s(parseInt(e.leads.qty_3).toLocaleString())+" pcs",1),t("td",ut,s(parseInt(e.leads.qty_4).toLocaleString())+" pcs",1)])])])])]),e.leads.notes?(l(),o("div",yt,[bt,t("p",ft,s(e.leads.notes),1)])):n("",!0)]),t("div",wt,[t("div",pt,[vt,t("div",kt,[d(r,{href:a.route("leads.edit",e.leads.id),class:"w-full"},{svg:i(()=>[Lt]),_:1},8,["href"]),d(r,{href:a.route("leads.index"),class:"w-full"},{svg:i(()=>[St]),_:1},8,["href"])])]),e.leads.documents&&e.leads.documents.length>0?(l(),o("div",Ct,[Bt,t("div",jt,[(l(!0),o(u,null,L(e.leads.documents,c=>(l(),o("div",{key:c.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",zt,[Dt,t("span",Nt,s(c.orignal_name),1)]),t("a",{href:"/uploads/leads/"+c.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,At)]))),128))])])):n("",!0),t("div",Vt,[Qt,t("div",Tt,[t("div",qt,[It,t("div",null,[Mt,t("p",Pt,s(m(e.leads.created_at)),1)])]),e.leads.updated_at!==e.leads.created_at?(l(),o("div",Yt,[Et,t("div",null,[Ht,t("p",Ut,s(m(e.leads.updated_at)),1)])])):n("",!0)])])])])])]}),_:1})],64))}};export{Gt as default};
