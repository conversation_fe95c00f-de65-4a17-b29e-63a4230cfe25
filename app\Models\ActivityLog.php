<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'action', 'description', 'user_id', 'loggable_type', 'loggable_id',
        'metadata', 'outcome', 'task_id'
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function loggable(): MorphTo
    {
        return $this->morphTo();
    }

    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    // Scopes
    public function scopeForEntity($query, $type, $id)
    {
        return $query->where('loggable_type', $type)
                    ->where('loggable_id', $id);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getOutcomeColorAttribute(): string
    {
        return match($this->outcome) {
            'positive' => 'green',
            'neutral' => 'yellow',
            'negative' => 'red',
            'no_response' => 'gray',
            default => 'gray'
        };
    }

    public function getActionIconAttribute(): string
    {
        return match($this->action) {
            'call_made' => 'phone',
            'email_sent' => 'mail',
            'meeting_held' => 'users',
            'note_added' => 'edit',
            'status_changed' => 'refresh',
            'task_completed' => 'check',
            'follow_up_scheduled' => 'calendar',
            default => 'activity'
        };
    }

    // Static methods for common activities
    public static function logCall($entity, $userId, array $data): self
    {
        return static::create([
            'action' => 'call_made',
            'description' => $data['description'] ?? 'Phone call made',
            'user_id' => $userId,
            'loggable_type' => get_class($entity),
            'loggable_id' => $entity->id,
            'metadata' => [
                'duration' => $data['duration'] ?? null,
                'phone_number' => $data['phone_number'] ?? null,
                'notes' => $data['notes'] ?? null,
            ],
            'outcome' => $data['outcome'] ?? 'neutral',
            'task_id' => $data['task_id'] ?? null,
        ]);
    }

    public static function logEmail($entity, $userId, array $data): self
    {
        return static::create([
            'action' => 'email_sent',
            'description' => $data['description'] ?? 'Email sent',
            'user_id' => $userId,
            'loggable_type' => get_class($entity),
            'loggable_id' => $entity->id,
            'metadata' => [
                'subject' => $data['subject'] ?? null,
                'recipient' => $data['recipient'] ?? null,
            ],
            'outcome' => $data['outcome'] ?? 'neutral',
            'task_id' => $data['task_id'] ?? null,
        ]);
    }

    public static function logStatusChange($entity, $userId, $oldStatus, $newStatus): self
    {
        return static::create([
            'action' => 'status_changed',
            'description' => "Status changed from {$oldStatus} to {$newStatus}",
            'user_id' => $userId,
            'loggable_type' => get_class($entity),
            'loggable_id' => $entity->id,
            'metadata' => [
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ],
            'outcome' => 'neutral',
        ]);
    }
}
