import{T as u,b as _,q as w,f as p,e as o,u as e,Z as f,h as t,l as g,n as y,s as x,p as b,m as h}from"./app-e21f56bc.js";import{G as v}from"./GuestLayout-6672278c.js";import{_ as l,a as i}from"./TextInput-1ecc3ccf.js";import{_ as n}from"./InputLabel-d2dad70b.js";import{P as V}from"./PrimaryButton-3e573a38.js";import{_ as P}from"./_plugin-vue_export-helper-c27b6911.js";const k=r=>(b("data-v-c5764cb4"),r=r(),h(),r),S=k(()=>t("h2",{class:"text-center text-xl font-semibold leading-9 tracking-tight text-indigo-600"},"Reset Password",-1)),q=["onSubmit"],R={class:"mt-2"},B={class:"mt-4"},I={class:"flex items-center justify-end mt-4"},C={__name:"ResetPassword",props:{email:{type:String,required:!0},token:{type:String,required:!0}},setup(r){const m=r,s=u({token:m.token,email:m.email,password:"",password_confirmation:""}),c=()=>{s.post(route("password.store"),{onFinish:()=>s.reset("password","password_confirmation")})};return(N,a)=>(_(),w(v,null,{default:p(()=>[o(e(f),{title:"Reset Password"}),S,t("form",{onSubmit:x(c,["prevent"])},[t("div",null,[o(n,{for:"email",value:"Email"}),o(l,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).email,"onUpdate:modelValue":a[0]||(a[0]=d=>e(s).email=d),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(i,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),t("div",R,[o(n,{for:"password",value:"Password"}),o(l,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password,"onUpdate:modelValue":a[1]||(a[1]=d=>e(s).password=d),required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(i,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),t("div",B,[o(n,{for:"password_confirmation",value:"Confirm Password"}),o(l,{id:"password_confirmation",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password_confirmation,"onUpdate:modelValue":a[2]||(a[2]=d=>e(s).password_confirmation=d),required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(i,{class:"mt-2",message:e(s).errors.password_confirmation},null,8,["message"])]),t("div",I,[o(V,{class:y({"opacity-25":e(s).processing}),disabled:e(s).processing},{default:p(()=>[g(" Reset Password ")]),_:1},8,["class","disabled"])])],40,q)]),_:1}))}},E=P(C,[["__scopeId","data-v-c5764cb4"]]);export{E as default};
