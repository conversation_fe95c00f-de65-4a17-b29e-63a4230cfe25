<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->integer('confirmed_qty_1')->nullable()->after('qty_4');
            $table->integer('confirmed_qty_2')->nullable()->after('confirmed_qty_1');
            $table->integer('confirmed_qty_3')->nullable()->after('confirmed_qty_2');
            $table->integer('confirmed_qty_4')->nullable()->after('confirmed_qty_3');
            $table->timestamp('order_confirmed_at')->nullable()->after('confirmed_qty_4');
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn([
                'confirmed_qty_1',
                'confirmed_qty_2', 
                'confirmed_qty_3',
                'confirmed_qty_4',
                'order_confirmed_at'
            ]);
        });
    }
};
