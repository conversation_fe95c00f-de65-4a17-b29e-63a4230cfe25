import{r as h,c as V,b as a,d as i,e as n,u as v,f as d,F as w,Z as te,g as e,h as M,v as se,E as oe,k,i as g,j as N,m as $,t as u,G as ae,y as le,n as ne,O as D}from"./app-be4dd2ad.js";import{_ as ie,b as re,a as U}from"./AdminLayout-49609d61.js";import{_ as de}from"./CreateButton-8bfa2249.js";import{M as ce,_ as ue}from"./Modal-32e0636f.js";import{D as me}from"./DangerButton-517220c9.js";import{s as _e,_ as pe,a as he}from"./ArrowIcon-df709a05.js";import{_ as E}from"./SearchableDropdownNew-87fef7b5.js";import{_ as C}from"./InputLabel-2a244146.js";/* empty css                                                              */import"./_plugin-vue_export-helper-c27b6911.js";const ve={class:"animate-top"},ge={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},fe=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Leads")],-1),xe={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},ye={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},be=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),we={key:0,class:"sm:flex-none"},ke={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},Ce={class:"flex justify-between mb-2"},Se={class:"flex"},Ae=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Le={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ve={key:0,class:"sm:col-span-4"},Me={class:"relative mt-2"},Ne={class:"sm:col-span-4"},$e={class:"relative mt-2"},Ee={class:"sm:col-span-4"},Oe={class:"relative mt-2"},Be={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},ze={class:"shadow rounded-lg"},De={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ue={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},je={class:"border-b-2"},Te=["onClick"],Pe={key:0},Fe={class:"px-4 py-2.5 min-w-28"},Ie={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},We={class:"px-4 py-2.5"},Re={class:"px-4 py-2.5 min-w-28"},qe={class:"px-4 py-2.5 min-w-28"},Ge={class:"px-4 py-2.5 min-w-28"},Ke={class:"px-4 py-2.5"},Ye={key:0,class:"flex items-center space-x-2 w-full"},Ze=["onUpdate:modelValue","onChange"],He=["value"],Qe=["onClick"],Xe={key:1,class:"flex items-center space-x-2"},Je=["onClick"],et={key:0,class:"px-4 py-2.5"},tt={class:"items-center px-4 py-2.5"},st={class:"flex items-center justify-start gap-4"},ot=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),at=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),lt=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),nt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),it=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),rt=["onClick"],dt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),ct=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),ut=[dt,ct],mt={key:1},_t=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),pt=[_t],ht={class:"p-6"},vt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this lead? ",-1),gt={class:"mt-6 flex justify-end"},Mt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","status","isAdmin"],setup(c){const _=c,{form:S,search:y,sort:j,fetchData:ft,sortKey:T,sortDirection:P}=_e("leads.index"),A=h(!1),O=h(null),B=[{id:"new",name:"New"},{id:"contacted",name:"Contacted"},{id:"quotation",name:"Quotation"},{id:"negotiation",name:"Negotiation"},{id:"won",name:"Won"},{id:"lost",name:"Lost"}],F=V(()=>[{id:"",name:"All Agents"},..._.agents]),I=V(()=>[{id:"",name:"All Country"},..._.counties]),W=V(()=>[{id:"",name:"All Status"},...B]),R=[{field:"lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!1,visible:!0},{field:"open_size",label:"OPEN SIZE",sortable:!0,visible:!0},{field:"box_style",label:"BOX STYLE",sortable:!0,visible:!0},{field:"stock",label:"STOCK",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:_.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],q=s=>{O.value=s,A.value=!0},L=()=>{A.value=!1},G=()=>{S.delete(route("leads.destroy",{lead:O.value}),{onSuccess:()=>L()})},K=s=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",m=h(_.agent_id||""),r=h(_.county_id||""),f=h(_.status||""),x=h(""),p=h({}),Y=(s,o)=>{m.value=s,b(x.value,m.value,r.value,f.value)},Z=(s,o)=>{r.value=s,b(x.value,m.value,r.value,f.value)},H=(s,o)=>{f.value=s,b(x.value,m.value,r.value,f.value)},b=(s,o,t,l)=>{x.value=s;const z=o===""?null:o,ee=t===""?null:t;S.get(route("leads.index",{search:s,agent_id:z,county_id:ee,status:l}),{preserveState:!0})},Q=(s,o)=>{p.value[s]=o},X=s=>{delete p.value[s]},J=(s,o)=>{D.post(route("leads.update-status",s),{status:o},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete p.value[s];const l=new URLSearchParams(window.location.search).get("page")||1;D.get(route("leads.index"),{search:x.value,agent_id:m.value===""?null:m.value,county_id:r.value===""?null:r.value,page:l},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})};return(s,o)=>(a(),i(w,null,[n(v(te),{title:"Leads"}),n(ie,null,{default:d(()=>[e("div",ve,[e("div",ge,[fe,e("div",xe,[e("div",ye,[be,M(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>oe(y)?y.value=t:null),onInput:o[1]||(o[1]=t=>b(v(y),m.value,r.value,f.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for leads..."},null,544),[[se,v(y)]])]),c.permissions.canCreateLead?(a(),i("div",we,[n(de,{href:s.route("leads.create")},{default:d(()=>[k(" Add Lead ")]),_:1},8,["href"])])):g("",!0)])]),e("div",ke,[e("div",Ce,[e("div",Se,[Ae,n(C,{for:"customer_id",value:"Filters"})])]),e("div",Le,[_.isAdmin?(a(),i("div",Ve,[n(C,{for:"agent_filter",value:"Agents"}),e("div",Me,[n(E,{options:F.value,modelValue:m.value,"onUpdate:modelValue":o[2]||(o[2]=t=>m.value=t),onOnchange:Y},null,8,["options","modelValue"])])])):g("",!0),e("div",Ne,[n(C,{for:"county_filter",value:"Country"}),e("div",$e,[n(E,{options:I.value,modelValue:r.value,"onUpdate:modelValue":o[3]||(o[3]=t=>r.value=t),onOnchange:Z},null,8,["options","modelValue"])])]),e("div",Ee,[n(C,{for:"county_filter",value:"Status"}),e("div",Oe,[n(E,{options:W.value,modelValue:r.value,"onUpdate:modelValue":o[4]||(o[4]=t=>r.value=t),onOnchange:H},null,8,["options","modelValue"])])])])]),e("div",Be,[e("div",ze,[e("table",De,[e("thead",Ue,[e("tr",je,[(a(),i(w,null,N(R,(t,l)=>M(e("th",{key:l,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:z=>v(j)(t.field,t.sortable)},[k(u(t.label)+" ",1),t.sortable?(a(),$(he,{key:0,isSorted:v(T)===t.field,direction:v(P)},null,8,["isSorted","direction"])):g("",!0)],8,Te),[[ae,t.visible]])),64))])]),c.data.data&&c.data.data.length>0?(a(),i("tbody",Pe,[(a(!0),i(w,null,N(c.data.data,t=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Fe,u(t.lead_number),1),e("td",Ie,u(t.client_name),1),e("td",We,u(t.county?t.county.name:"N/A"),1),e("td",Re,u(t.open_size),1),e("td",qe,u(t.box_style),1),e("td",Ge,u(t.stock),1),e("td",Ke,[p.value[t.id]!==void 0?(a(),i("div",Ye,[M(e("select",{"onUpdate:modelValue":l=>p.value[t.id]=l,class:"text-sm border-gray-300 rounded px-2 py-1",onChange:l=>J(t.id,p.value[t.id])},[(a(),i(w,null,N(B,l=>e("option",{class:"text-sm text-gray-900 text-bold",key:l.id,value:l.id},u(l.name),9,He)),64))],40,Ze),[[le,p.value[t.id]]]),e("button",{onClick:l=>X(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,Qe)])):(a(),i("div",Xe,[e("span",{class:ne(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",K(t.status)]),onClick:l=>Q(t.id,t.status),title:"Click to edit status"},u(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,Je)]))]),_.isAdmin?(a(),i("td",et,u(t.creator?t.creator.first_name:"N/A"),1)):g("",!0),e("td",tt,[e("div",st,[n(re,{align:"right",width:"48"},{trigger:d(()=>[ot]),content:d(()=>[n(U,{href:s.route("leads.show",{id:t.id})},{svg:d(()=>[at]),text:d(()=>[lt]),_:2},1032,["href"]),c.permissions.canEditLead?(a(),$(U,{key:0,href:s.route("leads.edit",{id:t.id})},{svg:d(()=>[nt]),text:d(()=>[it]),_:2},1032,["href"])):g("",!0),c.permissions.canDeleteLead?(a(),i("button",{key:1,type:"button",onClick:l=>q(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ut,8,rt)):g("",!0)]),_:2},1024)])])]))),128))])):(a(),i("tbody",mt,pt))])])]),c.data.data&&c.data.data.length>0?(a(),$(pe,{key:0,class:"mt-6",links:c.data.links},null,8,["links"])):g("",!0)]),n(ce,{show:A.value,onClose:L},{default:d(()=>[e("div",ht,[vt,e("div",gt,[n(ue,{onClick:L},{default:d(()=>[k("Cancel")]),_:1}),n(me,{class:"ml-3",onClick:G,disabled:v(S).processing},{default:d(()=>[k(" Delete Lead ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Mt as default};
