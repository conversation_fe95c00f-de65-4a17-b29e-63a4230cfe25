import{T as x,r as l,b as t,d as s,g as h,F as _,j as k,e as m,u as v,x as p,n as b,i as w}from"./app-5d829b53.js";function A(n,u={}){const i=x({}),e=l(""),o=l("id"),a=l("desc"),d=l({...u}),y=r=>{d.value={...d.value,...r}},g=(r={})=>{const c={...d.value,...r,search:e.value,sort_by:o.value,sort_direction:a.value},f=new URLSearchParams(window.location.search).get("page");f&&(c.page=f),i.get(route(n,c),{preserveState:!0,replace:!0})};return{form:i,search:e,sort:(r,c=!0)=>{c&&(o.value===r?a.value=a.value==="asc"?"desc":"asc":(a.value="asc",o.value=r),g())},fetchData:g,sortKey:o,sortDirection:a,updateParams:y}}const B={key:0},F={class:"flex flex-wrap justify-end isolate rounded-md"},L={key:0},M={key:1},C={__name:"Pagination",props:["links"],setup(n){return(u,i)=>n.links.length>1?(t(),s("div",B,[h("div",F,[(t(!0),s(_,null,k(n.links,(e,o)=>(t(),s(_,{key:o},[e.url===null?(t(),s("div",L,[m(v(p),{innerHTML:e.label,href:"#",class:"inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 bg-white hover:bg-gray-50 focus:z-20 focus:outline-offset-0"},null,8,["innerHTML"])])):(t(),s("div",M,[m(v(p),{innerHTML:e.label,href:e.url,class:b([{"bg-indigo-600 text-white hover:bg-indigo-600":e.active},"bg-white inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"])},null,8,["innerHTML","href","class"])]))],64))),128))])])):w("",!0)}},P={key:0},S={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},T=h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1),$=[T],j={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},H=h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1),z=[H],q={__name:"ArrowIcon",props:{isSorted:{type:Boolean,default:!1},direction:{type:String,default:"asc"}},setup(n){return(u,i)=>n.isSorted?(t(),s("span",P,[n.direction==="asc"?(t(),s("svg",S,$)):(t(),s("svg",j,z))])):w("",!0)}};export{C as _,q as a,A as s};
