<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, watch, computed } from 'vue';

const props = defineProps({
    quotation: {
        type: Object,
        required: true
    }
});

// Initialize form with empty quantities - agent will select what client needs
const form = useForm({
    quotation_id: props.quotation.id,
    selected_qty_1: '',
    selected_qty_2: '',
    selected_qty_3: '',
    selected_qty_4: '',
    notes: '',
    expected_delivery: '',
    tracking_number: ''
});

// Checkbox states for quantity selection - default unchecked, agent selects what client needs
const includeQty1 = ref(false);
const includeQty2 = ref(false);
const includeQty3 = ref(false);
const includeQty4 = ref(false);


const totalAmount = ref(0);

const submit = () => {
    form.post(route('orders.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

// Calculate total amount when quantities change
const calculateTotal = () => {
    let total = 0;

    if (includeQty1.value && form.selected_qty_1 && props.quotation.price_qty_1) {
        total += parseFloat(form.selected_qty_1) * parseFloat(props.quotation.price_qty_1);
    }
    if (includeQty2.value && form.selected_qty_2 && props.quotation.price_qty_2) {
        total += parseFloat(form.selected_qty_2) * parseFloat(props.quotation.price_qty_2);
    }
    if (includeQty3.value && form.selected_qty_3 && props.quotation.price_qty_3) {
        total += parseFloat(form.selected_qty_3) * parseFloat(props.quotation.price_qty_3);
    }
    if (includeQty4.value && form.selected_qty_4 && props.quotation.price_qty_4) {
        total += parseFloat(form.selected_qty_4) * parseFloat(props.quotation.price_qty_4);
    }

    totalAmount.value = total;
};

// Clear quantity when checkbox is unchecked
const handleQtyCheckbox = (qtyNumber, isChecked) => {
    if (!isChecked) {
        form[`selected_qty_${qtyNumber}`] = '';
    }
    calculateTotal();
};

// Watch for changes in quantities and checkboxes
watch([
    () => form.selected_qty_1, () => includeQty1.value,
    () => form.selected_qty_2, () => includeQty2.value,
    () => form.selected_qty_3, () => includeQty3.value,
    () => form.selected_qty_4, () => includeQty4.value
], calculateTotal);

// Set default expected delivery date (7 days from now)
const defaultDelivery = new Date();
defaultDelivery.setDate(defaultDelivery.getDate() + 7);
form.expected_delivery = defaultDelivery.toISOString().split('T')[0];

// Helper functions for bulk selection
const selectAllQuantities = () => {
    if (props.quotation.qty_1) {
        includeQty1.value = true;
        form.selected_qty_1 = props.quotation.qty_1;
    }
    if (props.quotation.qty_2) {
        includeQty2.value = true;
        form.selected_qty_2 = props.quotation.qty_2;
    }
    if (props.quotation.qty_3) {
        includeQty3.value = true;
        form.selected_qty_3 = props.quotation.qty_3;
    }
    if (props.quotation.qty_4) {
        includeQty4.value = true;
        form.selected_qty_4 = props.quotation.qty_4;
    }
    calculateTotal();
};

const clearAllQuantities = () => {
    includeQty1.value = false;
    includeQty2.value = false;
    includeQty3.value = false;
    includeQty4.value = false;
    form.selected_qty_1 = '';
    form.selected_qty_2 = '';
    form.selected_qty_3 = '';
    form.selected_qty_4 = '';
    calculateTotal();
};
</script>

<template>
    <Head title="Convert Quotation to Order" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">
                Convert Quotation to Order
            </h2>

            <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-sm text-blue-800">
                    <strong>Converting from Quotation:</strong> {{ quotation.quotation_number }} - {{ quotation.client_name }}
                </p>
                <p class="text-xs text-blue-600 mt-2">
                    💡 Select the quantities that the client actually needs. Use the buttons below for quick selection.
                </p>
            </div>

            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">

                        <!-- Quotation Details (Read-only) -->
                        <div class="sm:col-span-12">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quotation Details</h3>
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel value="Client Name"/>
                            <p class="mt-1 text-sm text-gray-900 p-2 bg-gray-50 rounded">{{ quotation.client_name }}</p>
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel value="County"/>
                            <p class="mt-1 text-sm text-gray-900 p-2 bg-gray-50 rounded">{{ quotation.county?.name || 'N/A' }}</p>
                        </div>

                        <div class="sm:col-span-12">
                            <InputLabel value="Product Specifications"/>
                            <div class="mt-2 p-4 bg-gray-50 rounded-lg">
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                                    <div><strong>Dimensions:</strong> {{ quotation.dimensions }}</div>
                                    <div><strong>Open Size:</strong> {{ quotation.open_size }}</div>
                                    <div><strong>Box Style:</strong> {{ quotation.box_style }}</div>
                                    <div><strong>Stock:</strong> {{ quotation.stock }}</div>
                                    <div><strong>Lamination:</strong> {{ quotation.lamination }}</div>
                                    <div><strong>Printing:</strong> {{ quotation.printing }}</div>
                                </div>
                                <div v-if="quotation.add_ons" class="mt-2">
                                    <strong>Add-ons:</strong> {{ quotation.add_ons }}
                                </div>
                            </div>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="sm:col-span-12 mt-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Select Quantities for Order</h3>
                                <div class="flex space-x-2">
                                    <button
                                        type="button"
                                        @click="selectAllQuantities"
                                        class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                                    >
                                        Select All
                                    </button>
                                    <button
                                        type="button"
                                        @click="clearAllQuantities"
                                        class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                    >
                                        Clear All
                                    </button>
                                </div>
                            </div>
                            <p class="text-sm text-gray-600 mb-6">Check the quantities that the client needs and specify the exact amounts:</p>
                        </div>

                        <!-- Quantity 1 Section -->
                        <div class="sm:col-span-3" v-if="quotation.qty_1 && quotation.price_qty_1">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty1"
                                    @update:checked="(checked) => handleQtyCheckbox(1, checked)"
                                />
                                <InputLabel value="Client wants Quantity 1" class="text-base font-medium text-blue-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4" v-if="includeQty1">
                                <div>
                                    <InputLabel for="selected_qty_1" :value="`Client Order Qty 1 (Available: ${quotation.qty_1})`"/>
                                    <TextInput
                                        id="selected_qty_1"
                                        type="number"
                                        v-model="form.selected_qty_1"
                                        :max="quotation.qty_1"
                                        min="1"
                                        :placeholder="`Max: ${quotation.qty_1}`"
                                    />
                                    <InputError :message="form.errors.selected_qty_1"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: ${{ quotation.price_qty_1 }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 2 Section -->
                        <div class="sm:col-span-3" v-if="quotation.qty_2 && quotation.price_qty_2">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty2"
                                    @update:checked="(checked) => handleQtyCheckbox(2, checked)"
                                />
                                <InputLabel value="Client wants Quantity 2" class="text-base font-medium text-green-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4" v-if="includeQty2">
                                <div>
                                    <InputLabel for="selected_qty_2" :value="`Client Order Qty 2 (Available: ${quotation.qty_2})`"/>
                                    <TextInput
                                        id="selected_qty_2"
                                        type="number"
                                        v-model="form.selected_qty_2"
                                        :max="quotation.qty_2"
                                        min="1"
                                        :placeholder="`Max: ${quotation.qty_2}`"
                                    />
                                    <InputError :message="form.errors.selected_qty_2"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: ${{ quotation.price_qty_2 }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 3 Section -->
                        <div class="sm:col-span-3" v-if="quotation.qty_3 && quotation.price_qty_3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty3"
                                    @update:checked="(checked) => handleQtyCheckbox(3, checked)"
                                />
                                <InputLabel value="Client wants Quantity 3" class="text-base font-medium text-yellow-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4" v-if="includeQty3">
                                <div>
                                    <InputLabel for="selected_qty_3" :value="`Client Order Qty 3 (Available: ${quotation.qty_3})`"/>
                                    <TextInput
                                        id="selected_qty_3"
                                        type="number"
                                        v-model="form.selected_qty_3"
                                        :max="quotation.qty_3"
                                        min="1"
                                        :placeholder="`Max: ${quotation.qty_3}`"
                                    />
                                    <InputError :message="form.errors.selected_qty_3"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: ${{ quotation.price_qty_3 }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 4 Section -->
                        <div class="sm:col-span-3" v-if="quotation.qty_4 && quotation.price_qty_4">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty4"
                                    @update:checked="(checked) => handleQtyCheckbox(4, checked)"
                                />
                                <InputLabel value="Client wants Quantity 4" class="text-base font-medium text-purple-800"/>
                            </div>
                            <div class="grid grid-cols-1 gap-4" v-if="includeQty4">
                                <div>
                                    <InputLabel for="selected_qty_4" :value="`Client Order Qty 4 (Available: ${quotation.qty_4})`"/>
                                    <TextInput
                                        id="selected_qty_4"
                                        type="number"
                                        v-model="form.selected_qty_4"
                                        :max="quotation.qty_4"
                                        min="1"
                                        :placeholder="`Max: ${quotation.qty_4}`"
                                    />
                                    <InputError :message="form.errors.selected_qty_4"/>
                                    <p class="text-sm text-gray-500 mt-1">Price: ${{ quotation.price_qty_4 }} per unit</p>
                                </div>
                            </div>
                        </div>

                        <!-- Order Total Display -->
                        <div class="sm:col-span-12" v-if="totalAmount > 0">
                            <div class="bg-green-50 border border-green-200 p-6 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-lg font-semibold text-green-800">
                                            Order Total: ${{ totalAmount.toFixed(2) }}
                                        </p>
                                        <p class="text-sm text-green-600 mt-1">
                                            Based on selected quantities and pricing from quotation
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Information -->
                        <div class="sm:col-span-12 mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Order Information</h3>
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel for="expected_delivery" value="Expected Delivery Date"/>
                            <TextInput
                                id="expected_delivery"
                                type="date"
                                v-model="form.expected_delivery"
                            />
                            <InputError :message="form.errors.expected_delivery"/>
                        </div>

                        <div class="sm:col-span-12">
                            <InputLabel for="notes" value="Order Notes"/>
                            <TextArea
                                id="notes"
                                v-model="form.notes"
                                rows="4"
                                placeholder="Any special instructions or notes for this order..."
                            />
                            <InputError :message="form.errors.notes"/>
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('quotations.show', quotation.id)">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing || totalAmount === 0">
                            Create Order
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
