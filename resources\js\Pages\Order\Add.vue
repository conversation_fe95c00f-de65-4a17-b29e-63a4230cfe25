<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, watch, computed } from 'vue';

const props = defineProps({
    quotation: {
        type: Object,
        required: true
    }
});

// Initialize form
const form = useForm({
    quotation_id: props.quotation.id,
    selected_qty_1: '',
    selected_qty_2: '',
    selected_qty_3: '',
    selected_qty_4: '',
    notes: '',
    expected_delivery: ''
});

// Checkbox states for quantity selection
const includeQty1 = ref(!!props.quotation.qty_1);
const includeQty2 = ref(false);
const includeQty3 = ref(false);
const includeQty4 = ref(false);


const totalAmount = ref(0);

const submit = () => {
    form.post(route('orders.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

// Calculate total amount when quantities change
const calculateTotal = () => {
    let total = 0;

    if (includeQty1.value && form.selected_qty_1 && props.quotation.price_qty_1) {
        total += parseFloat(form.selected_qty_1) * parseFloat(props.quotation.price_qty_1);
    }
    if (includeQty2.value && form.selected_qty_2 && props.quotation.price_qty_2) {
        total += parseFloat(form.selected_qty_2) * parseFloat(props.quotation.price_qty_2);
    }
    if (includeQty3.value && form.selected_qty_3 && props.quotation.price_qty_3) {
        total += parseFloat(form.selected_qty_3) * parseFloat(props.quotation.price_qty_3);
    }
    if (includeQty4.value && form.selected_qty_4 && props.quotation.price_qty_4) {
        total += parseFloat(form.selected_qty_4) * parseFloat(props.quotation.price_qty_4);
    }

    totalAmount.value = total;
};

// Clear quantity when checkbox is unchecked
const handleQtyCheckbox = (qtyNumber, isChecked) => {
    if (!isChecked) {
        form[`selected_qty_${qtyNumber}`] = '';
    }
    calculateTotal();
};

// Watch for changes in quantities and checkboxes
watch([
    () => form.selected_qty_1, () => includeQty1.value,
    () => form.selected_qty_2, () => includeQty2.value,
    () => form.selected_qty_3, () => includeQty3.value,
    () => form.selected_qty_4, () => includeQty4.value
], calculateTotal);

// Set default expected delivery date (7 days from now)
const defaultDelivery = new Date();
defaultDelivery.setDate(defaultDelivery.getDate() + 7);
form.expected_delivery = defaultDelivery.toISOString().split('T')[0];

// Available quantities from quotation
const availableQuantities = computed(() => {
    const quantities = [];
    if (props.quotation.qty_1 && props.quotation.price_qty_1) {
        quantities.push({
            number: 1,
            qty: props.quotation.qty_1,
            price: props.quotation.price_qty_1,
            total: props.quotation.qty_1 * props.quotation.price_qty_1
        });
    }
    if (props.quotation.qty_2 && props.quotation.price_qty_2) {
        quantities.push({
            number: 2,
            qty: props.quotation.qty_2,
            price: props.quotation.price_qty_2,
            total: props.quotation.qty_2 * props.quotation.price_qty_2
        });
    }
    if (props.quotation.qty_3 && props.quotation.price_qty_3) {
        quantities.push({
            number: 3,
            qty: props.quotation.qty_3,
            price: props.quotation.price_qty_3,
            total: props.quotation.qty_3 * props.quotation.price_qty_3
        });
    }
    if (props.quotation.qty_4 && props.quotation.price_qty_4) {
        quantities.push({
            number: 4,
            qty: props.quotation.qty_4,
            price: props.quotation.price_qty_4,
            total: props.quotation.qty_4 * props.quotation.price_qty_4
        });
    }
    return quantities;
});
</script>

<template>
    <Head title="Convert Quotation to Order" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">
                Convert Quotation to Order
            </h2>

            <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-sm text-blue-800">
                    <strong>Converting from Quotation:</strong> {{ quotation.quotation_number }} - {{ quotation.client_name }}
                </p>
            </div>

            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">

                        <!-- Quotation Details (Read-only) -->
                        <div class="sm:col-span-12">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quotation Details</h3>
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel value="Client Name"/>
                            <p class="mt-1 text-sm text-gray-900 p-2 bg-gray-50 rounded">{{ quotation.client_name }}</p>
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel value="County"/>
                            <p class="mt-1 text-sm text-gray-900 p-2 bg-gray-50 rounded">{{ quotation.county?.name || 'N/A' }}</p>
                        </div>

                        <div class="sm:col-span-12">
                            <InputLabel value="Product Specifications"/>
                            <div class="mt-2 p-4 bg-gray-50 rounded-lg">
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                                    <div><strong>Dimensions:</strong> {{ quotation.dimensions }}</div>
                                    <div><strong>Open Size:</strong> {{ quotation.open_size }}</div>
                                    <div><strong>Box Style:</strong> {{ quotation.box_style }}</div>
                                    <div><strong>Stock:</strong> {{ quotation.stock }}</div>
                                    <div><strong>Lamination:</strong> {{ quotation.lamination }}</div>
                                    <div><strong>Printing:</strong> {{ quotation.printing }}</div>
                                </div>
                                <div v-if="quotation.add_ons" class="mt-2">
                                    <strong>Add-ons:</strong> {{ quotation.add_ons }}
                                </div>
                            </div>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="sm:col-span-12 mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Select Quantities for Order</h3>
                            <p class="text-sm text-gray-600 mb-6">Choose which quantities from the quotation you want to include in this order:</p>
                        </div>

                        <!-- Available Quantities -->
                        <div class="sm:col-span-12">
                            <div class="space-y-4">
                                <div v-for="qty in availableQuantities" :key="qty.number" class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start space-x-4">
                                        <Checkbox
                                            :id="`qty_${qty.number}`"
                                            v-model:checked="qty.number === 1 ? includeQty1 : qty.number === 2 ? includeQty2 : qty.number === 3 ? includeQty3 : includeQty4"
                                            @update:checked="(checked) => handleQtyCheckbox(qty.number, checked)"
                                        />
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between">
                                                <label :for="`qty_${qty.number}`" class="text-base font-medium text-gray-900 cursor-pointer">
                                                    Quantity Option {{ qty.number }}
                                                </label>
                                                <div class="text-right">
                                                    <p class="text-lg font-semibold text-green-600">${{ qty.total.toFixed(2) }}</p>
                                                    <p class="text-sm text-gray-500">{{ qty.qty }} pcs × ${{ qty.price }}</p>
                                                </div>
                                            </div>

                                            <!-- Quantity Input (only show if checked) -->
                                            <div v-if="(qty.number === 1 && includeQty1) || (qty.number === 2 && includeQty2) || (qty.number === 3 && includeQty3) || (qty.number === 4 && includeQty4)" class="mt-3">
                                                <InputLabel :for="`selected_qty_${qty.number}`" :value="`Order Quantity (max: ${qty.qty})`"/>
                                                <TextInput
                                                    :id="`selected_qty_${qty.number}`"
                                                    type="number"
                                                    v-model="form[`selected_qty_${qty.number}`]"
                                                    :max="qty.qty"
                                                    min="1"
                                                    class="mt-1 w-32"
                                                    :placeholder="`Max: ${qty.qty}`"
                                                />
                                                <InputError :message="form.errors[`selected_qty_${qty.number}`]"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Total Display -->
                        <div class="sm:col-span-12" v-if="totalAmount > 0">
                            <div class="bg-green-50 border border-green-200 p-4 rounded-lg">
                                <p class="text-lg font-semibold text-green-800">
                                    Order Total: ${{ totalAmount.toFixed(2) }}
                                </p>
                            </div>
                        </div>

                        <!-- Order Information -->
                        <div class="sm:col-span-12 mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Order Information</h3>
                        </div>

                        <div class="sm:col-span-6">
                            <InputLabel for="expected_delivery" value="Expected Delivery Date"/>
                            <TextInput
                                id="expected_delivery"
                                type="date"
                                v-model="form.expected_delivery"
                            />
                            <InputError :message="form.errors.expected_delivery"/>
                        </div>

                        <div class="sm:col-span-12">
                            <InputLabel for="notes" value="Order Notes"/>
                            <TextArea
                                id="notes"
                                v-model="form.notes"
                                rows="4"
                                placeholder="Any special instructions or notes for this order..."
                            />
                            <InputError :message="form.errors.notes"/>
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('quotations.show', quotation.id)">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing || totalAmount === 0">
                            Create Order
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
