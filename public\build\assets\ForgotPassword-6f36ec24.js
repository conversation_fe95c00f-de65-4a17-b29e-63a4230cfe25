import{T as c,b as i,m as u,f as l,e as o,u as t,Z as _,d as p,t as f,i as g,g as a,k as w,n as y,q as x,p as h,l as b}from"./app-d08c0398.js";import{G as k}from"./GuestLayout-8eb3f7db.js";import{_ as v,a as P}from"./TextInput-3a644ba4.js";import{_ as S}from"./InputLabel-326caffb.js";import{P as V}from"./PrimaryButton-b2bd7aa7.js";import{_ as B}from"./_plugin-vue_export-helper-c27b6911.js";const m=e=>(h("data-v-e1e934d1"),e=e(),b(),e),F=m(()=>a("h2",{class:"text-center text-xl font-semibold leading-9 tracking-tight text-indigo-600"}," Forgot your password ?",-1)),N=m(()=>a("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1)),I={key:0,class:"mb-4 font-medium text-sm text-green-600"},C=["onSubmit"],E={class:"flex items-center justify-end mt-4"},$={__name:"ForgotPassword",props:{status:{type:String}},setup(e){const s=c({email:""}),d=()=>{s.post(route("password.email"))};return(q,r)=>(i(),u(k,null,{default:l(()=>[o(t(_),{title:"Forgot Password"}),F,N,e.status?(i(),p("div",I,f(e.status),1)):g("",!0),a("form",{onSubmit:x(d,["prevent"])},[a("div",null,[o(S,{for:"email",value:"Email"}),o(v,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:t(s).email,"onUpdate:modelValue":r[0]||(r[0]=n=>t(s).email=n),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(P,{class:"mt-2",message:t(s).errors.email},null,8,["message"])]),a("div",E,[o(V,{class:y({"opacity-25":t(s).processing}),disabled:t(s).processing},{default:l(()=>[w(" Email Password Reset Link ")]),_:1},8,["class","disabled"])])],40,C)]),_:1}))}},J=B($,[["__scopeId","data-v-e1e934d1"]]);export{J as default};
