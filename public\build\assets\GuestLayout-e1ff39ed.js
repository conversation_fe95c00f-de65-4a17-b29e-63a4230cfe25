import{b as s,d as t,g as o,B as a}from"./app-2e8279f3.js";import{_ as c}from"./_plugin-vue_export-helper-c27b6911.js";const _={class:"min-h-screen loginview bg-slate-100 flex flex-col sm:justify-center items-center pt-6 sm:pt-0"},r={class:"w-full sm:max-w-md mt-6 p-6 bg-white shadow-md overflow-hidden sm:rounded-lg border-gray-300"},n={__name:"GuestLayout",setup(d){return(e,l)=>(s(),t("div",_,[o("div",r,[a(e.$slots,"default",{},void 0,!0)])]))}},i=c(n,[["__scopeId","data-v-60418ccb"]]);export{i as G};
