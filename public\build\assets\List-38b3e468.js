import{r as p,c as N,b as a,d,e as l,u as g,f as n,F as $,Z as X,h as e,i as z,v as q,G as ee,j as v,k as B,q as x,l as C,t as c,H as te,n as se}from"./app-e1dc0e85.js";import{_ as oe,b as le,a as S}from"./AdminLayout-bed0e8e1.js";import{M as ae,_ as ne}from"./Modal-121db146.js";import{D as re}from"./DangerButton-9036461f.js";import{_ as ie}from"./Pagination-bcb92389.js";import{_ as j}from"./SearchableDropdownNew-b65b4fe7.js";import{_ as k}from"./InputLabel-b452155c.js";import{s as de,_ as ce}from"./ArrowIcon-1e5a9a2f.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ue={class:"animate-top"},me={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},_e=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Quotations")],-1),he={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},pe={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},ge=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ve={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},fe={class:"flex justify-between mb-2"},ye={class:"flex"},we=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),be={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},xe={key:0,class:"sm:col-span-4"},ke={class:"relative mt-2"},Ce={class:"sm:col-span-4"},Ae={class:"relative mt-2"},Ve={class:"sm:col-span-4"},Me={class:"relative mt-2"},Ne={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},$e={class:"shadow rounded-lg"},Se={class:"w-full text-sm text-left rtl:text-right text-gray-500"},je={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Oe={class:"border-b-2"},ze=["onClick"],Be={key:0},De={class:"px-4 py-2.5 min-w-36"},Le={class:"px-4 py-2.5 min-w-28"},Qe={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Te={class:"px-4 py-2.5"},He={class:"px-4 py-2.5 whitespace-nowrap"},Ue={class:"px-4 py-2.5"},Ee={class:"flex items-center space-x-2 min-w-36"},Fe={key:0,class:"px-4 py-2.5"},Ie={class:"items-center px-4 py-2.5"},Pe={class:"flex items-center justify-start gap-4"},We=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Ge=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),Re=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),Ye=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Ke=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),Ze=["onClick"],Je=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Xe=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),qe=[Je,Xe],et=["onClick"],tt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),st=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),ot=[tt,st],lt=e("button",{class:"w-full flex items-center justify-center px-4 py-2 bg-green-600 border border-green-600 rounded-md shadow-sm text-sm font-medium text-white hover:bg-green-700"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"})]),C(" Convert to Order ")],-1),at={key:1},nt=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),rt=[nt],it={class:"p-6"},dt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this quotation? ",-1),ct={class:"mt-6 flex justify-end"},xt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","isAdmin"],setup(r){const i=r,{form:A,search:y,sort:D,fetchData:ut,sortKey:L,sortDirection:Q}=de("quotations.index"),V=p(!1),O=p(null),T=N(()=>[{id:"",name:"All Agents"},...i.agents]),H=N(()=>[{id:"",name:"All Country"},...i.counties]),U=N(()=>[{id:"",name:"All Status"},...i.statusOptions]),E=[{field:"quotation_number",label:"QUOTATION NO",sortable:!0,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"lead.client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!0,visible:!0},{field:"qty",label:"QTY",sortable:!1,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:i.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],F=s=>{O.value=s,V.value=!0},M=()=>{V.value=!1},I=()=>{A.delete(route("quotations.destroy",{quotation:O.value}),{onSuccess:()=>M()})},P=s=>({pending:"bg-blue-100 text-blue-800",quotation_ready:"bg-yellow-100 text-yellow-800",order_placed:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",u=p(i.agent_id||""),m=p(i.county_id||""),_=p(i.status||""),w=p("");p({});const W=(s,o)=>{u.value=s,b(w.value,u.value,m.value,_.value)},G=(s,o)=>{m.value=s,b(w.value,u.value,m.value,_.value)},R=(s,o)=>{_.value=s,b(w.value,u.value,m.value,_.value)},b=(s,o,t,h)=>{w.value=s;const f=o===""?null:o,Z=t===""?null:t,J=h===""?null:h;A.get(route("quotations.index",{search:s,agent_id:f,county_id:Z,status:J}),{preserveState:!0})},Y=s=>{window.open(route("quotations.pdf",s),"_blank")};function K(s){return[{label:"1",qty:(s==null?void 0:s.qty_1)??null,price:(s==null?void 0:s.price_qty_1)??null},{label:"2",qty:(s==null?void 0:s.qty_2)??null,price:(s==null?void 0:s.price_qty_2)??null},{label:"3",qty:(s==null?void 0:s.qty_3)??null,price:(s==null?void 0:s.price_qty_3)??null},{label:"4",qty:(s==null?void 0:s.qty_4)??null,price:(s==null?void 0:s.price_qty_4)??null}].filter(t=>t.qty!=null).map(t=>t.price!=null?`Qty ${t.label}: ${t.qty} - £ ${t.price}`:`Qty ${t.label}: ${t.qty}`).join(", ")||"N/A"}return(s,o)=>(a(),d($,null,[l(g(X),{title:"Quotations"}),l(oe,null,{default:n(()=>[e("div",ue,[e("div",me,[_e,e("div",he,[e("div",pe,[ge,z(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>ee(y)?y.value=t:null),onInput:o[1]||(o[1]=t=>b(g(y),u.value,m.value,_.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for quotations..."},null,544),[[q,g(y)]])])])]),e("div",ve,[e("div",fe,[e("div",ye,[we,l(k,{for:"filters",value:"Filters"})])]),e("div",be,[i.isAdmin?(a(),d("div",xe,[l(k,{for:"agent_filter",value:"Agents"}),e("div",ke,[l(j,{options:T.value,modelValue:u.value,"onUpdate:modelValue":o[2]||(o[2]=t=>u.value=t),onOnchange:W},null,8,["options","modelValue"])])])):v("",!0),e("div",Ce,[l(k,{for:"county_filter",value:"Country"}),e("div",Ae,[l(j,{options:H.value,modelValue:m.value,"onUpdate:modelValue":o[3]||(o[3]=t=>m.value=t),onOnchange:G},null,8,["options","modelValue"])])]),e("div",Ve,[l(k,{for:"status_filter",value:"Status"}),e("div",Me,[l(j,{options:U.value,modelValue:_.value,"onUpdate:modelValue":o[4]||(o[4]=t=>_.value=t),onOnchange:R},null,8,["options","modelValue"])])])])]),e("div",Ne,[e("div",$e,[e("table",Se,[e("thead",je,[e("tr",Oe,[(a(),d($,null,B(E,(t,h)=>z(e("th",{key:h,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:f=>g(D)(t.field,t.sortable)},[C(c(t.label)+" ",1),t.sortable?(a(),x(ce,{key:0,isSorted:g(L)===t.field,direction:g(Q)},null,8,["isSorted","direction"])):v("",!0)],8,ze),[[te,t.visible]])),64))])]),r.data.data&&r.data.data.length>0?(a(),d("tbody",Be,[(a(!0),d($,null,B(r.data.data,t=>{var h;return a(),d("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",De,c(t.quotation_number),1),e("td",Le,c(t.lead?t.lead.lead_number:"N/A"),1),e("td",Qe,c((h=t.lead)==null?void 0:h.client_name),1),e("td",Te,c(t.county?t.county.name:"N/A"),1),e("td",He,c(K(t.lead)),1),e("td",Ue,[e("div",Ee,[e("span",{class:se(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",P(t.status)])},c(t.status.replace(/_/g," ").replace(/\b\w/g,f=>f.toUpperCase())),3)])]),i.isAdmin?(a(),d("td",Fe,c(t.lead.creator?t.lead.creator.first_name:"N/A"),1)):v("",!0),e("td",Ie,[e("div",Pe,[l(le,{align:"right",width:"48"},{trigger:n(()=>[We]),content:n(()=>[l(S,{href:s.route("quotations.show",{quotation:t.id})},{svg:n(()=>[Ge]),text:n(()=>[Re]),_:2},1032,["href"]),r.permissions.canEditQuotation&&t.status!=="order_placed"?(a(),x(S,{key:0,href:s.route("quotations.edit",{quotation:t.id})},{svg:n(()=>[Ye]),text:n(()=>[Ke]),_:2},1032,["href"])):v("",!0),r.permissions.canDeleteQuotation&&t.status!=="order_placed"?(a(),d("button",{key:1,type:"button",onClick:f=>F(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},qe,8,Ze)):v("",!0),e("button",{type:"button",onClick:f=>Y(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ot,8,et),t.status==="quotation_ready"?(a(),x(S,{key:2,href:s.route("orders.convert",{quotation:t.id}),class:"w-full"},{svg:n(()=>[lt]),_:2},1032,["href"])):v("",!0)]),_:2},1024)])])])}),128))])):(a(),d("tbody",at,rt))])])]),r.data.data&&r.data.data.length>0?(a(),x(ie,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):v("",!0)]),l(ae,{show:V.value,onClose:M},{default:n(()=>[e("div",it,[dt,e("div",ct,[l(ne,{onClick:M},{default:n(()=>[C("Cancel")]),_:1}),l(re,{class:"ml-3",onClick:I,disabled:g(A).processing},{default:n(()=>[C(" Delete Quotation ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{xt as default};
