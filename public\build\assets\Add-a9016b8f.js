import{T as E,r as f,w as B,b as c,d as y,e as s,u as a,f as k,F as D,Z as L,g as e,t as i,k as A,i as p,q as R}from"./app-2423a674.js";import{_ as j,a as O}from"./AdminLayout-2a78c5bd.js";import{_ as m,a as u}from"./TextInput-9557f155.js";import{_ as n}from"./InputLabel-b1d75d88.js";import{P as z}from"./PrimaryButton-0654e70f.js";import{_ as M}from"./TextArea-3440cfc5.js";import{_ as h}from"./Checkbox-49fbbb17.js";import"./_plugin-vue_export-helper-c27b6911.js";const Z={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},G={class:"text-2xl font-semibold leading-7 text-gray-900"},H={key:0,class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},J={class:"text-sm text-blue-800"},K=e("strong",null,"Converting from Lead:",-1),W=["onSubmit"],X={class:"border-b border-gray-900/10 pb-12"},ee={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},te={class:"sm:col-span-12"},se=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Lead Information",-1),le={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},oe=e("p",{class:"text-sm font-medium text-gray-500"},"Client Name",-1),ae={class:"text-sm text-gray-900"},de=e("p",{class:"text-sm font-medium text-gray-500"},"County",-1),ie={class:"text-sm text-gray-900"},ne=e("p",{class:"text-sm font-medium text-gray-500"},"Dimensions",-1),re={class:"text-sm text-gray-900"},ue=e("p",{class:"text-sm font-medium text-gray-500"},"Open Size",-1),_e={class:"text-sm text-gray-900"},me=e("p",{class:"text-sm font-medium text-gray-500"},"Box Style",-1),ce={class:"text-sm text-gray-900"},ye=e("p",{class:"text-sm font-medium text-gray-500"},"Stock",-1),pe={class:"text-sm text-gray-900"},ge=e("p",{class:"text-sm font-medium text-gray-500"},"Lamination",-1),qe={class:"text-sm text-gray-900"},ve=e("p",{class:"text-sm font-medium text-gray-500"},"Printing",-1),xe={class:"text-sm text-gray-900"},fe={key:0},be=e("p",{class:"text-sm font-medium text-gray-500"},"Add-ons",-1),he={class:"text-sm text-gray-900"},Ve=e("div",{class:"sm:col-span-12 mt-6"},[e("h3",{class:"text-lg font-bold text-gray-900"},"Quantities and Pricing")],-1),ke={class:"sm:col-span-3"},Qe={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Ue={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Te={class:"sm:col-span-3"},we={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Ce={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Fe={class:"sm:col-span-3"},Se={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},Ie={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},$e={class:"sm:col-span-3"},Pe={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ye={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Ae={class:"sm:col-span-12"},Ne={class:"bg-gray-50 p-4 rounded-lg"},Ee={class:"text-lg font-semibold text-gray-900"},Be={class:"sm:col-span-12"},De={class:"flex mt-6 items-center justify-between"},Le={class:"ml-auto flex items-center justify-end gap-x-6"},Re=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Ke={__name:"Add",props:{counties:{type:Array,required:!0},lead:{type:Object,required:!0}},setup(d){var T,w,C,F,S,I,$,P;const _=d,t=E({lead_id:((T=_.lead)==null?void 0:T.id)||null,qty_1:((w=_.lead)==null?void 0:w.qty_1)||"",qty_2:((C=_.lead)==null?void 0:C.qty_2)||"",qty_3:((F=_.lead)==null?void 0:F.qty_3)||"",qty_4:((S=_.lead)==null?void 0:S.qty_4)||"",price_qty_1:"",price_qty_2:"",price_qty_3:"",price_qty_4:"",notes:"",valid_until:"",documents:[]}),g=f(!0),q=f(!!((I=_.lead)!=null&&I.qty_2)),v=f(!!(($=_.lead)!=null&&$.qty_3)),x=f(!!((P=_.lead)!=null&&P.qty_4)),Q=f(0),N=()=>{t.post(route("quotations.store"),{preserveScroll:!0,onSuccess:()=>t.reset()})},U=()=>{let r=0;g.value&&t.qty_1&&t.price_qty_1&&(r+=parseFloat(t.qty_1)*parseFloat(t.price_qty_1)),q.value&&t.qty_2&&t.price_qty_2&&(r+=parseFloat(t.qty_2)*parseFloat(t.price_qty_2)),v.value&&t.qty_3&&t.price_qty_3&&(r+=parseFloat(t.qty_3)*parseFloat(t.price_qty_3)),x.value&&t.qty_4&&t.price_qty_4&&(r+=parseFloat(t.qty_4)*parseFloat(t.price_qty_4)),Q.value=r},b=(r,l)=>{l||(t[`qty_${r}`]="",t[`price_qty_${r}`]=""),U()};B([()=>t.qty_1,()=>t.price_qty_1,()=>g.value,()=>t.qty_2,()=>t.price_qty_2,()=>q.value,()=>t.qty_3,()=>t.price_qty_3,()=>v.value,()=>t.qty_4,()=>t.price_qty_4,()=>x.value],U);const V=new Date;return V.setDate(V.getDate()+30),t.valid_until=V.toISOString().split("T")[0],(r,l)=>(c(),y(D,null,[s(a(L),{title:"Add Quotation"}),s(j,null,{default:k(()=>{var Y;return[e("div",Z,[e("h2",G,i(d.lead?"Convert Lead to Quotation":"Add New Quotation"),1),d.lead?(c(),y("div",H,[e("p",J,[K,A(" "+i(d.lead.lead_number)+" - "+i(d.lead.client_name),1)])])):p("",!0),e("form",{onSubmit:R(N,["prevent"])},[e("div",X,[e("div",ee,[e("div",te,[se,e("div",le,[e("div",null,[oe,e("p",ae,i(d.lead.client_name),1)]),e("div",null,[de,e("p",ie,i(((Y=d.lead.county)==null?void 0:Y.name)||"N/A"),1)]),e("div",null,[ne,e("p",re,i(d.lead.dimensions),1)]),e("div",null,[ue,e("p",_e,i(d.lead.open_size),1)]),e("div",null,[me,e("p",ce,i(d.lead.box_style),1)]),e("div",null,[ye,e("p",pe,i(d.lead.stock),1)]),e("div",null,[ge,e("p",qe,i(d.lead.lamination),1)]),e("div",null,[ve,e("p",xe,i(d.lead.printing),1)]),d.lead.add_ons?(c(),y("div",fe,[be,e("p",he,i(d.lead.add_ons),1)])):p("",!0)])]),Ve,e("div",ke,[e("div",Qe,[s(h,{checked:g.value,"onUpdate:checked":[l[0]||(l[0]=o=>g.value=o),l[1]||(l[1]=o=>b(1,o))]},null,8,["checked"]),s(n,{value:"Include Quantity 1",class:"text-base font-medium text-blue-800"})]),g.value?(c(),y("div",Ue,[e("div",null,[s(n,{for:"qty_1",value:"QTY 1 *"}),s(m,{id:"qty_1",type:"number",modelValue:a(t).qty_1,"onUpdate:modelValue":l[2]||(l[2]=o=>a(t).qty_1=o),min:"1",required:""},null,8,["modelValue"]),s(u,{message:a(t).errors.qty_1},null,8,["message"])]),e("div",null,[s(n,{for:"price_qty_1",value:"PRICE QTY 1 *"}),s(m,{id:"price_qty_1",type:"number",step:"0.01",modelValue:a(t).price_qty_1,"onUpdate:modelValue":l[3]||(l[3]=o=>a(t).price_qty_1=o),min:"0"},null,8,["modelValue"]),s(u,{message:a(t).errors.price_qty_1},null,8,["message"])])])):p("",!0)]),e("div",Te,[e("div",we,[s(h,{checked:q.value,"onUpdate:checked":[l[4]||(l[4]=o=>q.value=o),l[5]||(l[5]=o=>b(2,o))]},null,8,["checked"]),s(n,{value:"Include Quantity 2",class:"text-base font-medium text-green-800"})]),q.value?(c(),y("div",Ce,[e("div",null,[s(n,{for:"qty_2",value:"QTY 2"}),s(m,{id:"qty_2",type:"number",modelValue:a(t).qty_2,"onUpdate:modelValue":l[6]||(l[6]=o=>a(t).qty_2=o),min:"1"},null,8,["modelValue"]),s(u,{message:a(t).errors.qty_2},null,8,["message"])]),e("div",null,[s(n,{for:"price_qty_2",value:"PRICE QTY 2"}),s(m,{id:"price_qty_2",type:"number",step:"0.01",modelValue:a(t).price_qty_2,"onUpdate:modelValue":l[7]||(l[7]=o=>a(t).price_qty_2=o),min:"0"},null,8,["modelValue"]),s(u,{message:a(t).errors.price_qty_2},null,8,["message"])])])):p("",!0)]),e("div",Fe,[e("div",Se,[s(h,{checked:v.value,"onUpdate:checked":[l[8]||(l[8]=o=>v.value=o),l[9]||(l[9]=o=>b(3,o))]},null,8,["checked"]),s(n,{value:"Include Quantity 3",class:"text-base font-medium text-yellow-800"})]),v.value?(c(),y("div",Ie,[e("div",null,[s(n,{for:"qty_3",value:"QTY 3"}),s(m,{id:"qty_3",type:"number",modelValue:a(t).qty_3,"onUpdate:modelValue":l[10]||(l[10]=o=>a(t).qty_3=o),min:"1"},null,8,["modelValue"]),s(u,{message:a(t).errors.qty_3},null,8,["message"])]),e("div",null,[s(n,{for:"price_qty_3",value:"PRICE QTY 3"}),s(m,{id:"price_qty_3",type:"number",step:"0.01",modelValue:a(t).price_qty_3,"onUpdate:modelValue":l[11]||(l[11]=o=>a(t).price_qty_3=o),min:"0"},null,8,["modelValue"]),s(u,{message:a(t).errors.price_qty_3},null,8,["message"])])])):p("",!0)]),e("div",$e,[e("div",Pe,[s(h,{checked:x.value,"onUpdate:checked":[l[12]||(l[12]=o=>x.value=o),l[13]||(l[13]=o=>b(4,o))]},null,8,["checked"]),s(n,{value:"Include Quantity 4",class:"text-base font-medium text-purple-800"})]),x.value?(c(),y("div",Ye,[e("div",null,[s(n,{for:"qty_4",value:"QTY 4"}),s(m,{id:"qty_4",type:"number",modelValue:a(t).qty_4,"onUpdate:modelValue":l[14]||(l[14]=o=>a(t).qty_4=o),min:"1"},null,8,["modelValue"]),s(u,{message:a(t).errors.qty_4},null,8,["message"])]),e("div",null,[s(n,{for:"price_qty_4",value:"PRICE QTY 4"}),s(m,{id:"price_qty_4",type:"number",step:"0.01",modelValue:a(t).price_qty_4,"onUpdate:modelValue":l[15]||(l[15]=o=>a(t).price_qty_4=o),min:"0"},null,8,["modelValue"]),s(u,{message:a(t).errors.price_qty_4},null,8,["message"])])])):p("",!0)]),e("div",Ae,[e("div",Ne,[e("p",Ee," Estimated Total: $"+i(Q.value.toFixed(2)),1)])]),e("div",Be,[s(n,{for:"notes",value:"Notes"}),s(M,{id:"notes",modelValue:a(t).notes,"onUpdate:modelValue":l[16]||(l[16]=o=>a(t).notes=o),rows:"4"},null,8,["modelValue"]),s(u,{message:a(t).errors.notes},null,8,["message"])])])]),e("div",De,[e("div",Le,[s(O,{href:r.route("quotations.index")},{svg:k(()=>[Re]),_:1},8,["href"]),s(z,{disabled:a(t).processing},{default:k(()=>[A(" Save ")]),_:1},8,["disabled"])])])],40,W)])]}),_:1})],64))}};export{Ke as default};
