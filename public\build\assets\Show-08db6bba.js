import{_ as K,a as m}from"./AdminLayout-f9e7be5b.js";import{P as R}from"./PrimaryButton-a6d2214f.js";import{b as o,d as a,e as d,u as W,f as c,F as $,Z as X,g as t,t as s,k as u,i,n as Y,j as tt}from"./app-5d829b53.js";import"./_plugin-vue_export-helper-c27b6911.js";const et={class:"animate-top"},st={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},ot={class:"text-3xl font-bold text-gray-900"},at=t("p",{class:"text-gray-600 mt-1"},"Quotation Details",-1),it={class:"flex flex-col sm:flex-row gap-3"},nt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),lt={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},dt={class:"lg:col-span-2 space-y-8"},ct={class:"bg-white shadow rounded-lg p-6"},rt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Lead Information",-1),ut={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},xt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),mt={class:"mt-1 text-sm text-gray-700"},ht=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),yt={class:"mt-1 text-sm text-gray-700"},gt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Number",-1),_t={class:"mt-1 text-sm text-gray-700"},bt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Status",-1),ft={class:"mt-1 text-sm text-gray-700"},wt={class:"bg-white shadow rounded-lg p-6"},qt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),vt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},kt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),pt={class:"mt-1 text-sm text-gray-700"},Lt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),Nt={class:"mt-1 text-sm text-gray-700"},At=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),Ct={class:"mt-1 text-sm text-gray-700"},St=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),Bt={class:"mt-1 text-sm text-gray-700"},jt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),Dt={class:"mt-1 text-sm text-gray-700"},Vt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),Pt={class:"mt-1 text-sm text-gray-700"},zt={key:0,class:"md:col-span-2"},Qt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),It={class:"mt-1 text-sm text-gray-700"},Mt={class:"bg-white shadow rounded-lg p-6"},Ut=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quotation Information",-1),Ft={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Et=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),Ht=t("label",{class:"block text-sm font-semibold text-gray-900"},"Valid Until",-1),Tt={class:"mt-1 text-sm text-gray-700"},$t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),Gt={class:"mt-1 text-sm text-gray-700"},Ot=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created Date",-1),Zt={class:"mt-1 text-sm text-gray-700"},Jt={class:"bg-white shadow rounded-lg p-6"},Kt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Pricing Details",-1),Rt={class:"overflow-x-auto"},Wt={class:"min-w-full divide-y divide-gray-200"},Xt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Unit Price"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Total")])],-1),Yt={class:"bg-white divide-y divide-gray-200"},te={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ee={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},se={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},oe={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ae={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ie={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},ne={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},le={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},de={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},ce={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},re={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ue={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},xe={key:0,class:"bg-white shadow rounded-lg p-6"},me=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),he={class:"text-sm text-gray-700 whitespace-pre-wrap"},ye={class:"space-y-6"},ge={class:"bg-white shadow rounded-lg p-6"},_e=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),be={class:"space-y-3 w-full"},fe=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),u(" Edit Quotation ")],-1),we=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),qe=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),u(" Back to List ")],-1),ve={key:0,class:"bg-white shadow rounded-lg p-6"},ke=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Documents",-1),pe={class:"space-y-3"},Le={class:"flex items-center space-x-3"},Ne=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1),Ae={class:"text-sm text-gray-700"},Ce=["href"],Se={class:"bg-white shadow rounded-lg p-6"},Be=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),je={class:"space-y-4"},De={class:"flex items-start space-x-3"},Ve=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),Pe=t("p",{class:"text-sm font-semibold text-gray-900"},"Quotation Created",-1),ze={class:"text-xs text-gray-500"},Qe={key:0,class:"flex items-start space-x-3"},Ie=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),Me=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),Ue={class:"text-xs text-gray-500"},$e={__name:"Show",props:{quotation:{type:Object,required:!0}},setup(e){const G=e,O=n=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",accepted:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",expired:"bg-yellow-100 text-yellow-800"})[n]||"bg-gray-100 text-gray-800",Z=()=>{window.open(route("quotations.pdf",G.quotation.id),"_blank")},r=n=>n?new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",l=n=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(n);return(n,J)=>(o(),a($,null,[d(W(X),{title:`Quotation - ${e.quotation.quotation_number}`},null,8,["title"]),d(K,null,{default:c(()=>{var h,y,g,_,b,f,w,q,v,k,p,L,N,A,C,S,B,j,D,V,P,z,Q,I,M,U,F,E,H,T;return[t("div",et,[t("div",st,[t("div",null,[t("h1",ot,s(e.quotation.quotation_number),1),at]),t("div",it,[d(m,{href:n.route("quotations.edit",e.quotation.id)},{svg:c(()=>[d(R,{class:"w-full items-center"},{default:c(()=>[nt,u(" Edit Quotation ")]),_:1})]),_:1},8,["href"])])]),t("div",lt,[t("div",dt,[t("div",ct,[rt,t("div",ut,[t("div",null,[xt,t("p",mt,s(((h=e.quotation.lead)==null?void 0:h.client_name)||"N/A"),1)]),t("div",null,[ht,t("p",yt,s(((g=(y=e.quotation.lead)==null?void 0:y.county)==null?void 0:g.name)||"N/A"),1)]),t("div",null,[gt,t("p",_t,s(((_=e.quotation.lead)==null?void 0:_.lead_number)||"N/A"),1)]),t("div",null,[bt,t("p",ft,s(((b=e.quotation.lead)==null?void 0:b.status)||"N/A"),1)])])]),t("div",wt,[qt,t("div",vt,[t("div",null,[kt,t("p",pt,s(((f=e.quotation.lead)==null?void 0:f.dimensions)||"N/A"),1)]),t("div",null,[Lt,t("p",Nt,s(((w=e.quotation.lead)==null?void 0:w.open_size)||"N/A"),1)]),t("div",null,[At,t("p",Ct,s(((q=e.quotation.lead)==null?void 0:q.box_style)||"N/A"),1)]),t("div",null,[St,t("p",Bt,s(((v=e.quotation.lead)==null?void 0:v.stock)||"N/A"),1)]),t("div",null,[jt,t("p",Dt,s(((k=e.quotation.lead)==null?void 0:k.lamination)||"N/A"),1)]),t("div",null,[Vt,t("p",Pt,s(((p=e.quotation.lead)==null?void 0:p.printing)||"N/A"),1)]),(L=e.quotation.lead)!=null&&L.add_ons?(o(),a("div",zt,[Qt,t("p",It,s(e.quotation.lead.add_ons),1)])):i("",!0)])]),t("div",Mt,[Ut,t("div",Ft,[t("div",null,[Et,t("span",{class:Y(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",O(e.quotation.status)])},s(e.quotation.status.charAt(0).toUpperCase()+e.quotation.status.slice(1)),3)]),t("div",null,[Ht,t("p",Tt,s(r(e.quotation.valid_until)),1)]),t("div",null,[$t,t("p",Gt,s((N=e.quotation.creator)==null?void 0:N.first_name)+" "+s((A=e.quotation.creator)==null?void 0:A.last_name),1)]),t("div",null,[Ot,t("p",Zt,s(r(e.quotation.created_at)),1)])])]),t("div",Jt,[Kt,t("div",Rt,[t("table",Wt,[Xt,t("tbody",Yt,[t("tr",null,[(C=e.quotation.lead)!=null&&C.qty_1?(o(),a("td",te,s(parseInt((S=e.quotation.lead)==null?void 0:S.qty_1).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_1?(o(),a("td",ee,s(l(e.quotation.price_qty_1)),1)):i("",!0),(B=e.quotation.lead)!=null&&B.qty_1&&e.quotation.price_qty_1?(o(),a("td",se,s(l(((j=e.quotation.lead)==null?void 0:j.qty_1)*e.quotation.price_qty_1)),1)):i("",!0)]),t("tr",null,[(D=e.quotation.lead)!=null&&D.qty_2?(o(),a("td",oe,s(parseInt((V=e.quotation.lead)==null?void 0:V.qty_2).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_2?(o(),a("td",ae,s(l(e.quotation.price_qty_2)),1)):i("",!0),(P=e.quotation.lead)!=null&&P.qty_2&&e.quotation.price_qty_2?(o(),a("td",ie,s(l(((z=e.quotation.lead)==null?void 0:z.qty_2)*e.quotation.price_qty_2)),1)):i("",!0)]),t("tr",null,[(Q=e.quotation.lead)!=null&&Q.qty_3?(o(),a("td",ne,s(parseInt((I=e.quotation.lead)==null?void 0:I.qty_3).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_3?(o(),a("td",le,s(l(e.quotation.price_qty_3)),1)):i("",!0),(M=e.quotation.lead)!=null&&M.qty_3&&e.quotation.price_qty_3?(o(),a("td",de,s(l(((U=e.quotation.lead)==null?void 0:U.qty_3)*e.quotation.price_qty_3)),1)):i("",!0)]),t("tr",null,[(F=e.quotation.lead)!=null&&F.qty_4?(o(),a("td",ce,s(parseInt((E=e.quotation.lead)==null?void 0:E.qty_4).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_4?(o(),a("td",re,s(l(e.quotation.price_qty_4)),1)):i("",!0),(H=e.quotation.lead)!=null&&H.qty_4&&e.quotation.price_qty_4?(o(),a("td",ue,s(l(((T=e.quotation.lead)==null?void 0:T.qty_4)*e.quotation.price_qty_4)),1)):i("",!0)])])])])]),e.quotation.notes?(o(),a("div",xe,[me,t("p",he,s(e.quotation.notes),1)])):i("",!0)]),t("div",ye,[t("div",ge,[_e,t("div",be,[d(m,{href:n.route("quotations.edit",e.quotation.id),class:"w-full"},{svg:c(()=>[fe]),_:1},8,["href"]),t("div",{class:"px-3 py-2"},[t("button",{onClick:Z,class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[we,u(" Download PDF ")])]),d(m,{href:n.route("quotations.index"),class:"w-full"},{svg:c(()=>[qe]),_:1},8,["href"])])]),e.quotation.documents&&e.quotation.documents.length>0?(o(),a("div",ve,[ke,t("div",pe,[(o(!0),a($,null,tt(e.quotation.documents,x=>(o(),a("div",{key:x.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",Le,[Ne,t("span",Ae,s(x.orignal_name),1)]),t("a",{href:"/uploads/leads/"+x.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,Ce)]))),128))])])):i("",!0),t("div",Se,[Be,t("div",je,[t("div",De,[Ve,t("div",null,[Pe,t("p",ze,s(r(e.quotation.created_at)),1)])]),e.quotation.updated_at!==e.quotation.created_at?(o(),a("div",Qe,[Ie,t("div",null,[Me,t("p",Ue,s(r(e.quotation.updated_at)),1)])])):i("",!0)])])])])])]}),_:1})],64))}};export{$e as default};
