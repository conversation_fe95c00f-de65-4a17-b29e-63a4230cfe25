<template>
    <Head title="Create Task" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Create New Task</h2>
                    <p class="text-sm text-gray-600 mt-1">Schedule a follow-up or reminder</p>
                </div>
                <Link :href="route('tasks.index')"
                      class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700">
                    ← Back to Tasks
                </Link>
            </div>

            <!-- Form -->
            <form @submit.prevent="submit" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Title -->
                    <div class="md:col-span-2">
                        <InputLabel for="title" value="Task Title *" />
                        <TextInput
                            id="title"
                            type="text"
                            v-model="form.title"
                            class="mt-1 block w-full"
                            placeholder="e.g., Call John about quotation follow-up"
                            required
                        />
                        <InputError :message="form.errors.title" />
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <InputLabel for="description" value="Description" />
                        <textarea
                            id="description"
                            v-model="form.description"
                            rows="3"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            placeholder="Additional details about the task..."
                        ></textarea>
                        <InputError :message="form.errors.description" />
                    </div>

                    <!-- Type -->
                    <div>
                        <InputLabel for="type" value="Task Type *" />
                        <select
                            id="type"
                            v-model="form.type"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required
                        >
                            <option value="">Select Type</option>
                            <option value="call">📞 Phone Call</option>
                            <option value="follow_up">🔄 Follow Up</option>
                            <option value="meeting">🤝 Meeting</option>
                            <option value="email">📧 Email</option>
                            <option value="quote_follow_up">💰 Quote Follow Up</option>
                            <option value="order_follow_up">📦 Order Follow Up</option>
                            <option value="general">📋 General Task</option>
                            <option value="reminder">⏰ Reminder</option>
                        </select>
                        <InputError :message="form.errors.type" />
                    </div>

                    <!-- Priority -->
                    <div>
                        <InputLabel for="priority" value="Priority *" />
                        <select
                            id="priority"
                            v-model="form.priority"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required
                        >
                            <option value="low">🟢 Low</option>
                            <option value="medium" selected>🟡 Medium</option>
                            <option value="high">🟠 High</option>
                            <option value="urgent">🔴 Urgent</option>
                        </select>
                        <InputError :message="form.errors.priority" />
                    </div>

                    <!-- Due Date -->
                    <div>
                        <InputLabel for="due_date" value="Due Date & Time *" />
                        <TextInput
                            id="due_date"
                            type="datetime-local"
                            v-model="form.due_date"
                            class="mt-1 block w-full"
                            required
                        />
                        <InputError :message="form.errors.due_date" />
                    </div>

                    <!-- Reminder Date -->
                    <div>
                        <InputLabel for="reminder_date" value="Reminder Date & Time" />
                        <TextInput
                            id="reminder_date"
                            type="datetime-local"
                            v-model="form.reminder_date"
                            class="mt-1 block w-full"
                        />
                        <InputError :message="form.errors.reminder_date" />
                        <p class="text-xs text-gray-500 mt-1">Optional: Get reminded before the due date</p>
                    </div>

                    <!-- Assigned To -->
                    <div>
                        <InputLabel for="assigned_to" value="Assign To *" />
                        <select
                            id="assigned_to"
                            v-model="form.assigned_to"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required
                        >
                            <option value="">Select User</option>
                            <option v-for="user in users" :key="user.id" :value="user.id">
                                {{ user.first_name }} {{ user.last_name }}
                            </option>
                        </select>
                        <InputError :message="form.errors.assigned_to" />
                    </div>

                    <!-- Related Entity (if provided) -->
                    <div v-if="relatedEntity" class="md:col-span-2">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">Related to:</h4>
                            <div class="text-sm text-blue-800">
                                <span v-if="relatedEntity.client_name">
                                    📋 Lead: {{ relatedEntity.client_name }} ({{ relatedEntity.company_name }})
                                </span>
                                <span v-else-if="relatedEntity.quotation_number">
                                    💰 Quotation: {{ relatedEntity.quotation_number }}
                                </span>
                                <span v-else-if="relatedEntity.order_number">
                                    📦 Order: {{ relatedEntity.order_number }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <InputLabel for="notes" value="Additional Notes" />
                        <textarea
                            id="notes"
                            v-model="form.notes"
                            rows="2"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            placeholder="Any additional notes or instructions..."
                        ></textarea>
                        <InputError :message="form.errors.notes" />
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Actions:</h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <button type="button" @click="setQuickTask('call', 'Call client', 'high')"
                                class="px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                            📞 Schedule Call
                        </button>
                        <button type="button" @click="setQuickTask('follow_up', 'Follow up on quotation', 'medium')"
                                class="px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200">
                            💰 Quote Follow-up
                        </button>
                        <button type="button" @click="setQuickTask('meeting', 'Schedule meeting', 'high')"
                                class="px-3 py-2 text-xs bg-purple-100 text-purple-800 rounded-md hover:bg-purple-200">
                            🤝 Schedule Meeting
                        </button>
                        <button type="button" @click="setQuickTask('reminder', 'Follow up reminder', 'low')"
                                class="px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200">
                            ⏰ Set Reminder
                        </button>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-3">
                    <Link :href="route('tasks.index')"
                          class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400">
                        Cancel
                    </Link>
                    <button type="submit" :disabled="form.processing"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 disabled:opacity-50">
                        <span v-if="form.processing">Creating...</span>
                        <span v-else>Create Task</span>
                    </button>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref } from 'vue'
import { Head, Link, useForm } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import InputLabel from '@/Components/InputLabel.vue'
import TextInput from '@/Components/TextInput.vue'
import InputError from '@/Components/InputError.vue'

const props = defineProps({
    users: Array,
    relatedEntity: Object
})

const form = useForm({
    title: '',
    description: '',
    type: '',
    priority: 'medium',
    due_date: '',
    reminder_date: '',
    assigned_to: '',
    taskable_type: props.relatedEntity ? props.relatedEntity.constructor.name : 'lead',
    taskable_id: props.relatedEntity ? props.relatedEntity.id : '1',
    notes: ''
})

// Set default due date to tomorrow 9 AM
const tomorrow = new Date()
tomorrow.setDate(tomorrow.getDate() + 1)
tomorrow.setHours(9, 0, 0, 0)
form.due_date = tomorrow.toISOString().slice(0, 16)

// Set default reminder to 1 hour before due date
const reminderTime = new Date(tomorrow)
reminderTime.setHours(8, 0, 0, 0)
form.reminder_date = reminderTime.toISOString().slice(0, 16)

const setQuickTask = (type, title, priority) => {
    form.type = type
    form.title = title
    form.priority = priority
}

const submit = () => {
    form.post(route('tasks.store'), {
        preserveScroll: true,
    })
}
</script>
