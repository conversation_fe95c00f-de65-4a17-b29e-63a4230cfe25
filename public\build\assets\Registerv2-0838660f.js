import{z as i,b as r,d as _,g as e,e as s,f as l,u as n,x as p,Z as d,p as u,l as h}from"./app-631e032c.js";import{_ as f}from"./_plugin-vue_export-helper-c27b6911.js";const o=t=>(u("data-v-2c382a18"),t=t(),h(),t),g={class:"min-h-screen loginview flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100"},m={class:"container"},v=o(()=>e("h2",null,"Access Denied",-1)),x=o(()=>e("p",null,"You do not have permission to access this page/event. ",-1)),y=o(()=>e("p",null,"Please contact your administrator if you believe this is an error.",-1)),R=["href"],w={__name:"Registerv2",setup(t){return(a,I)=>{const c=i("ApplicationLogo");return r(),_("div",g,[e("div",null,[s(n(p),{href:"/"},{default:l(()=>[s(c,{class:"w-60 fill-current text-gray-500"})]),_:1})]),s(n(d),{title:"Register"}),e("div",m,[v,x,y,e("h4",null,[e("a",{href:a.route("login")},"Return to Login",8,R)])])])}}},B=f(w,[["__scopeId","data-v-2c382a18"]]);export{B as default};
