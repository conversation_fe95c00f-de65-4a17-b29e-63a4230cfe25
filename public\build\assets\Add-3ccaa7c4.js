import{T as E,r as q,w as H,b as u,d as m,e as l,u as d,f as C,F as L,Z,g as e,k as j,t as i,i as _,q as G}from"./app-2423a674.js";import{_ as J,a as K}from"./AdminLayout-2a78c5bd.js";import{_ as x,a as y}from"./TextInput-9557f155.js";import{_ as c}from"./InputLabel-b1d75d88.js";import{P as R}from"./PrimaryButton-0654e70f.js";import{_ as W}from"./TextArea-3440cfc5.js";import{_ as k}from"./Checkbox-49fbbb17.js";import"./_plugin-vue_export-helper-c27b6911.js";const X={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Y=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"}," Convert Quotation to Order ",-1),ee={class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},te={class:"text-sm text-blue-800"},se=e("strong",null,"Converting from Quotation:",-1),le=e("div",{class:"sm:col-span-12 mt-6"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information")],-1),oe={class:"mt-6 p-4 bg-gray-50 rounded-lg"},ae={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},de=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1),ie={class:"text-sm text-gray-700"},ne=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1),ce={class:"text-sm text-gray-700"},re=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1),ue={class:"text-sm text-gray-700"},me=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1),_e={class:"text-sm text-gray-700"},ye=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1),qe={class:"text-sm text-gray-700"},xe=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1),ge={class:"text-sm text-gray-700"},ve=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1),pe={class:"text-sm text-gray-700"},be=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1),he={class:"text-sm text-gray-700"},fe={key:0},ke=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1),Ve={class:"text-sm text-gray-700"},Ce=["onSubmit"],we={class:"border-b border-gray-900/10 pb-12"},Qe={class:"mt-6 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ae=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4"},"Select Quantities for Order"),e("p",{class:"text-sm text-gray-600"},"Select the quantities that the client needs. Check the boxes and enter the required amounts:")],-1),Ne={key:0,class:"sm:col-span-3"},$e={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Oe={class:"grid grid-cols-1 gap-4"},Se={class:"text-sm text-gray-500 mt-1"},Fe={key:1,class:"sm:col-span-3"},Ue={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},De={class:"grid grid-cols-1 gap-4"},Be={class:"text-sm text-gray-500 mt-1"},Pe={key:2,class:"sm:col-span-3"},Me={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},ze={class:"grid grid-cols-1 gap-4"},Te={class:"text-sm text-gray-500 mt-1"},je={key:3,class:"sm:col-span-3"},Ie={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ee={class:"grid grid-cols-1 gap-4"},He={class:"text-sm text-gray-500 mt-1"},Le={key:4,class:"sm:col-span-12"},Ze={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Ge={class:"flex items-center justify-between"},Je={class:"text-lg font-semibold text-green-800"},Ke=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing from quotation ",-1),Re=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),We=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4 mb-4"},"Order Information")],-1),Xe={class:"sm:col-span-6"},Ye={class:"sm:col-span-12"},et={class:"flex mt-6 items-center justify-between"},tt={class:"ml-auto flex items-center justify-end gap-x-6"},st=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),ut={__name:"Add",props:{quotation:{type:Object,required:!0}},setup(t){var Q,A,N,$;const n=t,s=E({quotation_id:n.quotation.id,lead_id:n.quotation.lead_id,selected_qty_1:((Q=n.quotation.lead)==null?void 0:Q.qty_1)||"",selected_qty_2:((A=n.quotation.lead)==null?void 0:A.qty_2)||"",selected_qty_3:((N=n.quotation.lead)==null?void 0:N.qty_3)||"",selected_qty_4:(($=n.quotation.lead)==null?void 0:$.qty_4)||"",notes:"",expected_delivery:"",tracking_number:""}),g=q(!1),v=q(!1),p=q(!1),b=q(!1),h=q(0),I=()=>{s.post(route("orders.store"),{preserveScroll:!0,onSuccess:()=>s.reset()})},w=()=>{let r=0;g.value&&s.selected_qty_1&&n.quotation.price_qty_1&&(r+=parseFloat(s.selected_qty_1)*parseFloat(n.quotation.price_qty_1)),v.value&&s.selected_qty_2&&n.quotation.price_qty_2&&(r+=parseFloat(s.selected_qty_2)*parseFloat(n.quotation.price_qty_2)),p.value&&s.selected_qty_3&&n.quotation.price_qty_3&&(r+=parseFloat(s.selected_qty_3)*parseFloat(n.quotation.price_qty_3)),b.value&&s.selected_qty_4&&n.quotation.price_qty_4&&(r+=parseFloat(s.selected_qty_4)*parseFloat(n.quotation.price_qty_4)),h.value=r},f=(r,o)=>{w()};H([()=>s.selected_qty_1,()=>g.value,()=>s.selected_qty_2,()=>v.value,()=>s.selected_qty_3,()=>p.value,()=>s.selected_qty_4,()=>b.value],w);const V=new Date;return V.setDate(V.getDate()+7),s.expected_delivery=V.toISOString().split("T")[0],(r,o)=>(u(),m(L,null,[l(d(Z),{title:"Convert Quotation to Order"}),l(J,null,{default:C(()=>{var O,S,F,U,D,B,P,M,z,T;return[e("div",X,[Y,e("div",ee,[e("p",te,[se,j(" "+i(t.quotation.quotation_number),1)])]),le,e("div",oe,[e("div",ae,[e("div",null,[de,e("p",ie,i(((O=t.quotation.lead)==null?void 0:O.client_name)||"N/A"),1)]),e("div",null,[ne,e("p",ce,i(((F=(S=t.quotation.lead)==null?void 0:S.county)==null?void 0:F.name)||"N/A"),1)]),e("div",null,[re,e("p",ue,i(((U=t.quotation.lead)==null?void 0:U.dimensions)||"N/A"),1)]),e("div",null,[me,e("p",_e,i(((D=t.quotation.lead)==null?void 0:D.open_size)||"N/A"),1)]),e("div",null,[ye,e("p",qe,i(((B=t.quotation.lead)==null?void 0:B.box_style)||"N/A"),1)]),e("div",null,[xe,e("p",ge,i(((P=t.quotation.lead)==null?void 0:P.stock)||"N/A"),1)]),e("div",null,[ve,e("p",pe,i(((M=t.quotation.lead)==null?void 0:M.lamination)||"N/A"),1)]),e("div",null,[be,e("p",he,i(((z=t.quotation.lead)==null?void 0:z.printing)||"N/A"),1)]),(T=t.quotation.lead)!=null&&T.add_ons?(u(),m("div",fe,[ke,e("p",Ve,i(t.quotation.lead.add_ons),1)])):_("",!0)])]),e("form",{onSubmit:G(I,["prevent"])},[e("div",we,[e("div",Qe,[Ae,t.quotation.lead.qty_1&&t.quotation.price_qty_1?(u(),m("div",Ne,[e("div",$e,[l(k,{checked:g.value,"onUpdate:checked":[o[0]||(o[0]=a=>g.value=a),o[1]||(o[1]=a=>f(1,a))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",Oe,[e("div",null,[l(c,{for:"selected_qty_1",value:`Client Order Qty 1 (Available: ${t.quotation.lead.qty_1})`},null,8,["value"]),l(x,{id:"selected_qty_1",type:"number",modelValue:d(s).selected_qty_1,"onUpdate:modelValue":o[2]||(o[2]=a=>d(s).selected_qty_1=a),max:t.quotation.lead.qty_1,min:"1",placeholder:`Max: ${t.quotation.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(y,{message:d(s).errors.selected_qty_1},null,8,["message"]),e("p",Se,"Price: £"+i(t.quotation.price_qty_1)+" per unit",1)])])])):_("",!0),t.quotation.lead.qty_2&&t.quotation.price_qty_2?(u(),m("div",Fe,[e("div",Ue,[l(k,{checked:v.value,"onUpdate:checked":[o[3]||(o[3]=a=>v.value=a),o[4]||(o[4]=a=>f(2,a))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",De,[e("div",null,[l(c,{for:"selected_qty_2",value:`Client Order Qty 2 (Available: ${t.quotation.lead.qty_2})`},null,8,["value"]),l(x,{id:"selected_qty_2",type:"number",modelValue:d(s).selected_qty_2,"onUpdate:modelValue":o[5]||(o[5]=a=>d(s).selected_qty_2=a),max:t.quotation.lead.qty_2,min:"1",placeholder:`Max: ${t.quotation.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(y,{message:d(s).errors.selected_qty_2},null,8,["message"]),e("p",Be,"Price: £"+i(t.quotation.price_qty_2)+" per unit",1)])])])):_("",!0),t.quotation.lead.qty_3&&t.quotation.price_qty_3?(u(),m("div",Pe,[e("div",Me,[l(k,{checked:p.value,"onUpdate:checked":[o[6]||(o[6]=a=>p.value=a),o[7]||(o[7]=a=>f(3,a))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",ze,[e("div",null,[l(c,{for:"selected_qty_3",value:`Client Order Qty 3 (Available: ${t.quotation.lead.qty_3})`},null,8,["value"]),l(x,{id:"selected_qty_3",type:"number",modelValue:d(s).selected_qty_3,"onUpdate:modelValue":o[8]||(o[8]=a=>d(s).selected_qty_3=a),max:t.quotation.lead.qty_3,min:"1",placeholder:`Max: ${t.quotation.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(y,{message:d(s).errors.selected_qty_3},null,8,["message"]),e("p",Te,"Price: £"+i(t.quotation.price_qty_3)+" per unit",1)])])])):_("",!0),t.quotation.lead.qty_4&&t.quotation.price_qty_4?(u(),m("div",je,[e("div",Ie,[l(k,{checked:b.value,"onUpdate:checked":[o[9]||(o[9]=a=>b.value=a),o[10]||(o[10]=a=>f(4,a))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",Ee,[e("div",null,[l(c,{for:"selected_qty_4",value:`Client Order Qty 4 (Available: ${t.quotation.lead.qty_4})`},null,8,["value"]),l(x,{id:"selected_qty_4",type:"number",modelValue:d(s).selected_qty_4,"onUpdate:modelValue":o[11]||(o[11]=a=>d(s).selected_qty_4=a),max:t.quotation.lead.qty_4,min:"1",placeholder:`Max: ${t.quotation.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(y,{message:d(s).errors.selected_qty_4},null,8,["message"]),e("p",He,"Price: £"+i(t.quotation.price_qty_4)+" per unit",1)])])])):_("",!0),h.value>0?(u(),m("div",Le,[e("div",Ze,[e("div",Ge,[e("div",null,[e("p",Je," Order Total: £"+i(h.value.toFixed(2)),1),Ke]),Re])])])):_("",!0),We,e("div",Xe,[l(c,{for:"expected_delivery",value:"Expected Delivery Date"}),l(x,{id:"expected_delivery",type:"date",modelValue:d(s).expected_delivery,"onUpdate:modelValue":o[12]||(o[12]=a=>d(s).expected_delivery=a)},null,8,["modelValue"]),l(y,{message:d(s).errors.expected_delivery},null,8,["message"])]),e("div",Ye,[l(c,{for:"notes",value:"Order Notes"}),l(W,{id:"notes",modelValue:d(s).notes,"onUpdate:modelValue":o[13]||(o[13]=a=>d(s).notes=a),rows:"4",placeholder:"Any special instructions or notes for this order..."},null,8,["modelValue"]),l(y,{message:d(s).errors.notes},null,8,["message"])])])]),e("div",et,[e("div",tt,[l(K,{href:r.route("quotations.show",t.quotation.id)},{svg:C(()=>[st]),_:1},8,["href"]),l(R,{disabled:d(s).processing||h.value===0},{default:C(()=>[j(" Save ")]),_:1},8,["disabled"])])])],40,Ce)])]}),_:1})],64))}};export{ut as default};
