import{_ as t}from"./_plugin-vue_export-helper-c27b6911.js";import{b as r,d as o,B as n}from"./app-d08c0398.js";const s={},c={class:"inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"};function i(e,a){return r(),o("button",c,[n(e.$slots,"default")])}const u=t(s,[["render",i]]);export{u as D};
