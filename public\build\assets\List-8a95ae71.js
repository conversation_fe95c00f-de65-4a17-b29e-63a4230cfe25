import{r as y,c as L,b as o,d as n,e as l,u as x,f as c,F as S,Z as ne,h as e,i as N,v as ie,G as re,j as O,k as $,q as P,l as B,t as i,H as de,z as ce,n as ue,O as j}from"./app-e21f56bc.js";import{_ as me,b as _e,a as z}from"./AdminLayout-46709983.js";import{M as pe,_ as he}from"./Modal-04ee4cac.js";import{D as ve}from"./DangerButton-12e50229.js";import{_ as ge}from"./Pagination-eae9c7b2.js";import{_ as T}from"./SearchableDropdownNew-b8de8067.js";import{_ as M}from"./InputLabel-d2dad70b.js";import{s as fe,_ as ye}from"./ArrowIcon-434878f3.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const xe={class:"animate-top"},be={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},we=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Orders")],-1),ke={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},Ce={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},Ae=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),Se={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},Oe={class:"flex justify-between mb-2"},Me={class:"flex"},Ve=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),De={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ue={key:0,class:"sm:col-span-4"},Le={class:"relative mt-2"},Ne={class:"sm:col-span-4"},$e={class:"relative mt-2"},Be={class:"sm:col-span-4"},Te={class:"relative mt-2"},Ee={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Pe={class:"shadow rounded-lg"},je={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ze={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Fe={class:"border-b-2"},Ie=["onClick"],Re={key:0},qe={class:"px-4 py-2.5 min-w-36"},Ge={class:"px-4 py-2.5 min-w-36"},Ke={class:"px-4 py-2.5 min-w-28"},We={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},He={class:"px-4 py-2.5 min-w-36"},Ye={class:"px-4 py-2.5 font-semibold text-green-600 min-w-36"},Qe={class:"px-4 py-2.5"},Xe={key:0,class:"text-gray-700 text-sm"},Ze={key:1,class:"text-gray-400 text-sm"},Je={class:"px-4 py-2.5 min-w-36"},et={class:"px-4 py-2.5 min-w-44"},tt={key:0,class:"flex items-center space-x-2"},st=["onUpdate:modelValue","onChange"],at=["value"],ot=["onClick"],lt={key:1,class:"flex items-center space-x-2"},nt=["onClick"],it={key:0,class:"px-4 py-2.5"},rt={class:"items-center px-4 py-2.5"},dt={class:"flex items-center justify-start gap-4"},ct=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),ut=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),mt=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),_t=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),pt=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),ht=["onClick"],vt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),gt=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),ft=[vt,gt],yt=["onClick"],xt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),bt=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),wt=[xt,bt],kt={key:1},Ct=e("tr",{class:"bg-white"},[e("td",{colspan:"10",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),At=[Ct],St={class:"p-6"},Ot=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this order? ",-1),Mt={class:"mt-6 flex justify-end"},zt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","isAdmin"],setup(h){const u=h,{form:V,search:k,sort:F,fetchData:Vt,sortKey:I,sortDirection:R}=fe("orders.index"),D=y(!1),E=y(null),q=[{id:"confirmed",name:"Confirmed"},{id:"under_production",name:"Under Production"},{id:"shipped",name:"Shipped"},{id:"delivered",name:"Delivered"}],G=L(()=>[{id:"",name:"All Agents"},...u.agents]),K=L(()=>[{id:"",name:"All Country"},...u.counties]),W=L(()=>[{id:"",name:"All Status"},...u.statusOptions]),H=[{field:"order_number",label:"ORDER NO",sortable:!0,visible:!0},{field:"quotation.quotation_number",label:"QUOTATION NO",sortable:!1,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!1,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"lead.county.name",label:"COUNTRY",sortable:!0,visible:!0},{field:"total_amount",label:"TOTAL AMOUNT",sortable:!0,visible:!0},{field:"tracking_number",label:"TRACKING",sortable:!1,visible:!0},{field:"expected_delivery",label:"EXP. DELIVERY",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:u.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],Y=s=>{E.value=s,D.value=!0},U=()=>{D.value=!1},Q=()=>{V.delete(route("orders.destroy",{order:E.value}),{onSuccess:()=>U()})},X=s=>({confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-yellow-100 text-yellow-800",delivered:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",m=y(u.agent_id||""),_=y(u.county_id||""),p=y(u.status||""),w=y(""),v=y({}),Z=(s,a)=>{m.value=s,C(w.value,m.value,_.value,p.value)},J=(s,a)=>{_.value=s,C(w.value,m.value,_.value,p.value)},ee=(s,a)=>{p.value=s,C(w.value,m.value,_.value,p.value)},C=(s,a,t,r)=>{w.value=s;const g=a===""?null:a,f=t===""?null:t,b=r===""?null:r;V.get(route("orders.index",{search:s,agent_id:g,county_id:f,status:b}),{preserveState:!0})},te=(s,a)=>{v.value[s]=a},se=s=>{delete v.value[s]},ae=s=>{window.open(route("orders.pdf",s),"_blank")},oe=(s,a)=>{j.post(route("orders.update-status",s),{status:a},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete v.value[s];const r=new URLSearchParams(window.location.search).get("page")||1;j.get(route("orders.index"),{search:w.value,agent_id:m.value===""?null:m.value,county_id:_.value===""?null:_.value,status:p.value===""?null:p.value,page:r},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})},le=(s,a)=>{const t={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},r=Object.keys(t).find(A=>a==null?void 0:a.toLowerCase().includes(A.toLowerCase())),{locale:g,currency:f}=t[r]||t["United Kingdom"],b=new Intl.NumberFormat(g,{style:"currency",currency:f,currencyDisplay:"symbol"}).format(s);return`${f} ${b}`};return(s,a)=>(o(),n(S,null,[l(x(ne),{title:"Orders"}),l(me,null,{default:c(()=>[e("div",xe,[e("div",be,[we,e("div",ke,[e("div",Ce,[Ae,N(e("input",{type:"text","onUpdate:modelValue":a[0]||(a[0]=t=>re(k)?k.value=t:null),onInput:a[1]||(a[1]=t=>C(x(k),m.value,_.value,p.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for orders..."},null,544),[[ie,x(k)]])])])]),e("div",Se,[e("div",Oe,[e("div",Me,[Ve,l(M,{for:"filters",value:"Filters"})])]),e("div",De,[u.isAdmin?(o(),n("div",Ue,[l(M,{for:"agent_filter",value:"Agents"}),e("div",Le,[l(T,{options:G.value,modelValue:m.value,"onUpdate:modelValue":a[2]||(a[2]=t=>m.value=t),onOnchange:Z},null,8,["options","modelValue"])])])):O("",!0),e("div",Ne,[l(M,{for:"county_filter",value:"Country"}),e("div",$e,[l(T,{options:K.value,modelValue:_.value,"onUpdate:modelValue":a[3]||(a[3]=t=>_.value=t),onOnchange:J},null,8,["options","modelValue"])])]),e("div",Be,[l(M,{for:"status_filter",value:"Status"}),e("div",Te,[l(T,{options:W.value,modelValue:p.value,"onUpdate:modelValue":a[4]||(a[4]=t=>p.value=t),onOnchange:ee},null,8,["options","modelValue"])])])])]),e("div",Ee,[e("div",Pe,[e("table",je,[e("thead",ze,[e("tr",Fe,[(o(),n(S,null,$(H,(t,r)=>N(e("th",{key:r,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:g=>x(F)(t.field,t.sortable)},[B(i(t.label)+" ",1),t.sortable?(o(),P(ye,{key:0,isSorted:x(I)===t.field,direction:x(R)},null,8,["isSorted","direction"])):O("",!0)],8,Ie),[[de,t.visible]])),64))])]),h.data.data&&h.data.data.length>0?(o(),n("tbody",Re,[(o(!0),n(S,null,$(h.data.data,t=>{var r,g,f,b,A;return o(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",qe,i(t.order_number),1),e("td",Ge,i(t.quotation?t.quotation.quotation_number:"N/A"),1),e("td",Ke,i(t.lead?t.lead.lead_number:"N/A"),1),e("td",We,i((r=t.lead)==null?void 0:r.client_name),1),e("td",He,i((f=(g=t.lead)==null?void 0:g.county)==null?void 0:f.name),1),e("td",Ye,i(le(t.total_amount,(A=(b=t.lead)==null?void 0:b.county)==null?void 0:A.name)),1),e("td",Qe,[t.tracking_number?(o(),n("span",Xe,i(t.tracking_number),1)):(o(),n("span",Ze,"-"))]),e("td",Je,i(t.expected_delivery?new Date(t.expected_delivery).toLocaleDateString("en-GB"):"N/A"),1),e("td",et,[v.value[t.id]!==void 0?(o(),n("div",tt,[N(e("select",{"onUpdate:modelValue":d=>v.value[t.id]=d,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:d=>oe(t.id,v.value[t.id])},[(o(),n(S,null,$(q,d=>e("option",{key:d.id,value:d.id},i(d.name),9,at)),64))],40,st),[[ce,v.value[t.id]]]),e("button",{onClick:d=>se(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,ot)])):(o(),n("div",lt,[e("span",{class:ue(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",X(t.status)]),onClick:d=>te(t.id,t.status),title:"Click to edit status"},i(t.status.charAt(0).toUpperCase()+t.status.slice(1).replace("_"," ")),11,nt)]))]),u.isAdmin?(o(),n("td",it,i(t.lead.creator?t.lead.creator.first_name:"N/A"),1)):O("",!0),e("td",rt,[e("div",dt,[l(_e,{align:"right",width:"48"},{trigger:c(()=>[ct]),content:c(()=>[l(z,{href:s.route("orders.show",{order:t.id})},{svg:c(()=>[ut]),text:c(()=>[mt]),_:2},1032,["href"]),l(z,{href:s.route("orders.edit",{order:t.id})},{svg:c(()=>[_t]),text:c(()=>[pt]),_:2},1032,["href"]),e("button",{type:"button",onClick:d=>Y(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ft,8,ht),e("button",{type:"button",onClick:d=>ae(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},wt,8,yt)]),_:2},1024)])])])}),128))])):(o(),n("tbody",kt,At))])])]),h.data.data&&h.data.data.length>0?(o(),P(ge,{key:0,class:"mt-6",links:h.data.links},null,8,["links"])):O("",!0)]),l(pe,{show:D.value,onClose:U},{default:c(()=>[e("div",St,[Ot,e("div",Mt,[l(he,{onClick:U},{default:c(()=>[B("Cancel")]),_:1}),l(ve,{class:"ml-3",onClick:Q,disabled:x(V).processing},{default:c(()=>[B(" Delete Order ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{zt as default};
