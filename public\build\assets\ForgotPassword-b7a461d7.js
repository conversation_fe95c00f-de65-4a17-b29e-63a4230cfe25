import{T as c,b as l,m as u,f as i,e as o,u as t,Z as _,d as p,t as f,i as g,g as a,k as y,n as w,q as x,p as b,l as h}from"./app-72d803da.js";import{G as k}from"./GuestLayout-744a4d13.js";import{_ as v,a as P}from"./TextInput-fd97aae6.js";import{_ as S}from"./InputLabel-8b22735f.js";import{P as V}from"./PrimaryButton-7b7eba8c.js";import{_ as B}from"./_plugin-vue_export-helper-c27b6911.js";const m=e=>(b("data-v-0047bcf7"),e=e(),h(),e),F=m(()=>a("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"}," Forgot your password ?",-1)),N=m(()=>a("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1)),I={key:0,class:"mb-4 font-medium text-sm text-green-600"},C=["onSubmit"],E={class:"flex items-center justify-end mt-4"},$={__name:"ForgotPassword",props:{status:{type:String}},setup(e){const s=c({email:""}),d=()=>{s.post(route("password.email"))};return(q,r)=>(l(),u(k,null,{default:i(()=>[o(t(_),{title:"Forgot Password"}),F,N,e.status?(l(),p("div",I,f(e.status),1)):g("",!0),a("form",{onSubmit:x(d,["prevent"])},[a("div",null,[o(S,{for:"email",value:"Email"}),o(v,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:t(s).email,"onUpdate:modelValue":r[0]||(r[0]=n=>t(s).email=n),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(P,{class:"mt-2",message:t(s).errors.email},null,8,["message"])]),a("div",E,[o(V,{class:w({"opacity-25":t(s).processing}),disabled:t(s).processing},{default:i(()=>[y(" Email Password Reset Link ")]),_:1},8,["class","disabled"])])],40,C)]),_:1}))}},J=B($,[["__scopeId","data-v-0047bcf7"]]);export{J as default};
