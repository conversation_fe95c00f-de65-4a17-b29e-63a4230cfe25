import{r as k,T as Z,w as X,b as y,d as g,e as l,u as o,f as D,F as Y,Z as ee,h as e,t as r,j as x,s as te,l as ae}from"./app-e1dc0e85.js";import{_ as se,a as le}from"./AdminLayout-bed0e8e1.js";import{_ as V,a as v}from"./TextInput-f729f5f2.js";import{_ as c}from"./InputLabel-b452155c.js";import{P as de}from"./PrimaryButton-5df40e25.js";import{_ as oe}from"./TextArea-2666d68e.js";import{_ as F}from"./Checkbox-7446c6dc.js";import"./_plugin-vue_export-helper-c27b6911.js";const ne={class:"animate-top bg-white p-4 shadow sm:p-6 rounded-lg border"},ie={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6"},re={class:"text-xl sm:text-2xl font-semibold leading-7 text-gray-900"},ce={class:"text-sm text-gray-600 mt-1"},ue={class:"flex justify-start sm:justify-end"},_e={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"},me={class:"mb-8 p-4 bg-gray-50 rounded-lg"},ye=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1),ge={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},xe=e("p",{class:"text-sm font-semibold text-gray-900"},"Quotation",-1),ve={class:"text-sm text-gray-700"},qe=e("p",{class:"text-sm font-semibold text-gray-900"},"Lead",-1),pe={class:"text-sm text-gray-700"},fe=e("p",{class:"text-sm font-semibold text-gray-900"},"Total Amount",-1),be={class:"text-sm font-semibold text-green-700"},he=e("p",{class:"text-sm font-semibold text-gray-900"},"Created By",-1),ke={class:"text-sm text-gray-700"},we=["onSubmit"],Ae={class:"border-b border-gray-900/10 pb-12"},Ce={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ve={class:"sm:col-span-12"},Qe=e("h3",{class:"text-lg font-semibold text-gray-900"},"Edit Order Quantities",-1),Ne=e("p",{class:"text-sm text-gray-600"},'Current order quantities are shown. Check/uncheck to add/remove quantities. Use "Select All" to see all available options:',-1),Ue={key:0,class:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg"},$e={class:"text-sm text-red-600"},Fe={key:0,class:"sm:col-span-3"},Se={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},De={class:"grid grid-cols-1 gap-4"},Oe={class:"text-sm text-gray-500 mt-1"},Pe={key:1,class:"sm:col-span-3"},Be={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},je={class:"grid grid-cols-1 gap-4"},Me={class:"text-sm text-gray-500 mt-1"},Ee={key:2,class:"sm:col-span-3"},Te={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},ze={class:"grid grid-cols-1 gap-4"},Le={class:"text-sm text-gray-500 mt-1"},Ie={key:3,class:"sm:col-span-3"},Ke={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ge={class:"grid grid-cols-1 gap-4"},He={class:"text-sm text-gray-500 mt-1"},Ze={key:4,class:"sm:col-span-12"},Je={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Re={class:"flex items-center justify-between"},We={class:"text-lg font-semibold text-green-800"},Xe=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing ",-1),Ye=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),et={class:"sm:col-span-12 mb-6"},tt=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information",-1),at={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},st=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1),lt={class:"text-sm text-gray-700"},dt=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1),ot={class:"text-sm text-gray-700"},nt=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1),it={class:"text-sm text-gray-700"},rt=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1),ct={class:"text-sm text-gray-700"},ut=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1),_t={class:"text-sm text-gray-700"},mt=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1),yt={class:"text-sm text-gray-700"},gt=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1),xt={class:"text-sm text-gray-700"},vt=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1),qt={class:"text-sm text-gray-700"},pt={key:0},ft=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1),bt={class:"text-sm text-gray-700"},ht={class:"sm:col-span-4"},kt=e("p",{class:"text-sm text-gray-500 mt-1"},"Add tracking number when order is shipped",-1),wt={class:"sm:col-span-4"},At=["value"],Ct={class:"sm:col-span-4"},Vt=["value"],Qt={class:"sm:col-span-12"},Nt={class:"flex mt-6 items-center justify-between"},Ut={class:"ml-auto flex items-center justify-end gap-x-6"},$t=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Et={__name:"Edit",props:{data:{type:Object,required:!0}},setup(s){const n=s;k(!0);const q=k(!!n.data.selected_qty_1),p=k(!!n.data.selected_qty_2),f=k(!!n.data.selected_qty_3),b=k(!!n.data.selected_qty_4),Q=k(0),t=Z({selected_qty_1:n.data.lead.qty_1||"",selected_qty_2:n.data.lead.qty_2||"",selected_qty_3:n.data.lead.qty_3||"",selected_qty_4:n.data.lead.qty_4||"",tracking_number:n.data.tracking_number||"",expected_delivery:n.data.expected_delivery||"",actual_delivery:n.data.actual_delivery||"",notes:n.data.notes||""}),J=()=>q.value||p.value||f.value||b.value?q.value&&!t.selected_qty_1?(alert("Please enter a value for Quantity 1."),!1):p.value&&!t.selected_qty_2?(alert("Please enter a value for Quantity 2."),!1):f.value&&!t.selected_qty_3?(alert("Please enter a value for Quantity 3."),!1):b.value&&!t.selected_qty_4?(alert("Please enter a value for Quantity 4."),!1):!0:(alert("Please select at least one quantity for the order."),!1),R=()=>{if(!J())return;const i=W();Z(i).put(route("orders.update",n.data.id),{preserveScroll:!0})},h=i=>{var A,C;const a={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},u=((C=(A=n.data.lead)==null?void 0:A.county)==null?void 0:C.name)||"UK",_=Object.keys(a).find($=>u.toLowerCase().includes($.toLowerCase())),{locale:m,currency:w}=a[_]||a.UK,U=new Intl.NumberFormat(m,{style:"currency",currency:w,currencyDisplay:"symbol"}).format(i);return`${w} ${U}`},S=()=>{var a,u,_,m;let i=0;q.value&&t.selected_qty_1&&((a=n.data.quotation)!=null&&a.price_qty_1)&&(i+=parseFloat(t.selected_qty_1)*parseFloat(n.data.quotation.price_qty_1)),p.value&&t.selected_qty_2&&((u=n.data.quotation)!=null&&u.price_qty_2)&&(i+=parseFloat(t.selected_qty_2)*parseFloat(n.data.quotation.price_qty_2)),f.value&&t.selected_qty_3&&((_=n.data.quotation)!=null&&_.price_qty_3)&&(i+=parseFloat(t.selected_qty_3)*parseFloat(n.data.quotation.price_qty_3)),b.value&&t.selected_qty_4&&((m=n.data.quotation)!=null&&m.price_qty_4)&&(i+=parseFloat(t.selected_qty_4)*parseFloat(n.data.quotation.price_qty_4)),Q.value=i},N=(i,a)=>{S()},W=()=>{var a,u,_,m;const i={status:t.status,tracking_number:t.tracking_number,expected_delivery:t.expected_delivery,actual_delivery:t.actual_delivery,notes:t.notes,selected_qty_1:null,selected_qty_2:null,selected_qty_3:null,selected_qty_4:null,price_qty_1:null,price_qty_2:null,price_qty_3:null,price_qty_4:null};return q.value&&t.selected_qty_1&&(i.selected_qty_1=t.selected_qty_1,i.price_qty_1=(a=n.data.quotation)==null?void 0:a.price_qty_1),p.value&&t.selected_qty_2&&(i.selected_qty_2=t.selected_qty_2,i.price_qty_2=(u=n.data.quotation)==null?void 0:u.price_qty_2),f.value&&t.selected_qty_3&&(i.selected_qty_3=t.selected_qty_3,i.price_qty_3=(_=n.data.quotation)==null?void 0:_.price_qty_3),b.value&&t.selected_qty_4&&(i.selected_qty_4=t.selected_qty_4,i.price_qty_4=(m=n.data.quotation)==null?void 0:m.price_qty_4),i.total_amount=Q.value,i};return X([()=>t.selected_qty_1,()=>q.value,()=>t.selected_qty_2,()=>p.value,()=>t.selected_qty_3,()=>f.value,()=>t.selected_qty_4,()=>b.value],S),S(),(i,a)=>(y(),g(Y,null,[l(o(ee),{title:"Orders"}),l(se,null,{default:D(()=>{var u,_,m,w,U,A,C,$,O,P,B,j,M,E,T,z,L,I,K,G,H;return[e("div",ne,[e("div",ie,[e("div",null,[e("h2",re," Edit Order - "+r(s.data.order_number),1),e("p",ce," Client: "+r(s.data.lead.client_name),1)]),e("div",ue,[s.data.is_confirmed?(y(),g("span",_e," ✓ Confirmed ")):x("",!0)])]),e("div",me,[ye,e("div",ge,[e("div",null,[xe,e("p",ve,r(((u=s.data.quotation)==null?void 0:u.quotation_number)||"N/A"),1)]),e("div",null,[qe,e("p",pe,r(s.data.lead.lead_number||"N/A"),1)]),e("div",null,[fe,e("p",be,r(h(s.data.total_amount)),1)]),e("div",null,[he,e("p",ke,r((_=s.data.creator)==null?void 0:_.first_name)+" "+r((m=s.data.creator)==null?void 0:m.last_name),1)])])]),e("form",{onSubmit:te(R,["prevent"])},[e("div",Ae,[e("div",Ce,[e("div",Ve,[Qe,Ne,i.$page.props.errors.quantities?(y(),g("div",Ue,[e("p",$e,r(i.$page.props.errors.quantities),1)])):x("",!0)]),(w=s.data.lead)!=null&&w.qty_1&&((U=s.data.quotation)!=null&&U.price_qty_1)?(y(),g("div",Fe,[e("div",Se,[l(F,{checked:q.value,"onUpdate:checked":[a[0]||(a[0]=d=>q.value=d),a[1]||(a[1]=d=>N(1,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",De,[e("div",null,[l(c,{for:"selected_qty_1",value:`Order Qty 1 (Available: ${s.data.lead.qty_1})`},null,8,["value"]),l(V,{id:"selected_qty_1",type:"number",modelValue:o(t).selected_qty_1,"onUpdate:modelValue":a[2]||(a[2]=d=>o(t).selected_qty_1=d),max:s.data.lead.qty_1,min:"1",placeholder:`Max: ${s.data.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_1},null,8,["message"]),e("p",Oe,"Price: "+r(h(s.data.quotation.price_qty_1))+" per unit",1)])])])):x("",!0),(A=s.data.lead)!=null&&A.qty_2&&((C=s.data.quotation)!=null&&C.price_qty_2)?(y(),g("div",Pe,[e("div",Be,[l(F,{checked:p.value,"onUpdate:checked":[a[3]||(a[3]=d=>p.value=d),a[4]||(a[4]=d=>N(2,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",je,[e("div",null,[l(c,{for:"selected_qty_2",value:`Order Qty 2 (Available: ${s.data.lead.qty_2})`},null,8,["value"]),l(V,{id:"selected_qty_2",type:"number",modelValue:o(t).selected_qty_2,"onUpdate:modelValue":a[5]||(a[5]=d=>o(t).selected_qty_2=d),max:s.data.lead.qty_2,min:"1",placeholder:`Max: ${s.data.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_2},null,8,["message"]),e("p",Me,"Price: "+r(h(s.data.quotation.price_qty_2))+" per unit",1)])])])):x("",!0),($=s.data.lead)!=null&&$.qty_3&&((O=s.data.quotation)!=null&&O.price_qty_3)?(y(),g("div",Ee,[e("div",Te,[l(F,{checked:f.value,"onUpdate:checked":[a[6]||(a[6]=d=>f.value=d),a[7]||(a[7]=d=>N(3,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",ze,[e("div",null,[l(c,{for:"selected_qty_3",value:`Order Qty 3 (Available: ${s.data.lead.qty_3})`},null,8,["value"]),l(V,{id:"selected_qty_3",type:"number",modelValue:o(t).selected_qty_3,"onUpdate:modelValue":a[8]||(a[8]=d=>o(t).selected_qty_3=d),max:s.data.lead.qty_3,min:"1",placeholder:`Max: ${s.data.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_3},null,8,["message"]),e("p",Le,"Price: "+r(h(s.data.quotation.price_qty_3))+" per unit",1)])])])):x("",!0),(P=s.data.lead)!=null&&P.qty_4&&((B=s.data.quotation)!=null&&B.price_qty_4)?(y(),g("div",Ie,[e("div",Ke,[l(F,{checked:b.value,"onUpdate:checked":[a[9]||(a[9]=d=>b.value=d),a[10]||(a[10]=d=>N(4,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",Ge,[e("div",null,[l(c,{for:"selected_qty_4",value:`Order Qty 4 (Available: ${s.data.lead.qty_4})`},null,8,["value"]),l(V,{id:"selected_qty_4",type:"number",modelValue:o(t).selected_qty_4,"onUpdate:modelValue":a[11]||(a[11]=d=>o(t).selected_qty_4=d),max:s.data.lead.qty_4,min:"1",placeholder:`Max: ${s.data.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_4},null,8,["message"]),e("p",He,"Price: "+r(h(s.data.quotation.price_qty_4))+" per unit",1)])])])):x("",!0),Q.value>0?(y(),g("div",Ze,[e("div",Je,[e("div",Re,[e("div",null,[e("p",We," Updated Order Total: "+r(h(Q.value.toFixed(2))),1),Xe]),Ye])])])):x("",!0),e("div",et,[tt,e("div",at,[e("div",null,[st,e("p",lt,r(((j=s.data.lead)==null?void 0:j.client_name)||"N/A"),1)]),e("div",null,[dt,e("p",ot,r(((E=(M=s.data.lead)==null?void 0:M.county)==null?void 0:E.name)||"N/A"),1)]),e("div",null,[nt,e("p",it,r(((T=s.data.lead)==null?void 0:T.dimensions)||"N/A"),1)]),e("div",null,[rt,e("p",ct,r(((z=s.data.lead)==null?void 0:z.open_size)||"N/A"),1)]),e("div",null,[ut,e("p",_t,r(((L=s.data.lead)==null?void 0:L.box_style)||"N/A"),1)]),e("div",null,[mt,e("p",yt,r(((I=s.data.lead)==null?void 0:I.stock)||"N/A"),1)]),e("div",null,[gt,e("p",xt,r(((K=s.data.lead)==null?void 0:K.lamination)||"N/A"),1)]),e("div",null,[vt,e("p",qt,r(((G=s.data.lead)==null?void 0:G.printing)||"N/A"),1)]),(H=s.data.lead)!=null&&H.add_ons?(y(),g("div",pt,[ft,e("p",bt,r(s.data.lead.add_ons),1)])):x("",!0)])]),e("div",ht,[l(c,{for:"tracking_number",value:"Tracking Number"}),l(V,{id:"tracking_number",type:"text",modelValue:o(t).tracking_number,"onUpdate:modelValue":a[12]||(a[12]=d=>o(t).tracking_number=d),placeholder:"Enter tracking number"},null,8,["modelValue"]),l(v,{message:o(t).errors.tracking_number},null,8,["message"]),kt]),e("div",wt,[l(c,{for:"expected_delivery",value:"Expected Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(t).expected_delivery?o(t).expected_delivery.slice(0,10):"",onInput:a[13]||(a[13]=d=>o(t).expected_delivery=d.target.value)},null,40,At),l(v,{message:o(t).errors.expected_delivery},null,8,["message"])]),e("div",Ct,[l(c,{for:"actual_delivery",value:"Actual Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(t).actual_delivery?o(t).actual_delivery.slice(0,10):"",onInput:a[14]||(a[14]=d=>o(t).actual_delivery=d.target.value)},null,40,Vt),l(v,{message:o(t).errors.actual_delivery},null,8,["message"])]),e("div",Qt,[l(c,{for:"notes",value:"Order Notes"}),l(oe,{id:"notes",modelValue:o(t).notes,"onUpdate:modelValue":a[15]||(a[15]=d=>o(t).notes=d),rows:"4",placeholder:"Add any notes about this order..."},null,8,["modelValue"]),l(v,{message:o(t).errors.notes},null,8,["message"])])])]),e("div",Nt,[e("div",Ut,[l(le,{href:i.route("orders.index")},{svg:D(()=>[$t]),_:1},8,["href"]),l(de,{disabled:o(t).processing},{default:D(()=>[ae(" Save ")]),_:1},8,["disabled"])])])],40,we)])]}),_:1})],64))}};export{Et as default};
