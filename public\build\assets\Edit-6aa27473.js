import{_ as i}from"./AdminLayout-46709983.js";/* empty css                                                              */import{b as o,d as m,e as s,u as r,f as a,F as l,Z as n,h as t}from"./app-e21f56bc.js";import c from"./UpdateProfileInformationForm-d852e157.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./TextInput-1ecc3ccf.js";import"./InputLabel-d2dad70b.js";import"./PrimaryButton-3e573a38.js";import"./TextArea-b0545758.js";const u=t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),d={class:""},_={class:"max-w-7xl mx-auto"},f={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},k={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(e){return(p,h)=>(o(),m(l,null,[s(r(n),{title:"Profile"}),s(i,null,{header:a(()=>[u]),default:a(()=>[t("div",d,[t("div",_,[t("div",f,[s(c,{"must-verify-email":e.mustVerifyEmail,status:e.status,class:"max-w-xl"},null,8,["must-verify-email","status"])])])])]),_:1})],64))}};export{k as default};
