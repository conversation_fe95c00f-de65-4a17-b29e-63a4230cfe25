import{b as a,d as y,e as n,u as s,f as p,F as q,Z as V,h as l,s as x,q as m,j as d,l as b,E as k}from"./app-e21f56bc.js";import{_ as $,a as C}from"./AdminLayout-46709983.js";import{_ as u,a as r}from"./TextInput-1ecc3ccf.js";import{_ as i}from"./InputLabel-d2dad70b.js";import{P as U}from"./PrimaryButton-3e573a38.js";import{_ as S}from"./TextArea-b0545758.js";import{_ as z}from"./SearchableDropdownNew-b8de8067.js";import{_ as N}from"./MultipleFileUpload-d12a67b0.js";import{u as w}from"./index-a79101d6.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top"},T={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},A=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Lead",-1),F=["onSubmit"],Q={class:"border-b border-gray-900/10 pb-12"},Y={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},j={class:"sm:col-span-2"},D={class:"sm:col-span-2"},L={class:"relative mt-2"},P={class:"sm:col-span-2"},E={class:"sm:col-span-2"},O={class:"sm:col-span-2"},I={class:"sm:col-span-2"},M={class:"sm:col-span-2"},Z={class:"sm:col-span-2"},G={class:"sm:col-span-2"},H={class:"sm:col-span-1"},J={class:"sm:col-span-1"},K={class:"sm:col-span-1"},R={class:"sm:col-span-1"},W={class:"sm:col-span-2"},X={class:"sm:col-span-6"},h={class:"flex mt-6 items-center justify-between"},ee={class:"ml-auto flex items-center justify-end gap-x-6"},se=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),te={key:0,class:"text-sm text-gray-600"},pe={__name:"Add",props:{counties:{type:Array,required:!0}},setup(v){const e=w("post","/leads",{client_name:"",county_id:"",dimensions:"",open_size:"",box_style:"",stock:"",lamination:"",printing:"",add_ons:"",qty_1:"",qty_2:"",qty_3:"",qty_4:"",notes:"",document:""}),g=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),f=(_,t)=>{e.county_id=_},c=_=>{e.document=_};return(_,t)=>(a(),y(q,null,[n(s(V),{title:"Leads"}),n($,null,{default:p(()=>[l("div",B,[l("div",T,[A,l("form",{onSubmit:x(g,["prevent"]),class:""},[l("div",Q,[l("div",Y,[l("div",j,[n(i,{for:"client_name",value:"Client Name *"}),n(u,{id:"client_name",type:"text",modelValue:s(e).client_name,"onUpdate:modelValue":t[0]||(t[0]=o=>s(e).client_name=o),required:"",onChange:t[1]||(t[1]=o=>s(e).validate("client_name"))},null,8,["modelValue"]),s(e).invalid("client_name")?(a(),m(r,{key:0,message:s(e).errors.client_name},null,8,["message"])):d("",!0)]),l("div",D,[n(i,{for:"county_id",value:"Country *"}),l("div",L,[n(z,{options:v.counties,onOnchange:f,required:"",placeholder:"Select Country"},null,8,["options"])]),s(e).invalid("county_id")?(a(),m(r,{key:0,message:s(e).errors.county_id},null,8,["message"])):d("",!0)]),l("div",P,[n(i,{for:"dimensions",value:"Dimensions *"}),n(u,{id:"dimensions",type:"text",modelValue:s(e).dimensions,"onUpdate:modelValue":t[2]||(t[2]=o=>s(e).dimensions=o),required:"",onChange:t[3]||(t[3]=o=>s(e).validate("dimensions"))},null,8,["modelValue"]),s(e).invalid("dimensions")?(a(),m(r,{key:0,message:s(e).errors.dimensions},null,8,["message"])):d("",!0)]),l("div",E,[n(i,{for:"open_size",value:"Open Size  *"}),n(u,{id:"open_size",type:"text",modelValue:s(e).open_size,"onUpdate:modelValue":t[4]||(t[4]=o=>s(e).open_size=o),required:"",onChange:t[5]||(t[5]=o=>s(e).validate("open_size"))},null,8,["modelValue"]),s(e).invalid("open_size")?(a(),m(r,{key:0,message:s(e).errors.open_size},null,8,["message"])):d("",!0)]),l("div",O,[n(i,{for:"box_style",value:"Box Style *"}),n(u,{id:"box_style",type:"text",modelValue:s(e).box_style,"onUpdate:modelValue":t[6]||(t[6]=o=>s(e).box_style=o),required:"",onChange:t[7]||(t[7]=o=>s(e).validate("box_style"))},null,8,["modelValue"]),s(e).invalid("box_style")?(a(),m(r,{key:0,message:s(e).errors.box_style},null,8,["message"])):d("",!0)]),l("div",I,[n(i,{for:"stock",value:"Stock *"}),n(u,{id:"stock",type:"text",modelValue:s(e).stock,"onUpdate:modelValue":t[8]||(t[8]=o=>s(e).stock=o),required:"",onChange:t[9]||(t[9]=o=>s(e).validate("stock"))},null,8,["modelValue"]),s(e).invalid("stock")?(a(),m(r,{key:0,message:s(e).errors.stock},null,8,["message"])):d("",!0)]),l("div",M,[n(i,{for:"lamination",value:"Lamination *"}),n(u,{id:"lamination",type:"text",modelValue:s(e).lamination,"onUpdate:modelValue":t[10]||(t[10]=o=>s(e).lamination=o),required:"",onChange:t[11]||(t[11]=o=>s(e).validate("lamination"))},null,8,["modelValue"]),s(e).invalid("lamination")?(a(),m(r,{key:0,message:s(e).errors.lamination},null,8,["message"])):d("",!0)]),l("div",Z,[n(i,{for:"printing",value:"Printing *"}),n(u,{id:"printing",type:"text",modelValue:s(e).printing,"onUpdate:modelValue":t[12]||(t[12]=o=>s(e).printing=o),required:"",onChange:t[13]||(t[13]=o=>s(e).validate("printing"))},null,8,["modelValue"]),s(e).invalid("printing")?(a(),m(r,{key:0,message:s(e).errors.printing},null,8,["message"])):d("",!0)]),l("div",G,[n(i,{for:"add_ons",value:"Add ons"}),n(u,{id:"add_ons",type:"text",modelValue:s(e).add_ons,"onUpdate:modelValue":t[14]||(t[14]=o=>s(e).add_ons=o),onChange:t[15]||(t[15]=o=>s(e).validate("add_ons"))},null,8,["modelValue"]),s(e).invalid("add_ons")?(a(),m(r,{key:0,message:s(e).errors.add_ons},null,8,["message"])):d("",!0)]),l("div",H,[n(i,{for:"qty_1",value:"QTY 1 *"}),n(u,{id:"qty_1",type:"text",modelValue:s(e).qty_1,"onUpdate:modelValue":t[16]||(t[16]=o=>s(e).qty_1=o),numeric:!0,required:"",onChange:t[17]||(t[17]=o=>s(e).validate("qty_1"))},null,8,["modelValue"]),s(e).invalid("qty_1")?(a(),m(r,{key:0,message:s(e).errors.qty_1},null,8,["message"])):d("",!0)]),l("div",J,[n(i,{for:"qty_2",value:"QTY 2"}),n(u,{id:"qty_2",type:"text",numeric:!0,modelValue:s(e).qty_2,"onUpdate:modelValue":t[18]||(t[18]=o=>s(e).qty_2=o),onChange:t[19]||(t[19]=o=>s(e).validate("qty_2"))},null,8,["modelValue"]),s(e).invalid("qty_2")?(a(),m(r,{key:0,message:s(e).errors.qty_2},null,8,["message"])):d("",!0)]),l("div",K,[n(i,{for:"qty_3",value:"QTY 3"}),n(u,{id:"qty_3",type:"text",numeric:!0,modelValue:s(e).qty_3,"onUpdate:modelValue":t[20]||(t[20]=o=>s(e).qty_3=o),onChange:t[21]||(t[21]=o=>s(e).validate("qty_3"))},null,8,["modelValue"]),s(e).invalid("qty_3")?(a(),m(r,{key:0,message:s(e).errors.qty_3},null,8,["message"])):d("",!0)]),l("div",R,[n(i,{for:"qty_4",value:"QTY 4"}),n(u,{id:"qty_4",type:"text",numeric:!0,modelValue:s(e).qty_4,"onUpdate:modelValue":t[22]||(t[22]=o=>s(e).qty_4=o),onChange:t[23]||(t[23]=o=>s(e).validate("qty_4"))},null,8,["modelValue"]),s(e).invalid("qty_4")?(a(),m(r,{key:0,message:s(e).errors.qty_4},null,8,["message"])):d("",!0)]),l("div",W,[n(i,{for:"note",value:"Upload Documents"}),n(N,{inputId:"document",inputName:"document",onFiles:c})]),l("div",X,[n(i,{for:"notes",value:"Notes"}),n(S,{id:"notes",type:"text",rows:3,modelValue:s(e).notes,"onUpdate:modelValue":t[24]||(t[24]=o=>s(e).notes=o),autocomplete:"notes",onChange:t[25]||(t[25]=o=>s(e).validate("notes"))},null,8,["modelValue"]),n(r,{class:"",message:s(e).errors.notes},null,8,["message"])])])]),l("div",h,[l("div",ee,[n(C,{href:_.route("leads.index")},{svg:p(()=>[se]),_:1},8,["href"]),n(U,{disabled:s(e).processing},{default:p(()=>[b("Save")]),_:1},8,["disabled"]),n(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[s(e).recentlySuccessful?(a(),y("p",te,"Saved.")):d("",!0)]),_:1})])])],40,F)])])]),_:1})],64))}};export{pe as default};
