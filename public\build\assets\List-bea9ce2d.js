import{b as s,d as i,g as e,n as C,r as V,e as n,u as r,f as l,F as x,Z as I,h as z,v as N,G as j,k as f,i as u,j as S,m as y,t as m}from"./app-2e8279f3.js";import{_ as E,b as T,a as F}from"./AdminLayout-536dfaf3.js";import{_ as O}from"./CreateButton-07fc4799.js";import{M as D,_ as R}from"./Modal-54a04bcc.js";import{D as G}from"./DangerButton-43ae3aed.js";import{s as H,_ as K,a as Z}from"./ArrowIcon-8539b53f.js";/* empty css                                                              */import"./_plugin-vue_export-helper-c27b6911.js";const q=["aria-checked"],J=e("span",{class:"sr-only"},"Toggle setting",-1),P={__name:"SwitchButton",props:["switchValue","userId"],emits:["updateSwitchValue"],setup(o,{emit:h}){const a=o,_=()=>{h("updateSwitchValue",!a.switchValue,a.userId)};return(p,b)=>(s(),i("button",{type:"button",class:C(["relative inline-flex items-center flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2",a.switchValue?"bg-indigo-600":"bg-gray-300","w-12 h-7 sm:w-14 sm:h-8"]),role:"switch","aria-checked":a.switchValue.toString(),onClick:_},[J,e("span",{class:C(["inline-block transform rounded-full bg-white shadow transition duration-200 ease-in-out",a.switchValue?"translate-x-5 sm:translate-x-6":"translate-x-1","w-5 h-5 sm:w-6 sm:h-6"]),"aria-hidden":"true"},null,2)],10,q))}},Q={class:"animate-top"},W={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},X=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Users")],-1),Y={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},ee={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},te=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),se={key:0,class:"sm:flex-none"},oe={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},ae={class:"shadow rounded-lg"},le={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ie={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},ne={class:"border-b-2"},re=["onClick"],de={key:0},ce={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},ue={class:"px-4 py-2.5 min-w-36"},me={class:"px-4 py-2.5 min-w-36"},he={class:"items-center px-4 py-2.5"},fe={class:"items-center px-4 py-2.5"},_e={class:"flex items-center justify-start gap-4"},pe=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),ge=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),we=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),xe=["onClick"],ye=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),be=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),ve=[ye,be],ke={key:1},Ce=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ve=[Ce],Se={class:"p-6"},Me=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),$e={class:"mt-6 flex justify-end"},Ee={__name:"List",props:["data","search","permissions"],setup(o){const{form:h,search:a,sort:_,fetchData:p,sortKey:b,sortDirection:M}=H("users.index"),g=V(!1),v=V(null),$=[{field:"first_name",label:"NAME",sortable:!0,multiFieldSort:["first_name","last_name"]},{field:"email",label:"EMAIL",sortable:!0},{field:"role_id",label:"ROLE",sortable:!0},{field:"status",label:"STATUS",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],U=d=>{v.value=d,g.value=!0},w=()=>{g.value=!1},B=()=>{h.delete(route("users.destroy",{id:v.value}),{onSuccess:()=>w()})},L=(d,c)=>{h.post(route("users.activation",{id:c,status:d}),{})};return(d,c)=>(s(),i(x,null,[n(r(I),{title:"Users"}),n(E,null,{default:l(()=>[e("div",Q,[e("div",W,[X,e("div",Y,[e("div",ee,[te,z(e("input",{type:"text","onUpdate:modelValue":c[0]||(c[0]=t=>j(a)?a.value=t:null),onInput:c[1]||(c[1]=(...t)=>r(p)&&r(p)(...t)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for users..."},null,544),[[N,r(a)]])]),o.permissions.canCreateUser?(s(),i("div",se,[n(O,{href:d.route("users.create")},{default:l(()=>[f(" Add User ")]),_:1},8,["href"])])):u("",!0)])]),e("div",oe,[e("div",ae,[e("table",le,[e("thead",ie,[e("tr",ne,[(s(),i(x,null,S($,(t,k)=>e("th",{key:k,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:A=>r(_)(t.field,t.sortable)},[f(m(t.label)+" ",1),t.sortable?(s(),y(Z,{key:0,isSorted:r(b)===t.field,direction:r(M)},null,8,["isSorted","direction"])):u("",!0)],8,re)),64))])]),o.data.data&&o.data.data.length>0?(s(),i("tbody",de,[(s(!0),i(x,null,S(o.data.data,(t,k)=>(s(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",ce,m(t.first_name)+" "+m(t.last_name),1),e("td",ue,m(t.email),1),e("td",me,m(t.roles[0].name),1),e("td",he,[n(P,{switchValue:t.status,userId:t.id,onUpdateSwitchValue:L},null,8,["switchValue","userId"])]),e("td",fe,[e("div",_e,[n(T,{align:"right",width:"48"},{trigger:l(()=>[pe]),content:l(()=>[o.permissions.canEditUser?(s(),y(F,{key:0,href:d.route("users.edit",{id:t.id})},{svg:l(()=>[ge]),text:l(()=>[we]),_:2},1032,["href"])):u("",!0),o.permissions.canDeleteUser?(s(),i("button",{key:1,type:"button",onClick:A=>U(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ve,8,xe)):u("",!0)]),_:2},1024)])])]))),128))])):(s(),i("tbody",ke,Ve))])])]),o.data.data&&o.data.data.length>0?(s(),y(K,{key:0,class:"mt-6",links:o.data.links},null,8,["links"])):u("",!0)]),n(D,{show:g.value,onClose:w},{default:l(()=>[e("div",Se,[Me,e("div",$e,[n(R,{onClick:w},{default:l(()=>[f(" Cancel ")]),_:1}),n(G,{class:"ml-3",onClick:B},{default:l(()=>[f(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Ee as default};
