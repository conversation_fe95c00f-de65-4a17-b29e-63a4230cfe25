import{r as w,b as c,d as u,e as l,u as m,f as i,F as M,Z as L,h as e,y as x,l as d,t as a,j as g,k as q,q as E,O as k,n as _}from"./app-e1dc0e85.js";import{_ as I}from"./AdminLayout-bed0e8e1.js";import{_ as p}from"./SearchableDropdownNew-b65b4fe7.js";import{_ as f}from"./InputLabel-b452155c.js";import{_ as T}from"./CreateButton-5d60253c.js";import{_ as G}from"./Pagination-bcb92389.js";import{M as Y,_ as Z}from"./Modal-121db146.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const J={class:"animate-top"},K={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},Q={class:"flex justify-between items-center mb-6"},R=e("div",null,[e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Tasks"),e("p",{class:"text-sm text-gray-600 mt-1"},"Manage your tasks and follow-ups")],-1),W={class:"flex space-x-2"},X={class:"sm:flex-none"},ee={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},te={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},se={class:"flex items-center"},oe=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1),le={class:"ml-4"},ne=e("p",{class:"text-sm font-medium text-blue-600"},"Total Tasks",-1),re={class:"text-2xl font-semibold text-blue-900"},ae={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},ie={class:"flex items-center"},de=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1),ce={class:"ml-4"},ue=e("p",{class:"text-sm font-medium text-yellow-600"},"Pending",-1),me={class:"text-2xl font-semibold text-yellow-900"},ge={class:"bg-red-50 border border-red-200 rounded-lg p-4"},he={class:"flex items-center"},xe=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1),_e={class:"ml-4"},pe=e("p",{class:"text-sm font-medium text-red-600"},"Overdue",-1),fe={class:"text-2xl font-semibold text-red-900"},ve={class:"bg-green-50 border border-green-200 rounded-lg p-4"},be={class:"flex items-center"},ye=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1),we={class:"ml-4"},ke=e("p",{class:"text-sm font-medium text-green-600"},"Due Today",-1),Ve={class:"text-2xl font-semibold text-green-900"},Ce={class:"bg-gray-50 p-4 rounded-lg mb-6"},Me={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Te={class:"relative mt-2"},je={class:"relative mt-2"},Be={class:"relative mt-2"},Se={class:"relative mt-2"},Oe={key:0,class:"text-center py-12"},$e=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),Ae=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No tasks found",-1),De=e("p",{class:"mt-1 text-sm text-gray-500"},"Get started by creating a new task.",-1),ze={class:"mt-6"},Ne=e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Ue={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6"},He={class:"flex items-start justify-between"},Fe={class:"flex-1"},Pe={class:"text-sm font-semibold text-gray-900 mb-1"},Le={key:0,class:"text-xs text-gray-500"},qe={class:"flex flex-col items-end space-y-2"},Ee={class:"space-y-2 mb-4"},Ie={class:"flex items-center text-sm text-gray-700"},Ge=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1),Ye=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),Ze={key:0,class:"ml-1 text-red-500"},Je={class:"flex items-center text-xs text-gray-700"},Ke={class:"flex space-x-2"},Qe=["onClick"],Re={class:"p-6"},We=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to complete this task? ",-1),Xe={class:"mt-6 flex justify-end space-x-4"},dt={__name:"Index",props:{tasks:Object,users:Array,stats:Object,status:Array,types:Array,priorities:Array},setup(n){const r=w({status:"",type:"",priority:"",assigned_to:""}),j=s=>{r.value.status=s,h()},B=s=>{r.value.type=s,h()},S=s=>{r.value.priority=s,h()},O=s=>{r.value.assigned_to=s,h()},h=()=>{k.get(route("tasks.index"),r.value,{preserveState:!0,preserveScroll:!0})},v=w(!1),V=w(null),$=s=>{V.value=s,v.value=!0},b=()=>{v.value=!1},A=()=>{k.post(route("tasks.complete",V.value),{onSuccess:()=>{b(),k.reload()}})},D=s=>{const o=new Date(s),t=String(o.getDate()).padStart(2,"0"),C=String(o.getMonth()+1).padStart(2,"0"),P=o.getFullYear();return`${t}/${C}/${P}`},z=s=>s.replace("_"," ").replace(/\b\w/g,o=>o.toUpperCase()),N=s=>s.replace("_"," ").replace(/\b\w/g,o=>o.toUpperCase()),y=s=>s.status!=="completed"&&new Date(s.due_date)<new Date,U=s=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[s]||"bg-gray-100 text-gray-800",H=s=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",F=s=>({pending:"bg-yellow-100 text-yellow-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800";return(s,o)=>(c(),u(M,null,[l(m(L),{title:"Tasks"}),l(I,null,{default:i(()=>[e("div",J,[e("div",K,[e("div",Q,[R,e("div",W,[l(m(x),{href:s.route("tasks.dashboard"),class:"inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700"},{default:i(()=>[d(" 📊 Dashboard ")]),_:1},8,["href"]),e("div",X,[l(T,{href:s.route("tasks.create")},{default:i(()=>[d(" Add Task ")]),_:1},8,["href"])])])]),e("div",ee,[e("div",te,[e("div",se,[oe,e("div",le,[ne,e("p",re,a(n.stats.total),1)])])]),e("div",ae,[e("div",ie,[de,e("div",ce,[ue,e("p",me,a(n.stats.pending),1)])])]),e("div",ge,[e("div",he,[xe,e("div",_e,[pe,e("p",fe,a(n.stats.overdue),1)])])]),e("div",ve,[e("div",be,[ye,e("div",we,[ke,e("p",Ve,a(n.stats.due_today),1)])])])]),e("div",Ce,[e("div",Me,[e("div",null,[l(f,{for:"agent_filter",value:"Status"}),e("div",Te,[l(p,{options:n.status,modelValue:r.value.status,"onUpdate:modelValue":o[0]||(o[0]=t=>r.value.status=t),onOnchange:j},null,8,["options","modelValue"])])]),e("div",null,[l(f,{for:"agent_filter",value:"Type"}),e("div",je,[l(p,{options:n.types,modelValue:r.value.type,"onUpdate:modelValue":o[1]||(o[1]=t=>r.value.type=t),onOnchange:B},null,8,["options","modelValue"])])]),e("div",null,[l(f,{for:"agent_filter",value:"Priority"}),e("div",Be,[l(p,{options:n.priorities,modelValue:r.value.priority,"onUpdate:modelValue":o[2]||(o[2]=t=>r.value.priority=t),onOnchange:S},null,8,["options","modelValue"])])]),e("div",null,[l(f,{for:"agent_filter",value:"Assigned To"}),e("div",Se,[l(p,{options:n.users,modelValue:r.value.assigned_to,"onUpdate:modelValue":o[3]||(o[3]=t=>r.value.assigned_to=t),onOnchange:O},null,8,["options","modelValue"])])])])]),n.tasks.data.length===0?(c(),u("div",Oe,[$e,Ae,De,e("div",ze,[l(m(x),{href:s.route("tasks.create"),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"},{default:i(()=>[Ne,d(" New Task ")]),_:1},8,["href"])])])):g("",!0)]),e("div",Ue,[(c(!0),u(M,null,q(n.tasks.data,t=>(c(),u("div",{key:t.id,class:"bg-white border rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",He,[e("div",Fe,[e("h3",Pe,a(t.title),1),t.lead?(c(),u("p",Le," 📋 "+a(t.lead.client_name)+" ("+a(t.lead.company_name)+") ",1)):g("",!0)]),e("div",qe,[e("span",{class:_(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",H(t.priority)])},a(t.priority),3),e("span",{class:_(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",F(t.status)])},a(N(t.status)),3)])]),e("div",Ee,[e("div",Ie,[Ge,d(" "+a(t.assigned_to.first_name)+" "+a(t.assigned_to.last_name),1)]),e("div",{class:_(["flex items-center text-xs",{"text-red-600 font-semibold":y(t),"text-gray-600":!y(t)}])},[Ye,d(" "+a(D(t.due_date))+" ",1),y(t)?(c(),u("span",Ze,"⚠️ Overdue")):g("",!0)],2),e("div",Je,[e("span",{class:_(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",U(t.type)])},a(z(t.type)),3)])]),e("div",Ke,[l(m(x),{href:s.route("tasks.show",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100"},{default:i(()=>[d(" View ")]),_:2},1032,["href"]),l(m(x),{href:s.route("tasks.edit",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-green-600 bg-green-50 rounded-md hover:bg-green-100"},{default:i(()=>[d(" Edit ")]),_:2},1032,["href"]),t.status!=="completed"?(c(),u("button",{key:0,onClick:C=>$(t.id),class:"flex-1 px-3 py-2 text-xs font-semibold text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"}," ✓ Complete ",8,Qe)):g("",!0)])]))),128))]),n.tasks.links&&n.tasks.links.length>0?(c(),E(G,{key:0,class:"mt-6",links:n.tasks.links},null,8,["links"])):g("",!0)]),l(Y,{show:v.value,onClose:b},{default:i(()=>[e("div",Re,[We,e("div",Xe,[l(Z,{onClick:b},{default:i(()=>[d("Cancel")]),_:1}),l(T,{class:"w-40",onClick:A},{default:i(()=>[d(" Complete Task ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{dt as default};
