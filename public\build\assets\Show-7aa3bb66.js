import{_ as D,a as m}from"./AdminLayout-f9e7be5b.js";import{P as O}from"./PrimaryButton-a6d2214f.js";import{b as d,d as r,e as i,u as B,f as c,F as j,Z as I,g as t,t as s,k as x,i as l,n as P}from"./app-5d829b53.js";import"./_plugin-vue_export-helper-c27b6911.js";const U={class:"animate-top"},z={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},T={class:"text-3xl font-bold text-gray-900"},E=t("p",{class:"text-gray-600 mt-1"},"Order Details",-1),M={class:"flex flex-col sm:flex-row gap-3"},Q=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),V={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},F={class:"lg:col-span-2 space-y-8"},$={class:"bg-white shadow rounded-lg p-6"},Z=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Lead Information",-1),G={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},H=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),J={class:"mt-1 text-sm text-gray-700"},K=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),R={class:"mt-1 text-sm text-gray-700"},W=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Number",-1),X={class:"mt-1 text-sm text-gray-700"},Y=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Status",-1),tt={class:"mt-1 text-sm text-gray-700"},et={class:"bg-white shadow rounded-lg p-6"},st=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),ot={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),rt={class:"mt-1 text-sm text-gray-700"},lt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),at={class:"mt-1 text-sm text-gray-700"},it=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),nt={class:"mt-1 text-sm text-gray-700"},ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),mt={class:"mt-1 text-sm text-gray-700"},xt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),ht={class:"mt-1 text-sm text-gray-700"},_t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),gt={class:"mt-1 text-sm text-gray-700"},yt={key:0,class:"md:col-span-2"},ut=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),bt={class:"mt-1 text-sm text-gray-700"},ft={class:"bg-white shadow rounded-lg p-6"},wt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Information",-1),vt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},pt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),kt={key:0},qt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Quotation Number",-1),Nt={class:"mt-1 text-sm text-gray-700"},St=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),Lt={class:"mt-1 text-sm text-gray-700"},At={key:1},Ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Tracking Number",-1),Dt={class:"mt-1 text-sm text-gray-700"},Ot=t("label",{class:"block text-sm font-semibold text-gray-900"},"Expected Delivery",-1),Bt={class:"mt-1 text-sm text-gray-700"},jt={key:2},It=t("label",{class:"block text-sm font-semibold text-gray-900"},"Actual Delivery",-1),Pt={class:"mt-1 text-sm text-gray-700"},Ut={class:"bg-white shadow rounded-lg p-6"},zt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Quantities",-1),Tt={class:"overflow-x-auto"},Et={class:"min-w-full divide-y divide-gray-200"},Mt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Unit Price"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Total")])],-1),Qt={class:"bg-white divide-y divide-gray-200"},Vt={key:0},Ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Zt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Gt={key:1},Ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Kt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Rt={key:2},Wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Yt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},te={key:3},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},oe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},de={class:"bg-gray-50"},re=t("td",{colspan:"2",class:"px-6 py-4 text-sm font-semibold text-gray-900 text-right"},"Total Amount:",-1),le={class:"px-6 py-4 text-lg font-bold text-green-600"},ae={key:0,class:"bg-white shadow rounded-lg p-6"},ie=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),ne={class:"text-sm text-gray-700 whitespace-pre-wrap"},ce={class:"space-y-6"},me={class:"bg-white shadow rounded-lg p-6"},xe=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),he={class:"space-y-3 w-full"},_e=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),x(" Edit Order ")],-1),ge=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),x(" Back to Orders ")],-1),ye={class:"bg-white shadow rounded-lg p-6"},ue=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),be={class:"space-y-4"},fe={class:"flex items-start space-x-3"},we=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),ve=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Created",-1),pe={class:"text-xs text-gray-500"},ke={key:0,class:"flex items-start space-x-3"},qe=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),Ne=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Confirmed",-1),Se={class:"text-xs text-gray-500"},Le={key:1,class:"flex items-start space-x-3"},Ae=t("div",{class:"flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"},null,-1),Ce=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),De={class:"text-xs text-gray-500"},Pe={__name:"Show",props:{order:{type:Object,required:!0}},setup(e){const A=o=>({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-indigo-100 text-indigo-800",delivered:"bg-green-100 text-green-800"})[o]||"bg-gray-100 text-gray-800",n=o=>o?new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",a=o=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(o),C=o=>({pending:"Pending",confirmed:"Confirmed",under_production:"Under Production",shipped:"Shipped",delivered:"Delivered"})[o]||o;return(o,h)=>(d(),r(j,null,[i(B(I),{title:`Order - ${e.order.order_number}`},null,8,["title"]),i(D,null,{default:c(()=>{var _,g,y,u,b,f,w,v,p,k,q,N,S,L;return[t("div",U,[t("div",z,[t("div",null,[t("h1",T,s(e.order.order_number),1),E]),t("div",M,[i(m,{href:o.route("orders.edit",e.order.id)},{svg:c(()=>[i(O,{class:"w-full items-center"},{default:c(()=>[Q,x(" Edit Order ")]),_:1})]),_:1},8,["href"])])]),t("div",V,[t("div",F,[t("div",$,[Z,t("div",G,[t("div",null,[H,t("p",J,s(((_=e.order.lead)==null?void 0:_.client_name)||"N/A"),1)]),t("div",null,[K,t("p",R,s(((y=(g=e.order.lead)==null?void 0:g.county)==null?void 0:y.name)||"N/A"),1)]),t("div",null,[W,t("p",X,s(((u=e.order.lead)==null?void 0:u.lead_number)||"N/A"),1)]),t("div",null,[Y,t("p",tt,s(((b=e.order.lead)==null?void 0:b.status)||"N/A"),1)])])]),t("div",et,[st,t("div",ot,[t("div",null,[dt,t("p",rt,s(((f=e.order.lead)==null?void 0:f.dimensions)||"N/A"),1)]),t("div",null,[lt,t("p",at,s(((w=e.order.lead)==null?void 0:w.open_size)||"N/A"),1)]),t("div",null,[it,t("p",nt,s(((v=e.order.lead)==null?void 0:v.box_style)||"N/A"),1)]),t("div",null,[ct,t("p",mt,s(((p=e.order.lead)==null?void 0:p.stock)||"N/A"),1)]),t("div",null,[xt,t("p",ht,s(((k=e.order.lead)==null?void 0:k.lamination)||"N/A"),1)]),t("div",null,[_t,t("p",gt,s(((q=e.order.lead)==null?void 0:q.printing)||"N/A"),1)]),(N=e.order.lead)!=null&&N.add_ons?(d(),r("div",yt,[ut,t("p",bt,s(e.order.lead.add_ons),1)])):l("",!0)])]),t("div",ft,[wt,t("div",vt,[t("div",null,[pt,t("span",{class:P(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",A(e.order.status)])},s(C(e.order.status)),3)]),e.order.quotation?(d(),r("div",kt,[qt,t("p",Nt,s(e.order.quotation.quotation_number),1)])):l("",!0),t("div",null,[St,t("p",Lt,s((S=e.order.creator)==null?void 0:S.first_name)+" "+s((L=e.order.creator)==null?void 0:L.last_name),1)]),e.order.tracking_number?(d(),r("div",At,[Ct,t("p",Dt,s(e.order.tracking_number),1)])):l("",!0),t("div",null,[Ot,t("p",Bt,s(n(e.order.expected_delivery)),1)]),e.order.actual_delivery?(d(),r("div",jt,[It,t("p",Pt,s(n(e.order.actual_delivery)),1)])):l("",!0)])]),t("div",Ut,[zt,t("div",Tt,[t("table",Et,[Mt,t("tbody",Qt,[e.order.selected_qty_1&&e.order.price_qty_1?(d(),r("tr",Vt,[t("td",Ft,s(parseInt(e.order.selected_qty_1).toLocaleString())+" pcs",1),t("td",$t,s(a(e.order.price_qty_1)),1),t("td",Zt,s(a(e.order.selected_qty_1*e.order.price_qty_1)),1)])):l("",!0),e.order.selected_qty_2&&e.order.price_qty_2?(d(),r("tr",Gt,[t("td",Ht,s(parseInt(e.order.selected_qty_2).toLocaleString())+" pcs",1),t("td",Jt,s(a(e.order.price_qty_2)),1),t("td",Kt,s(a(e.order.selected_qty_2*e.order.price_qty_2)),1)])):l("",!0),e.order.selected_qty_3&&e.order.price_qty_3?(d(),r("tr",Rt,[t("td",Wt,s(parseInt(e.order.selected_qty_3).toLocaleString())+" pcs",1),t("td",Xt,s(a(e.order.price_qty_3)),1),t("td",Yt,s(a(e.order.selected_qty_3*e.order.price_qty_3)),1)])):l("",!0),e.order.selected_qty_4&&e.order.price_qty_4?(d(),r("tr",te,[t("td",ee,s(parseInt(e.order.selected_qty_4).toLocaleString())+" pcs",1),t("td",se,s(a(e.order.price_qty_4)),1),t("td",oe,s(a(e.order.selected_qty_4*e.order.price_qty_4)),1)])):l("",!0)]),t("tfoot",de,[t("tr",null,[re,t("td",le,s(a(e.order.total_amount)),1)])])])])]),e.order.notes?(d(),r("div",ae,[ie,t("p",ne,s(e.order.notes),1)])):l("",!0)]),t("div",ce,[t("div",me,[xe,t("div",he,[i(m,{href:o.route("orders.edit",e.order.id),class:"w-full"},{svg:c(()=>[_e]),_:1},8,["href"]),i(m,{href:o.route("orders.index"),class:"w-full"},{svg:c(()=>[ge]),_:1},8,["href"])])]),t("div",ye,[ue,t("div",be,[t("div",fe,[we,t("div",null,[ve,t("p",pe,s(n(e.order.created_at)),1)])]),e.order.is_confirmed?(d(),r("div",ke,[qe,t("div",null,[Ne,t("p",Se,s(n(e.order.confirmed_at)),1)])])):l("",!0),e.order.updated_at!==e.order.created_at?(d(),r("div",Le,[Ae,t("div",null,[Ce,t("p",De,s(n(e.order.updated_at)),1)])])):l("",!0)])])])])])]}),_:1})],64))}};export{Pe as default};
