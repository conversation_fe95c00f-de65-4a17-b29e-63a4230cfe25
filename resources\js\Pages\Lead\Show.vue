<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps({
    leads: {
        type: Object,
        required: true
    }
});

const getStatusClass = (status) => {
    const classes = {
        'draft': 'bg-gray-100 text-gray-800',
        'sent': 'bg-blue-100 text-blue-800',
        'accepted': 'bg-green-100 text-green-800',
        'rejected': 'bg-red-100 text-red-800',
        'expired': 'bg-yellow-100 text-yellow-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};
</script>

<template>
    <Head :title="`Lead - ${leads.lead_number}`" />

    <AdminLayout>
        <div class="animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ leads.lead_number }}</h1>
                    <p class="text-gray-600 mt-1">Leads Details</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <ActionLink :href="route('leads.edit', leads.id)">
                        <template #svg>
                            <PrimaryButton  class="w-full items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                                </svg>
                                Edit leads
                            </PrimaryButton>
                        </template>
                    </ActionLink>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Basic Information -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Client Name</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.client_name }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">County</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.county?.name || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Status</label>
                                <span :class="['inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1', getStatusClass(leads.status)]">
                                    {{ leads.status.charAt(0).toUpperCase() + leads.status.slice(1) }}
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Created By</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.creator?.first_name }} {{ leads.creator?.last_name }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Product Specifications -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Product Specifications</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Dimensions</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.dimensions }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Open Size</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.open_size }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Box Style</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.box_style }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Stock</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.stock }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Lamination</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.lamination }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Printing</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.printing }}</p>
                            </div>
                            <div v-if="leads.add_ons" class="md:col-span-2">
                                <label class="block text-sm font-semibold text-gray-900">Add-ons</label>
                                <p class="mt-1 text-sm text-gray-700">{{ leads.add_ons }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity Details -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Quantity Details</h2>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider">Requested</th>
                                        <th class="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider">Confirmed</th>
                                        <th class="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-if="leads.qty_1">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Quantity 1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(leads.qty_1).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                            <span v-if="leads.confirmed_qty_1" class="text-green-600 font-medium">
                                                {{ parseInt(leads.confirmed_qty_1).toLocaleString() }} pcs
                                            </span>
                                            <span v-else class="text-gray-400">Not confirmed</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span v-if="leads.confirmed_qty_1" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                ✓ Confirmed
                                            </span>
                                            <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                Pending
                                            </span>
                                        </td>
                                    </tr>
                                    <tr v-if="leads.qty_2">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Quantity 2</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(leads.qty_2).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                            <span v-if="leads.confirmed_qty_2" class="text-green-600 font-medium">
                                                {{ parseInt(leads.confirmed_qty_2).toLocaleString() }} pcs
                                            </span>
                                            <span v-else class="text-gray-400">Not confirmed</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span v-if="leads.confirmed_qty_2" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                ✓ Confirmed
                                            </span>
                                            <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                Pending
                                            </span>
                                        </td>
                                    </tr>
                                    <tr v-if="leads.qty_3">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Quantity 3</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(leads.qty_3).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                            <span v-if="leads.confirmed_qty_3" class="text-green-600 font-medium">
                                                {{ parseInt(leads.confirmed_qty_3).toLocaleString() }} pcs
                                            </span>
                                            <span v-else class="text-gray-400">Not confirmed</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span v-if="leads.confirmed_qty_3" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                ✓ Confirmed
                                            </span>
                                            <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                Pending
                                            </span>
                                        </td>
                                    </tr>
                                    <tr v-if="leads.qty_4">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Quantity 4</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ parseInt(leads.qty_4).toLocaleString() }} pcs</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                            <span v-if="leads.confirmed_qty_4" class="text-green-600 font-medium">
                                                {{ parseInt(leads.confirmed_qty_4).toLocaleString() }} pcs
                                            </span>
                                            <span v-else class="text-gray-400">Not confirmed</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span v-if="leads.confirmed_qty_4" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                ✓ Confirmed
                                            </span>
                                            <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                Pending
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Order Confirmation Info -->
                        <div v-if="leads.order_confirmed_at" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-green-800">Order Confirmed</p>
                                    <p class="text-xs text-green-600">{{ formatDate(leads.order_confirmed_at) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div v-if="leads.notes" class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Notes</h2>
                        <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ leads.notes }}</p>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h3>
                        <div class="space-y-3 w-full">
                            <ActionLink :href="route('leads.edit', leads.id)" class="w-full">
                                <template #svg>
                                    <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                                        </svg>
                                        Edit Lead
                                    </button>
                                </template>
                            </ActionLink>
                            <ActionLink :href="route('leads.index')" class="w-full">
                                <template #svg>
                                    <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
                                        </svg>
                                        Back to List
                                    </button>
                                </template>
                            </ActionLink>
                        </div>
                    </div>

                    <!-- Documents -->
                    <div v-if="leads.documents && leads.documents.length > 0" class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Documents</h3>
                        <div class="space-y-3">
                            <div v-for="document in leads.documents" :key="document.id" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">{{ document.orignal_name }}</span>
                                </div>
                                <a :href="'/uploads/leads/' + document.name" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">View</a>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Timeline</h3>
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">leads Created</p>
                                    <p class="text-xs text-gray-500">{{ formatDate(leads.created_at) }}</p>
                                </div>
                            </div>
                            <div v-if="leads.updated_at !== leads.created_at" class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900">Last Updated</p>
                                    <p class="text-xs text-gray-500">{{ formatDate(leads.updated_at) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
