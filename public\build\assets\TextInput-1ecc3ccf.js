import{i as p,H as d,b as r,d as l,h as f,t as g,r as _,o as h}from"./app-e21f56bc.js";const v={class:"text-sm text-red-600"},V={__name:"InputError",props:{message:{type:String}},setup(e){return(n,u)=>p((r(),l("div",null,[f("p",v,g(e.message),1)],512)),[[d,e.message]])}},x=["value"],I={__name:"TextInput",props:{modelValue:{required:!0},numeric:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{expose:n,emit:u}){const c=e,t=_(null),i=s=>s.replace(/\D/g,""),m=s=>{const a=s.target.value,o=c.numeric?i(a):a;o==""&&(t.value.value=""),u("update:modelValue",o)};return h(()=>{t.value.hasAttribute("autofocus")&&t.value.focus()}),n({focus:()=>t.value.focus()}),(s,a)=>(r(),l("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:e.modelValue,onInput:m,autocomplete:"off",ref_key:"input",ref:t},null,40,x))}};export{I as _,V as a};
