import{r as h,c as V,b as n,d as i,e as l,u as g,f as r,F as b,Z as oe,g as e,h as L,v as ae,E as le,k,i as x,j as O,m as N,t as _,y as ne,n as ie,O as T}from"./app-890b4b78.js";import{_ as re,b as de,a as U}from"./AdminLayout-bc98fd09.js";import{_ as ce}from"./CreateButton-28e18e07.js";import{M as ue,_ as me}from"./Modal-44025e29.js";import{D as _e}from"./DangerButton-dad128b5.js";import{s as pe,_ as he,a as ge}from"./ArrowIcon-fefb25cd.js";import{_ as $}from"./SearchableDropdownNew-813a9f4d.js";import{_ as C}from"./InputLabel-795b7075.js";/* empty css                                                              */import"./_plugin-vue_export-helper-c27b6911.js";const ve={class:"animate-top"},fe={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},xe=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Quotations")],-1),ye={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},we={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},be=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ke={key:0,class:"sm:flex-none"},Ce={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Ae={class:"flex justify-between mb-2"},Se={class:"flex"},Me=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Ve={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Oe={class:"sm:col-span-4"},Ne={class:"relative mt-2"},$e={class:"sm:col-span-4"},je={class:"relative mt-2"},De={class:"sm:col-span-4"},Le={class:"relative mt-2"},Te={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Ue={class:"shadow rounded-lg"},Be={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ee={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Qe={class:"border-b-2"},Pe=["onClick"],ze={key:0},Fe={class:"px-4 py-2.5 min-w-28"},Ie={class:"px-4 py-2.5 min-w-28"},Re={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},We={class:"px-4 py-2.5"},Ge={class:"px-4 py-2.5 font-semibold text-green-600"},He={class:"px-4 py-2.5"},Ke={key:0,class:"flex items-center space-x-2"},Ye=["onUpdate:modelValue","onChange"],Ze=["value"],Je=["onClick"],Xe={key:1,class:"flex items-center space-x-2"},qe=["onClick"],et={class:"px-4 py-2.5"},tt={class:"items-center px-4 py-2.5"},st={class:"flex items-center justify-start gap-4"},ot=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),at=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),lt=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),nt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),it=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),rt=["onClick"],dt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),ct=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),ut=[dt,ct],mt=["onClick"],_t=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),pt=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),ht=[_t,pt],gt={key:1},vt=e("tr",{class:"bg-white"},[e("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),ft=[vt],xt={class:"p-6"},yt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this quotation? ",-1),wt={class:"mt-6 flex justify-end"},Dt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status"],setup(d){const v=d,{form:A,search:y,sort:B,fetchData:bt,sortKey:E,sortDirection:Q}=pe("quotations.index"),S=h(!1),j=h(null),P=[{id:"draft",name:"Draft"},{id:"sent",name:"Sent"},{id:"accepted",name:"Accepted"},{id:"rejected",name:"Rejected"},{id:"expired",name:"Expired"}],z=V(()=>[{id:"",name:"All Agents"},...v.agents]),F=V(()=>[{id:"",name:"All Counties"},...v.counties]),I=V(()=>[{id:"",name:"All Status"},...v.statusOptions]),R=[{field:"quotation_number",label:"QUOTATION NO",sortable:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0},{field:"county.name",label:"COUNTY",sortable:!1},{field:"total_amount",label:"TOTAL AMOUNT",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"creator.first_name",label:"AGENT",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],W=s=>{j.value=s,S.value=!0},M=()=>{S.value=!1},G=()=>{A.delete(route("quotations.destroy",{quotation:j.value}),{onSuccess:()=>M()})},H=s=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",accepted:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",expired:"bg-yellow-100 text-yellow-800"})[s]||"bg-gray-100 text-gray-800",c=h(v.agent_id||""),u=h(v.county_id||""),m=h(v.status||""),f=h(""),p=h({}),K=(s,o)=>{c.value=s,w(f.value,c.value,u.value,m.value)},Y=(s,o)=>{u.value=s,w(f.value,c.value,u.value,m.value)},Z=(s,o)=>{m.value=s,w(f.value,c.value,u.value,m.value)},w=(s,o,t,a)=>{f.value=s;const D=o===""?null:o,te=t===""?null:t,se=a===""?null:a;A.get(route("quotations.index",{search:s,agent_id:D,county_id:te,status:se}),{preserveState:!0})},J=(s,o)=>{p.value[s]=o},X=s=>{delete p.value[s]},q=(s,o)=>{T.post(route("quotations.update-status",s),{status:o},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete p.value[s];const a=new URLSearchParams(window.location.search).get("page")||1;T.get(route("quotations.index"),{search:f.value,agent_id:c.value===""?null:c.value,county_id:u.value===""?null:u.value,status:m.value===""?null:m.value,page:a},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})},ee=s=>{window.open(route("quotations.pdf",s),"_blank")};return(s,o)=>(n(),i(b,null,[l(g(oe),{title:"Quotations"}),l(re,null,{default:r(()=>[e("div",ve,[e("div",fe,[xe,e("div",ye,[e("div",we,[be,L(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>le(y)?y.value=t:null),onInput:o[1]||(o[1]=t=>w(g(y),c.value,u.value,m.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for quotations..."},null,544),[[ae,g(y)]])]),d.permissions.canCreateQuotation?(n(),i("div",ke,[l(ce,{href:s.route("quotations.create")},{default:r(()=>[k(" Add Quotation ")]),_:1},8,["href"])])):x("",!0)])]),e("div",Ce,[e("div",Ae,[e("div",Se,[Me,l(C,{for:"filters",value:"Filters"})])]),e("div",Ve,[e("div",Oe,[l(C,{for:"agent_filter",value:"Agents"}),e("div",Ne,[l($,{options:z.value,modelValue:c.value,"onUpdate:modelValue":o[2]||(o[2]=t=>c.value=t),onOnchange:K},null,8,["options","modelValue"])])]),e("div",$e,[l(C,{for:"county_filter",value:"Counties"}),e("div",je,[l($,{options:F.value,modelValue:u.value,"onUpdate:modelValue":o[3]||(o[3]=t=>u.value=t),onOnchange:Y},null,8,["options","modelValue"])])]),e("div",De,[l(C,{for:"status_filter",value:"Status"}),e("div",Le,[l($,{options:I.value,modelValue:m.value,"onUpdate:modelValue":o[4]||(o[4]=t=>m.value=t),onOnchange:Z},null,8,["options","modelValue"])])])])]),e("div",Te,[e("div",Ue,[e("table",Be,[e("thead",Ee,[e("tr",Qe,[(n(),i(b,null,O(R,(t,a)=>e("th",{key:a,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:D=>g(B)(t.field,t.sortable)},[k(_(t.label)+" ",1),t.sortable?(n(),N(ge,{key:0,isSorted:g(E)===t.field,direction:g(Q)},null,8,["isSorted","direction"])):x("",!0)],8,Pe)),64))])]),d.data.data&&d.data.data.length>0?(n(),i("tbody",ze,[(n(!0),i(b,null,O(d.data.data,t=>(n(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Fe,_(t.quotation_number),1),e("td",Ie,_(t.lead?t.lead.lead_number:"N/A"),1),e("td",Re,_(t.client_name),1),e("td",We,_(t.county?t.county.name:"N/A"),1),e("td",Ge,"$"+_(parseFloat(t.total_amount).toFixed(2)),1),e("td",He,[p.value[t.id]!==void 0?(n(),i("div",Ke,[L(e("select",{"onUpdate:modelValue":a=>p.value[t.id]=a,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:a=>q(t.id,p.value[t.id])},[(n(),i(b,null,O(P,a=>e("option",{key:a.id,value:a.id},_(a.name),9,Ze)),64))],40,Ye),[[ne,p.value[t.id]]]),e("button",{onClick:a=>X(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,Je)])):(n(),i("div",Xe,[e("span",{class:ie(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",H(t.status)]),onClick:a=>J(t.id,t.status),title:"Click to edit status"},_(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,qe)]))]),e("td",et,_(t.creator?t.creator.first_name:"N/A"),1),e("td",tt,[e("div",st,[l(de,{align:"right",width:"48"},{trigger:r(()=>[ot]),content:r(()=>[l(U,{href:s.route("quotations.show",{quotation:t.id})},{svg:r(()=>[at]),text:r(()=>[lt]),_:2},1032,["href"]),d.permissions.canEditQuotation?(n(),N(U,{key:0,href:s.route("quotations.edit",{quotation:t.id})},{svg:r(()=>[nt]),text:r(()=>[it]),_:2},1032,["href"])):x("",!0),e("button",{type:"button",onClick:a=>ee(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ut,8,rt),d.permissions.canDeleteQuotation?(n(),i("button",{key:1,type:"button",onClick:a=>W(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ht,8,mt)):x("",!0)]),_:2},1024)])])]))),128))])):(n(),i("tbody",gt,ft))])])]),d.data.data&&d.data.data.length>0?(n(),N(he,{key:0,class:"mt-6",links:d.data.links},null,8,["links"])):x("",!0)]),l(ue,{show:S.value,onClose:M},{default:r(()=>[e("div",xt,[yt,e("div",wt,[l(me,{onClick:M},{default:r(()=>[k("Cancel")]),_:1}),l(_e,{class:"ml-3",onClick:G,disabled:g(A).processing},{default:r(()=>[k(" Delete Quotation ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Dt as default};
