import{b as t,d as n,h as c,F as r,k as g,e as i,u as o,y as a,n as u,j as d}from"./app-e1dc0e85.js";const f={key:0},h={class:"flex flex-wrap justify-end isolate rounded-md"},m={key:0},y={key:1},p={__name:"Pagination",props:["links"],setup(s){return(x,_)=>s.links.length>1?(t(),n("div",f,[c("div",h,[(t(!0),n(r,null,g(s.links,(e,l)=>(t(),n(r,{key:l},[e.url===null?(t(),n("div",m,[i(o(a),{innerHTML:e.label,href:"#",class:"inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 bg-white hover:bg-gray-50 focus:z-20 focus:outline-offset-0"},null,8,["innerHTML"])])):(t(),n("div",y,[i(o(a),{innerHTML:e.label,href:e.url,class:u([{"bg-indigo-600 text-white hover:bg-indigo-600":e.active},"bg-white inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"])},null,8,["innerHTML","href","class"])]))],64))),128))])])):d("",!0)}};export{p as _};
