import{_ as i}from"./AdminLayout-bed0e8e1.js";/* empty css                                                              */import{b as o,d as m,e as s,u as r,f as a,F as l,Z as n,h as t}from"./app-e1dc0e85.js";import c from"./UpdateProfileInformationForm-27de95b5.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./TextInput-f729f5f2.js";import"./InputLabel-b452155c.js";import"./PrimaryButton-5df40e25.js";import"./TextArea-2666d68e.js";const u=t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),d={class:""},_={class:"max-w-7xl mx-auto"},f={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},k={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(e){return(p,h)=>(o(),m(l,null,[s(r(n),{title:"Profile"}),s(i,null,{header:a(()=>[u]),default:a(()=>[t("div",d,[t("div",_,[t("div",f,[s(c,{"must-verify-email":e.mustVerifyEmail,status:e.status,class:"max-w-xl"},null,8,["must-verify-email","status"])])])])]),_:1})],64))}};export{k as default};
