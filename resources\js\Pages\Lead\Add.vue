<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SearchableDropdown from '@/Components/SearchableDropdownNew.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import SvgLink from '@/Components/ActionLink.vue';
import { Head } from '@inertiajs/vue3';
import { ref } from 'vue';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps({
    counties: {
        type: Array,
        required: true
    }
});

const form = useForm('post', '/leads', {
    client_name: '',
    county_id: '',
    dimensions: '',
    open_size: '',
    box_style: '',
    stock: '',
    lamination: '',
    printing: '',
    add_ons: '',
    qty_1: '',
    qty_2: '',
    qty_3: '',
    qty_4: '',
    notes: '',
    document: ''
});

const submit = () => form.submit({
    preserveScroll: true,
    onSuccess: () => form.reset(),
});

const setCounty = (id, name) => {
    form.county_id = id;
};

const handleDocument = (file) => {
    form.document = file;
};

</script>

<template>
    <Head title="Add Lead" />
    <AdminLayout>
        <div class="animate-top">
            <div class="bg-white p-4 shadow sm:p-6 rounded-lg border">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900">Add New Lead</h2>
                <form @submit.prevent="submit" class="">
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-2">
                                <InputLabel for="client_name" value="Client Name *" />
                                <TextInput
                                    id="client_name"
                                    type="text"
                                    v-model="form.client_name"
                                    required
                                    @change="form.validate('client_name')"
                                />
                                <InputError v-if="form.invalid('client_name')" :message="form.errors.client_name" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="county_id" value="Country *" />
                                <div class="relative mt-2">
                                    <SearchableDropdown
                                        :options="counties"
                                        @onchange="setCounty"
                                        required
                                        placeholder="Select Country"
                                    />
                                </div>
                                <InputError v-if="form.invalid('county_id')" :message="form.errors.county_id" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="dimensions" value="Dimensions *" />
                                <TextInput
                                    id="dimensions"
                                    type="text"
                                    v-model="form.dimensions"
                                    required
                                    @change="form.validate('dimensions')"
                                />
                                <InputError v-if="form.invalid('dimensions')" :message="form.errors.dimensions" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="open_size" value="Open Size  *" />
                                <TextInput
                                    id="open_size"
                                    type="text"
                                    v-model="form.open_size"
                                    required
                                    @change="form.validate('open_size')"
                                />
                                <InputError v-if="form.invalid('open_size')" :message="form.errors.open_size" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="box_style" value="Box Style *" />
                                <TextInput
                                    id="box_style"
                                    type="text"
                                    v-model="form.box_style"
                                    required
                                    @change="form.validate('box_style')"
                                />
                                <InputError v-if="form.invalid('box_style')" :message="form.errors.box_style" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="stock" value="Stock *" />
                                <TextInput
                                    id="stock"
                                    type="text"
                                    v-model="form.stock"
                                    required
                                    @change="form.validate('stock')"
                                />
                                <InputError v-if="form.invalid('stock')" :message="form.errors.stock" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="lamination" value="Lamination *" />
                                <TextInput
                                    id="lamination"
                                    type="text"
                                    v-model="form.lamination"
                                    required
                                    @change="form.validate('lamination')"
                                />
                                <InputError v-if="form.invalid('lamination')" :message="form.errors.lamination" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="printing" value="Printing *" />
                                <TextInput
                                    id="printing"
                                    type="text"
                                    v-model="form.printing"
                                    required
                                    @change="form.validate('printing')"
                                />
                                <InputError v-if="form.invalid('printing')" :message="form.errors.printing" />
                            </div>

                            <div class="sm:col-span-2">
                                <InputLabel for="add_ons" value="Add ons" />
                                <TextInput
                                    id="add_ons"
                                    type="text"
                                    v-model="form.add_ons"
                                    @change="form.validate('add_ons')"
                                />
                                <InputError v-if="form.invalid('add_ons')" :message="form.errors.add_ons" />
                            </div>


                            <div class="sm:col-span-1">
                                <InputLabel for="qty_1" value="QTY 1 *" />
                                <TextInput
                                    id="qty_1"
                                    type="text"
                                    v-model="form.qty_1"
                                    :numeric="true"
                                    required
                                    @change="form.validate('qty_1')"
                                />
                                <InputError v-if="form.invalid('qty_1')" :message="form.errors.qty_1" />
                            </div>

                            <div class="sm:col-span-1">
                                <InputLabel for="qty_2" value="QTY 2" />
                                <TextInput
                                    id="qty_2"
                                    type="text"
                                    :numeric="true"
                                    v-model="form.qty_2"
                                    @change="form.validate('qty_2')"
                                />
                                <InputError v-if="form.invalid('qty_2')" :message="form.errors.qty_2" />
                            </div>

                            <div class="sm:col-span-1">
                                <InputLabel for="qty_3" value="QTY 3" />
                                <TextInput
                                    id="qty_3"
                                    type="text"
                                    :numeric="true"
                                    v-model="form.qty_3"
                                    @change="form.validate('qty_3')"
                                />
                                <InputError v-if="form.invalid('qty_3')" :message="form.errors.qty_3" />
                            </div>

                            <div class="sm:col-span-1">
                                <InputLabel for="qty_4" value="QTY 4" />
                                <TextInput
                                    id="qty_4"
                                    type="text"
                                    :numeric="true"
                                    v-model="form.qty_4"
                                    @change="form.validate('qty_4')"
                                />
                                <InputError v-if="form.invalid('qty_4')" :message="form.errors.qty_4" />
                            </div>


                            <div class="sm:col-span-2">
                                <InputLabel for="note" value="Upload Documents"/>
                                <MultipleFileUpload
                                    inputId="document"
                                    inputName="document"
                                    @files="handleDocument"
                                />
                            </div>

                            <div class="sm:col-span-6">
                                <InputLabel for="notes" value="Notes"/>
                                <TextArea
                                    id="notes"
                                    type="text"
                                    :rows="3"
                                    v-model="form.notes"
                                    autocomplete="notes"
                                    @change="form.validate('notes')"
                                />
                                <InputError class="" :message="form.errors.notes" />
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                            <SvgLink :href="route('leads.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                        Cancel
                                    </button>
                                </template>
                            </SvgLink>
                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                        </Transition>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
