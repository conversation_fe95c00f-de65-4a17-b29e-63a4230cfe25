import{_ as v,a as m}from"./AdminLayout-536dfaf3.js";import{P as k}from"./PrimaryButton-04471aec.js";import{b as o,d as a,e as c,u as p,f as d,F as _,Z as L,g as t,t as s,k as r,n as C,i,j as B}from"./app-2e8279f3.js";import"./_plugin-vue_export-helper-c27b6911.js";const S={class:"animate-top"},j={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},D={class:"text-3xl font-bold text-gray-900"},N=t("p",{class:"text-gray-600 mt-1"},"Quotation Details",-1),V={class:"flex flex-col sm:flex-row gap-3"},A=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),P={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},z={class:"lg:col-span-2 space-y-8"},M={class:"bg-white shadow rounded-lg p-6"},Q=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1),I={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},U=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),F={class:"mt-1 text-sm text-gray-700"},E=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),H={class:"mt-1 text-sm text-gray-700"},T=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),$=t("label",{class:"block text-sm font-semibold text-gray-900"},"Valid Until",-1),G={class:"mt-1 text-sm text-gray-700"},O={key:0},Z=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Number",-1),J={class:"mt-1 text-sm text-gray-700"},K=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),R={class:"mt-1 text-sm text-gray-700"},W={class:"bg-white shadow rounded-lg p-6"},X=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),Y={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},tt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),et={class:"mt-1 text-sm text-gray-700"},st=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),ot={class:"mt-1 text-sm text-gray-700"},at=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),it={class:"mt-1 text-sm text-gray-700"},nt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),lt={class:"mt-1 text-sm text-gray-700"},ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),dt={class:"mt-1 text-sm text-gray-700"},rt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),ut={class:"mt-1 text-sm text-gray-700"},xt={key:0,class:"md:col-span-2"},mt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),ht={class:"mt-1 text-sm text-gray-700"},yt={class:"bg-white shadow rounded-lg p-6"},gt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Pricing Details",-1),_t={class:"overflow-x-auto"},bt={class:"min-w-full divide-y divide-gray-200"},wt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Unit Price"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Total")])],-1),ft={class:"bg-white divide-y divide-gray-200"},qt={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},vt={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},kt={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},pt={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Lt={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ct={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Bt={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},St={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},jt={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Dt={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Nt={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Vt={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},At={key:0,class:"bg-white shadow rounded-lg p-6"},Pt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),zt={class:"text-sm text-gray-700 whitespace-pre-wrap"},Mt={class:"space-y-6"},Qt={class:"bg-white shadow rounded-lg p-6"},It=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),Ut={class:"space-y-3 w-full"},Ft=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),r(" Edit Quotation ")],-1),Et=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),Ht=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),r(" Back to List ")],-1),Tt={key:0,class:"bg-white shadow rounded-lg p-6"},$t=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Documents",-1),Gt={class:"space-y-3"},Ot={class:"flex items-center space-x-3"},Zt=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1),Jt={class:"text-sm text-gray-700"},Kt=["href"],Rt={class:"bg-white shadow rounded-lg p-6"},Wt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),Xt={class:"space-y-4"},Yt={class:"flex items-start space-x-3"},te=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),ee=t("p",{class:"text-sm font-semibold text-gray-900"},"Quotation Created",-1),se={class:"text-xs text-gray-500"},oe={key:0,class:"flex items-start space-x-3"},ae=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),ie=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),ne={class:"text-xs text-gray-500"},ue={__name:"Show",props:{quotation:{type:Object,required:!0}},setup(e){const b=e,w=n=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",accepted:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",expired:"bg-yellow-100 text-yellow-800"})[n]||"bg-gray-100 text-gray-800",f=()=>{window.open(route("quotations.pdf",b.quotation.id),"_blank")},u=n=>n?new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",l=n=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(n);return(n,q)=>(o(),a(_,null,[c(p(L),{title:`Quotation - ${e.quotation.quotation_number}`},null,8,["title"]),c(v,null,{default:d(()=>{var h,y,g;return[t("div",S,[t("div",j,[t("div",null,[t("h1",D,s(e.quotation.quotation_number),1),N]),t("div",V,[c(m,{href:n.route("quotations.edit",e.quotation.id)},{svg:d(()=>[c(k,{class:"w-full items-center"},{default:d(()=>[A,r(" Edit Quotation ")]),_:1})]),_:1},8,["href"])])]),t("div",P,[t("div",z,[t("div",M,[Q,t("div",I,[t("div",null,[U,t("p",F,s(e.quotation.client_name),1)]),t("div",null,[E,t("p",H,s(((h=e.quotation.county)==null?void 0:h.name)||"N/A"),1)]),t("div",null,[T,t("span",{class:C(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",w(e.quotation.status)])},s(e.quotation.status.charAt(0).toUpperCase()+e.quotation.status.slice(1)),3)]),t("div",null,[$,t("p",G,s(u(e.quotation.valid_until)),1)]),e.quotation.lead?(o(),a("div",O,[Z,t("p",J,s(e.quotation.lead.lead_number),1)])):i("",!0),t("div",null,[K,t("p",R,s((y=e.quotation.creator)==null?void 0:y.first_name)+" "+s((g=e.quotation.creator)==null?void 0:g.last_name),1)])])]),t("div",W,[X,t("div",Y,[t("div",null,[tt,t("p",et,s(e.quotation.dimensions),1)]),t("div",null,[st,t("p",ot,s(e.quotation.open_size),1)]),t("div",null,[at,t("p",it,s(e.quotation.box_style),1)]),t("div",null,[nt,t("p",lt,s(e.quotation.stock),1)]),t("div",null,[ct,t("p",dt,s(e.quotation.lamination),1)]),t("div",null,[rt,t("p",ut,s(e.quotation.printing),1)]),e.quotation.add_ons?(o(),a("div",xt,[mt,t("p",ht,s(e.quotation.add_ons),1)])):i("",!0)])]),t("div",yt,[gt,t("div",_t,[t("table",bt,[wt,t("tbody",ft,[t("tr",null,[e.quotation.qty_1?(o(),a("td",qt,s(parseInt(e.quotation.qty_1).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_1?(o(),a("td",vt,s(l(e.quotation.price_qty_1)),1)):i("",!0),e.quotation.qty_1&&e.quotation.price_qty_1?(o(),a("td",kt,s(l(e.quotation.qty_1*e.quotation.price_qty_1)),1)):i("",!0)]),t("tr",null,[e.quotation.qty_2?(o(),a("td",pt,s(parseInt(e.quotation.qty_2).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_2?(o(),a("td",Lt,s(l(e.quotation.price_qty_2)),1)):i("",!0),e.quotation.qty_2&&e.quotation.price_qty_2?(o(),a("td",Ct,s(l(e.quotation.qty_2*e.quotation.price_qty_2)),1)):i("",!0)]),t("tr",null,[e.quotation.qty_3?(o(),a("td",Bt,s(parseInt(e.quotation.qty_3).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_3?(o(),a("td",St,s(l(e.quotation.price_qty_3)),1)):i("",!0),e.quotation.qty_3&&e.quotation.price_qty_3?(o(),a("td",jt,s(l(e.quotation.qty_3*e.quotation.price_qty_3)),1)):i("",!0)]),t("tr",null,[e.quotation.qty_4?(o(),a("td",Dt,s(parseInt(e.quotation.qty_4).toLocaleString())+" pcs",1)):i("",!0),e.quotation.price_qty_4?(o(),a("td",Nt,s(l(e.quotation.price_qty_4)),1)):i("",!0),e.quotation.qty_4&&e.quotation.price_qty_4?(o(),a("td",Vt,s(l(e.quotation.qty_4*e.quotation.price_qty_4)),1)):i("",!0)])])])])]),e.quotation.notes?(o(),a("div",At,[Pt,t("p",zt,s(e.quotation.notes),1)])):i("",!0)]),t("div",Mt,[t("div",Qt,[It,t("div",Ut,[c(m,{href:n.route("quotations.edit",e.quotation.id),class:"w-full"},{svg:d(()=>[Ft]),_:1},8,["href"]),t("div",{class:"px-3 py-2"},[t("button",{onClick:f,class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[Et,r(" Download PDF ")])]),c(m,{href:n.route("quotations.index"),class:"w-full"},{svg:d(()=>[Ht]),_:1},8,["href"])])]),e.quotation.documents&&e.quotation.documents.length>0?(o(),a("div",Tt,[$t,t("div",Gt,[(o(!0),a(_,null,B(e.quotation.documents,x=>(o(),a("div",{key:x.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",Ot,[Zt,t("span",Jt,s(x.orignal_name),1)]),t("a",{href:"/uploads/leads/"+x.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,Kt)]))),128))])])):i("",!0),t("div",Rt,[Wt,t("div",Xt,[t("div",Yt,[te,t("div",null,[ee,t("p",se,s(u(e.quotation.created_at)),1)])]),e.quotation.updated_at!==e.quotation.created_at?(o(),a("div",oe,[ae,t("div",null,[ie,t("p",ne,s(u(e.quotation.updated_at)),1)])])):i("",!0)])])])])])]}),_:1})],64))}};export{ue as default};
