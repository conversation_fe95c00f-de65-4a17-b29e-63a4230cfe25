import{r as h,T as G,w as X,b as u,d as _,e as l,u as o,f as N,F as Y,Z as ee,g as e,t as r,i as x,q as te,k as ae}from"./app-3b719d4c.js";import{_ as se,a as le}from"./AdminLayout-4519ee57.js";import{_ as k,a as v}from"./TextInput-fee5092c.js";import{_ as c}from"./InputLabel-3b756a6a.js";import{P as de}from"./PrimaryButton-09156d82.js";import{_ as oe}from"./TextArea-85f1aff3.js";import{_ as A}from"./Checkbox-a125690f.js";import"./_plugin-vue_export-helper-c27b6911.js";const ne={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ie={class:"flex justify-between items-center mb-6"},re={class:"text-2xl font-semibold leading-7 text-gray-900"},ce={class:"text-sm text-gray-600 mt-1"},ue={class:"flex space-x-3"},_e={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"},me={class:"mb-8 p-4 bg-gray-50 rounded-lg"},ye=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1),ge={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},xe=e("p",{class:"text-sm font-semibold text-gray-900"},"Quotation",-1),ve={class:"text-sm text-gray-700"},qe=e("p",{class:"text-sm font-semibold text-gray-900"},"Lead",-1),pe={class:"text-sm text-gray-700"},fe=e("p",{class:"text-sm font-semibold text-gray-900"},"Total Amount",-1),be={class:"text-sm font-semibold text-green-700"},he=e("p",{class:"text-sm font-semibold text-gray-900"},"Created By",-1),ke={class:"text-sm text-gray-700"},we=["onSubmit"],Ve={class:"border-b border-gray-900/10 pb-12"},Ae={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Qe={class:"sm:col-span-12"},Ne=e("h3",{class:"text-lg font-semibold text-gray-900"},"Edit Order Quantities",-1),Ce=e("p",{class:"text-sm text-gray-600"},'Current order quantities are shown. Check/uncheck to add/remove quantities. Use "Select All" to see all available options:',-1),$e={key:0,class:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg"},Fe={class:"text-sm text-red-600"},Se={key:0,class:"sm:col-span-3"},Ue={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Oe={class:"grid grid-cols-1 gap-4"},Pe={class:"text-sm text-gray-500 mt-1"},De={key:1,class:"sm:col-span-3"},Be={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Ee={class:"grid grid-cols-1 gap-4"},Me={class:"text-sm text-gray-500 mt-1"},Te={key:2,class:"sm:col-span-3"},je={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},ze={class:"grid grid-cols-1 gap-4"},Ie={class:"text-sm text-gray-500 mt-1"},Le={key:3,class:"sm:col-span-3"},He={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ze={class:"grid grid-cols-1 gap-4"},Ge={class:"text-sm text-gray-500 mt-1"},Je={key:4,class:"sm:col-span-12"},Ke={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Re={class:"flex items-center justify-between"},We={class:"text-lg font-semibold text-green-800"},Xe=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing ",-1),Ye=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),et={class:"sm:col-span-12 mb-6"},tt=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information",-1),at={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},st=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1),lt={class:"text-sm text-gray-700"},dt=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1),ot={class:"text-sm text-gray-700"},nt=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1),it={class:"text-sm text-gray-700"},rt=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1),ct={class:"text-sm text-gray-700"},ut=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1),_t={class:"text-sm text-gray-700"},mt=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1),yt={class:"text-sm text-gray-700"},gt=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1),xt={class:"text-sm text-gray-700"},vt=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1),qt={class:"text-sm text-gray-700"},pt={key:0},ft=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1),bt={class:"text-sm text-gray-700"},ht={class:"sm:col-span-4"},kt=e("p",{class:"text-sm text-gray-500 mt-1"},"Add tracking number when order is shipped",-1),wt={class:"sm:col-span-4"},Vt=["value"],At={class:"sm:col-span-4"},Qt=["value"],Nt={class:"sm:col-span-12"},Ct={class:"flex mt-6 items-center justify-between"},$t={class:"ml-auto flex items-center justify-end gap-x-6"},Ft=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Tt={__name:"Edit",props:{data:{type:Object,required:!0}},setup(a){const i=a;h(!0);const q=h(!!i.data.selected_qty_1),p=h(!!i.data.selected_qty_2),f=h(!!i.data.selected_qty_3),b=h(!!i.data.selected_qty_4),w=h(0),t=G({selected_qty_1:i.data.lead.qty_1||"",selected_qty_2:i.data.lead.qty_2||"",selected_qty_3:i.data.lead.qty_3||"",selected_qty_4:i.data.lead.qty_4||"",tracking_number:i.data.tracking_number||"",expected_delivery:i.data.expected_delivery||"",actual_delivery:i.data.actual_delivery||"",notes:i.data.notes||""}),J=()=>q.value||p.value||f.value||b.value?q.value&&!t.selected_qty_1?(alert("Please enter a value for Quantity 1."),!1):p.value&&!t.selected_qty_2?(alert("Please enter a value for Quantity 2."),!1):f.value&&!t.selected_qty_3?(alert("Please enter a value for Quantity 3."),!1):b.value&&!t.selected_qty_4?(alert("Please enter a value for Quantity 4."),!1):!0:(alert("Please select at least one quantity for the order."),!1),K=()=>{if(!J())return;const n=W();G(n).put(route("orders.update",i.data.id),{preserveScroll:!0})},R=n=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(n),Q=()=>{var s,m,y,g;let n=0;q.value&&t.selected_qty_1&&((s=i.data.quotation)!=null&&s.price_qty_1)&&(n+=parseFloat(t.selected_qty_1)*parseFloat(i.data.quotation.price_qty_1)),p.value&&t.selected_qty_2&&((m=i.data.quotation)!=null&&m.price_qty_2)&&(n+=parseFloat(t.selected_qty_2)*parseFloat(i.data.quotation.price_qty_2)),f.value&&t.selected_qty_3&&((y=i.data.quotation)!=null&&y.price_qty_3)&&(n+=parseFloat(t.selected_qty_3)*parseFloat(i.data.quotation.price_qty_3)),b.value&&t.selected_qty_4&&((g=i.data.quotation)!=null&&g.price_qty_4)&&(n+=parseFloat(t.selected_qty_4)*parseFloat(i.data.quotation.price_qty_4)),w.value=n},V=(n,s)=>{Q()},W=()=>{var s,m,y,g;const n={status:t.status,tracking_number:t.tracking_number,expected_delivery:t.expected_delivery,actual_delivery:t.actual_delivery,notes:t.notes,selected_qty_1:null,selected_qty_2:null,selected_qty_3:null,selected_qty_4:null,price_qty_1:null,price_qty_2:null,price_qty_3:null,price_qty_4:null};return q.value&&t.selected_qty_1&&(n.selected_qty_1=t.selected_qty_1,n.price_qty_1=(s=i.data.quotation)==null?void 0:s.price_qty_1),p.value&&t.selected_qty_2&&(n.selected_qty_2=t.selected_qty_2,n.price_qty_2=(m=i.data.quotation)==null?void 0:m.price_qty_2),f.value&&t.selected_qty_3&&(n.selected_qty_3=t.selected_qty_3,n.price_qty_3=(y=i.data.quotation)==null?void 0:y.price_qty_3),b.value&&t.selected_qty_4&&(n.selected_qty_4=t.selected_qty_4,n.price_qty_4=(g=i.data.quotation)==null?void 0:g.price_qty_4),n.total_amount=w.value,n};return X([()=>t.selected_qty_1,()=>q.value,()=>t.selected_qty_2,()=>p.value,()=>t.selected_qty_3,()=>f.value,()=>t.selected_qty_4,()=>b.value],Q),Q(),(n,s)=>(u(),_(Y,null,[l(o(ee),{title:"Edit Order"}),l(se,null,{default:N(()=>{var m,y,g,C,$,F,S,U,O,P,D,B,E,M,T,j,z,I,L,H,Z;return[e("div",ne,[e("div",ie,[e("div",null,[e("h2",re," Edit Order - "+r(a.data.order_number),1),e("p",ce,"Client: "+r(a.data.lead.client_name),1)]),e("div",ue,[a.data.is_confirmed?(u(),_("span",_e," ✓ Confirmed ")):x("",!0)])]),e("div",me,[ye,e("div",ge,[e("div",null,[xe,e("p",ve,r(((m=a.data.quotation)==null?void 0:m.quotation_number)||"N/A"),1)]),e("div",null,[qe,e("p",pe,r(a.data.lead.lead_number||"N/A"),1)]),e("div",null,[fe,e("p",be,r(R(a.data.total_amount)),1)]),e("div",null,[he,e("p",ke,r((y=a.data.creator)==null?void 0:y.first_name)+" "+r((g=a.data.creator)==null?void 0:g.last_name),1)])])]),e("form",{onSubmit:te(K,["prevent"])},[e("div",Ve,[e("div",Ae,[e("div",Qe,[Ne,Ce,n.$page.props.errors.quantities?(u(),_("div",$e,[e("p",Fe,r(n.$page.props.errors.quantities),1)])):x("",!0)]),(C=a.data.lead)!=null&&C.qty_1&&(($=a.data.quotation)!=null&&$.price_qty_1)?(u(),_("div",Se,[e("div",Ue,[l(A,{checked:q.value,"onUpdate:checked":[s[0]||(s[0]=d=>q.value=d),s[1]||(s[1]=d=>V(1,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",Oe,[e("div",null,[l(c,{for:"selected_qty_1",value:`Order Qty 1 (Available: ${a.data.lead.qty_1})`},null,8,["value"]),l(k,{id:"selected_qty_1",type:"number",modelValue:o(t).selected_qty_1,"onUpdate:modelValue":s[2]||(s[2]=d=>o(t).selected_qty_1=d),max:a.data.lead.qty_1,min:"1",placeholder:`Max: ${a.data.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_1},null,8,["message"]),e("p",Pe,"Price: £"+r(a.data.quotation.price_qty_1)+" per unit",1)])])])):x("",!0),(F=a.data.lead)!=null&&F.qty_2&&((S=a.data.quotation)!=null&&S.price_qty_2)?(u(),_("div",De,[e("div",Be,[l(A,{checked:p.value,"onUpdate:checked":[s[3]||(s[3]=d=>p.value=d),s[4]||(s[4]=d=>V(2,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",Ee,[e("div",null,[l(c,{for:"selected_qty_2",value:`Order Qty 2 (Available: ${a.data.lead.qty_2})`},null,8,["value"]),l(k,{id:"selected_qty_2",type:"number",modelValue:o(t).selected_qty_2,"onUpdate:modelValue":s[5]||(s[5]=d=>o(t).selected_qty_2=d),max:a.data.lead.qty_2,min:"1",placeholder:`Max: ${a.data.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_2},null,8,["message"]),e("p",Me,"Price: £"+r(a.data.quotation.price_qty_2)+" per unit",1)])])])):x("",!0),(U=a.data.lead)!=null&&U.qty_3&&((O=a.data.quotation)!=null&&O.price_qty_3)?(u(),_("div",Te,[e("div",je,[l(A,{checked:f.value,"onUpdate:checked":[s[6]||(s[6]=d=>f.value=d),s[7]||(s[7]=d=>V(3,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",ze,[e("div",null,[l(c,{for:"selected_qty_3",value:`Order Qty 3 (Available: ${a.data.lead.qty_3})`},null,8,["value"]),l(k,{id:"selected_qty_3",type:"number",modelValue:o(t).selected_qty_3,"onUpdate:modelValue":s[8]||(s[8]=d=>o(t).selected_qty_3=d),max:a.data.lead.qty_3,min:"1",placeholder:`Max: ${a.data.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_3},null,8,["message"]),e("p",Ie,"Price: £"+r(a.data.quotation.price_qty_3)+" per unit",1)])])])):x("",!0),(P=a.data.lead)!=null&&P.qty_4&&((D=a.data.quotation)!=null&&D.price_qty_4)?(u(),_("div",Le,[e("div",He,[l(A,{checked:b.value,"onUpdate:checked":[s[9]||(s[9]=d=>b.value=d),s[10]||(s[10]=d=>V(4,d))]},null,8,["checked"]),l(c,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",Ze,[e("div",null,[l(c,{for:"selected_qty_4",value:`Order Qty 4 (Available: ${a.data.lead.qty_4})`},null,8,["value"]),l(k,{id:"selected_qty_4",type:"number",modelValue:o(t).selected_qty_4,"onUpdate:modelValue":s[11]||(s[11]=d=>o(t).selected_qty_4=d),max:a.data.lead.qty_4,min:"1",placeholder:`Max: ${a.data.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(v,{message:o(t).errors.selected_qty_4},null,8,["message"]),e("p",Ge,"Price: £"+r(a.data.quotation.price_qty_4)+" per unit",1)])])])):x("",!0),w.value>0?(u(),_("div",Je,[e("div",Ke,[e("div",Re,[e("div",null,[e("p",We," Updated Order Total: £"+r(w.value.toFixed(2)),1),Xe]),Ye])])])):x("",!0),e("div",et,[tt,e("div",at,[e("div",null,[st,e("p",lt,r(((B=a.data.lead)==null?void 0:B.client_name)||"N/A"),1)]),e("div",null,[dt,e("p",ot,r(((M=(E=a.data.lead)==null?void 0:E.county)==null?void 0:M.name)||"N/A"),1)]),e("div",null,[nt,e("p",it,r(((T=a.data.lead)==null?void 0:T.dimensions)||"N/A"),1)]),e("div",null,[rt,e("p",ct,r(((j=a.data.lead)==null?void 0:j.open_size)||"N/A"),1)]),e("div",null,[ut,e("p",_t,r(((z=a.data.lead)==null?void 0:z.box_style)||"N/A"),1)]),e("div",null,[mt,e("p",yt,r(((I=a.data.lead)==null?void 0:I.stock)||"N/A"),1)]),e("div",null,[gt,e("p",xt,r(((L=a.data.lead)==null?void 0:L.lamination)||"N/A"),1)]),e("div",null,[vt,e("p",qt,r(((H=a.data.lead)==null?void 0:H.printing)||"N/A"),1)]),(Z=a.data.lead)!=null&&Z.add_ons?(u(),_("div",pt,[ft,e("p",bt,r(a.data.lead.add_ons),1)])):x("",!0)])]),e("div",ht,[l(c,{for:"tracking_number",value:"Tracking Number"}),l(k,{id:"tracking_number",type:"text",modelValue:o(t).tracking_number,"onUpdate:modelValue":s[12]||(s[12]=d=>o(t).tracking_number=d),placeholder:"Enter tracking number"},null,8,["modelValue"]),l(v,{message:o(t).errors.tracking_number},null,8,["message"]),kt]),e("div",wt,[l(c,{for:"expected_delivery",value:"Expected Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(t).expected_delivery?o(t).expected_delivery.slice(0,10):"",onInput:s[13]||(s[13]=d=>o(t).expected_delivery=d.target.value)},null,40,Vt),l(v,{message:o(t).errors.expected_delivery},null,8,["message"])]),e("div",At,[l(c,{for:"actual_delivery",value:"Actual Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(t).actual_delivery?o(t).actual_delivery.slice(0,10):"",onInput:s[14]||(s[14]=d=>o(t).actual_delivery=d.target.value)},null,40,Qt),l(v,{message:o(t).errors.actual_delivery},null,8,["message"])]),e("div",Nt,[l(c,{for:"notes",value:"Order Notes"}),l(oe,{id:"notes",modelValue:o(t).notes,"onUpdate:modelValue":s[15]||(s[15]=d=>o(t).notes=d),rows:"4",placeholder:"Add any notes about this order..."},null,8,["modelValue"]),l(v,{message:o(t).errors.notes},null,8,["message"])])])]),e("div",Ct,[e("div",$t,[l(le,{href:n.route("orders.index")},{svg:N(()=>[Ft]),_:1},8,["href"]),l(de,{disabled:o(t).processing},{default:N(()=>[ae(" Save ")]),_:1},8,["disabled"])])])],40,we)])]}),_:1})],64))}};export{Tt as default};
