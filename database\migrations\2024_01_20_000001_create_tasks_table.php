<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();

            // Task categorization
            $table->enum('type', [
                'call', 'follow_up', 'meeting', 'email', 'quote_follow_up',
                'order_follow_up', 'general', 'reminder'
            ])->default('general');

            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');

            // Scheduling
            $table->datetime('due_date');
            $table->datetime('reminder_date')->nullable();
            $table->datetime('completed_at')->nullable();

            // Relationships
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');

            // Polymorphic relationships for leads, quotations, orders
            $table->morphs('taskable'); // taskable_type, taskable_id

            // Additional fields
            $table->json('metadata')->nullable(); // For storing call duration, outcome, etc.
            $table->text('notes')->nullable();
            $table->boolean('is_recurring')->default(false);
            $table->string('recurring_pattern')->nullable(); // daily, weekly, monthly

            $table->timestamps();

            // Indexes for performance
            $table->index(['assigned_to', 'status', 'due_date']);
            // $table->index(['taskable_type', 'taskable_id']);
            $table->index(['due_date', 'status']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('tasks');
    }
};
