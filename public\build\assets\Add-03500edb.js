import{b as a,d as y,e as n,u as t,f as p,F as q,Z as V,g as l,q as x,m,i as d,k,D as b}from"./app-21b1097d.js";import{_ as $,a as C}from"./AdminLayout-931121ed.js";import{_ as u,a as r}from"./TextInput-43edb095.js";import{_ as i}from"./InputLabel-121d48a5.js";import{P as U}from"./PrimaryButton-c64acda1.js";import{_ as S}from"./TextArea-b94c961a.js";import{_ as z}from"./SearchableDropdown-e8469827.js";import{_ as N}from"./MultipleFileUpload-f02fbc0c.js";import{u as w}from"./index-c33d0138.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top"},T={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},A=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Lead",-1),D=["onSubmit"],F={class:"border-b border-gray-900/10 pb-12"},Q={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Y={class:"sm:col-span-2"},L={class:"sm:col-span-2"},P={class:"relative mt-2"},j={class:"sm:col-span-2"},O={class:"sm:col-span-2"},E={class:"sm:col-span-2"},I={class:"sm:col-span-2"},M={class:"sm:col-span-2"},Z={class:"sm:col-span-2"},G={class:"sm:col-span-2"},H={class:"sm:col-span-1"},J={class:"sm:col-span-1"},K={class:"sm:col-span-1"},R={class:"sm:col-span-1"},W={class:"sm:col-span-2"},X={class:"sm:col-span-6"},h={class:"flex mt-6 items-center justify-between"},ee={class:"ml-auto flex items-center justify-end gap-x-6"},te=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),se={key:0,class:"text-sm text-gray-600"},pe={__name:"Add",props:{counties:{type:Array,required:!0}},setup(v){const e=w("post","/leads",{client_name:"",county_id:"",dimensions:"",open_size:"",box_style:"",stock:"",lamination:"",printing:"",add_ons:"",qty_1:"",qty_2:"",qty_3:"",qty_4:"",notes:"",document:""}),g=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),f=(_,s)=>{e.county_id=_},c=_=>{e.document=_};return(_,s)=>(a(),y(q,null,[n(t(V),{title:"Add Lead"}),n($,null,{default:p(()=>[l("div",B,[l("div",T,[A,l("form",{onSubmit:x(g,["prevent"]),class:""},[l("div",F,[l("div",Q,[l("div",Y,[n(i,{for:"client_name",value:"Client Name *"}),n(u,{id:"client_name",type:"text",modelValue:t(e).client_name,"onUpdate:modelValue":s[0]||(s[0]=o=>t(e).client_name=o),required:"",onChange:s[1]||(s[1]=o=>t(e).validate("client_name"))},null,8,["modelValue"]),t(e).invalid("client_name")?(a(),m(r,{key:0,message:t(e).errors.client_name},null,8,["message"])):d("",!0)]),l("div",L,[n(i,{for:"county_id",value:"Country *"}),l("div",P,[n(z,{options:v.counties,onOnchange:f,required:"",placeholder:"Select Country"},null,8,["options"])]),t(e).invalid("county_id")?(a(),m(r,{key:0,message:t(e).errors.county_id},null,8,["message"])):d("",!0)]),l("div",j,[n(i,{for:"dimensions",value:"Dimensions *"}),n(u,{id:"dimensions",type:"text",modelValue:t(e).dimensions,"onUpdate:modelValue":s[2]||(s[2]=o=>t(e).dimensions=o),required:"",onChange:s[3]||(s[3]=o=>t(e).validate("dimensions"))},null,8,["modelValue"]),t(e).invalid("dimensions")?(a(),m(r,{key:0,message:t(e).errors.dimensions},null,8,["message"])):d("",!0)]),l("div",O,[n(i,{for:"open_size",value:"Open Size  *"}),n(u,{id:"open_size",type:"text",modelValue:t(e).open_size,"onUpdate:modelValue":s[4]||(s[4]=o=>t(e).open_size=o),required:"",onChange:s[5]||(s[5]=o=>t(e).validate("open_size"))},null,8,["modelValue"]),t(e).invalid("open_size")?(a(),m(r,{key:0,message:t(e).errors.open_size},null,8,["message"])):d("",!0)]),l("div",E,[n(i,{for:"box_style",value:"Box Style *"}),n(u,{id:"box_style",type:"text",modelValue:t(e).box_style,"onUpdate:modelValue":s[6]||(s[6]=o=>t(e).box_style=o),required:"",onChange:s[7]||(s[7]=o=>t(e).validate("box_style"))},null,8,["modelValue"]),t(e).invalid("box_style")?(a(),m(r,{key:0,message:t(e).errors.box_style},null,8,["message"])):d("",!0)]),l("div",I,[n(i,{for:"stock",value:"Stock *"}),n(u,{id:"stock",type:"text",modelValue:t(e).stock,"onUpdate:modelValue":s[8]||(s[8]=o=>t(e).stock=o),required:"",onChange:s[9]||(s[9]=o=>t(e).validate("stock"))},null,8,["modelValue"]),t(e).invalid("stock")?(a(),m(r,{key:0,message:t(e).errors.stock},null,8,["message"])):d("",!0)]),l("div",M,[n(i,{for:"lamination",value:"Lamination *"}),n(u,{id:"lamination",type:"text",modelValue:t(e).lamination,"onUpdate:modelValue":s[10]||(s[10]=o=>t(e).lamination=o),required:"",onChange:s[11]||(s[11]=o=>t(e).validate("lamination"))},null,8,["modelValue"]),t(e).invalid("lamination")?(a(),m(r,{key:0,message:t(e).errors.lamination},null,8,["message"])):d("",!0)]),l("div",Z,[n(i,{for:"printing",value:"Printing *"}),n(u,{id:"printing",type:"text",modelValue:t(e).printing,"onUpdate:modelValue":s[12]||(s[12]=o=>t(e).printing=o),required:"",onChange:s[13]||(s[13]=o=>t(e).validate("printing"))},null,8,["modelValue"]),t(e).invalid("printing")?(a(),m(r,{key:0,message:t(e).errors.printing},null,8,["message"])):d("",!0)]),l("div",G,[n(i,{for:"add_ons",value:"Add ons"}),n(u,{id:"add_ons",type:"text",modelValue:t(e).add_ons,"onUpdate:modelValue":s[14]||(s[14]=o=>t(e).add_ons=o),onChange:s[15]||(s[15]=o=>t(e).validate("add_ons"))},null,8,["modelValue"]),t(e).invalid("add_ons")?(a(),m(r,{key:0,message:t(e).errors.add_ons},null,8,["message"])):d("",!0)]),l("div",H,[n(i,{for:"qty_1",value:"QTY 1 *"}),n(u,{id:"qty_1",type:"text",modelValue:t(e).qty_1,"onUpdate:modelValue":s[16]||(s[16]=o=>t(e).qty_1=o),numeric:!0,required:"",onChange:s[17]||(s[17]=o=>t(e).validate("qty_1"))},null,8,["modelValue"]),t(e).invalid("qty_1")?(a(),m(r,{key:0,message:t(e).errors.qty_1},null,8,["message"])):d("",!0)]),l("div",J,[n(i,{for:"qty_2",value:"QTY 2"}),n(u,{id:"qty_2",type:"text",numeric:!0,modelValue:t(e).qty_2,"onUpdate:modelValue":s[18]||(s[18]=o=>t(e).qty_2=o),onChange:s[19]||(s[19]=o=>t(e).validate("qty_2"))},null,8,["modelValue"]),t(e).invalid("qty_2")?(a(),m(r,{key:0,message:t(e).errors.qty_2},null,8,["message"])):d("",!0)]),l("div",K,[n(i,{for:"qty_3",value:"QTY 3"}),n(u,{id:"qty_3",type:"text",numeric:!0,modelValue:t(e).qty_3,"onUpdate:modelValue":s[20]||(s[20]=o=>t(e).qty_3=o),onChange:s[21]||(s[21]=o=>t(e).validate("qty_3"))},null,8,["modelValue"]),t(e).invalid("qty_3")?(a(),m(r,{key:0,message:t(e).errors.qty_3},null,8,["message"])):d("",!0)]),l("div",R,[n(i,{for:"qty_4",value:"QTY 4"}),n(u,{id:"qty_4",type:"text",numeric:!0,modelValue:t(e).qty_4,"onUpdate:modelValue":s[22]||(s[22]=o=>t(e).qty_4=o),onChange:s[23]||(s[23]=o=>t(e).validate("qty_4"))},null,8,["modelValue"]),t(e).invalid("qty_4")?(a(),m(r,{key:0,message:t(e).errors.qty_4},null,8,["message"])):d("",!0)]),l("div",W,[n(i,{for:"note",value:"Upload Documents"}),n(N,{inputId:"document",inputName:"document",onFiles:c})]),l("div",X,[n(i,{for:"notes",value:"Notes"}),n(S,{id:"notes",type:"text",rows:3,modelValue:t(e).notes,"onUpdate:modelValue":s[24]||(s[24]=o=>t(e).notes=o),autocomplete:"notes",onChange:s[25]||(s[25]=o=>t(e).validate("notes"))},null,8,["modelValue"]),n(r,{class:"",message:t(e).errors.notes},null,8,["message"])])])]),l("div",h,[l("div",ee,[n(C,{href:_.route("leads.index")},{svg:p(()=>[te]),_:1},8,["href"]),n(U,{disabled:t(e).processing},{default:p(()=>[k("Save")]),_:1},8,["disabled"]),n(b,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[t(e).recentlySuccessful?(a(),y("p",se,"Saved.")):d("",!0)]),_:1})])])],40,D)])])]),_:1})],64))}};export{pe as default};
