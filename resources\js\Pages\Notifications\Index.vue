<template>
    <Head title="Notifications" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Notifications</h2>
                    <p class="text-sm text-gray-600 mt-1">Stay updated with your tasks and activities</p>
                </div>
                <div class="flex space-x-2">
                    <button @click="markAllAsRead" 
                            class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                        ✅ Mark All Read
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-blue-600">Total</p>
                            <p class="text-2xl font-semibold text-blue-900">{{ stats.total }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-yellow-600">Unread</p>
                            <p class="text-2xl font-semibold text-yellow-900">{{ stats.unread }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-red-600">High Priority</p>
                            <p class="text-2xl font-semibold text-red-900">{{ stats.high_priority }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <select v-model="filters.type" @change="applyFilters" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Types</option>
                            <option value="task_reminder">Task Reminder</option>
                            <option value="task_overdue">Task Overdue</option>
                            <option value="lead_update">Lead Update</option>
                            <option value="quotation_update">Quotation Update</option>
                            <option value="order_update">Order Update</option>
                            <option value="system">System</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select v-model="filters.unread_only" @change="applyFilters"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Notifications</option>
                            <option value="1">Unread Only</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button @click="clearFilters"
                                class="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700">
                            Clear Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="space-y-3">
                <div v-for="notification in notifications.data" :key="notification.id" 
                     class="border rounded-lg p-4 hover:shadow-md transition-shadow"
                     :class="{ 'bg-blue-50 border-blue-200': !notification.is_read, 'bg-white': notification.is_read }">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="getPriorityColor(notification.priority)">
                                    {{ notification.priority }}
                                </span>
                                <span class="inline-flex px-2 py-1 text-xs rounded-full"
                                      :class="getTypeColor(notification.type)">
                                    {{ formatType(notification.type) }}
                                </span>
                                <span v-if="!notification.is_read" 
                                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    NEW
                                </span>
                            </div>
                            <h3 class="text-sm font-semibold text-gray-900 mb-1">{{ notification.title }}</h3>
                            <p class="text-sm text-gray-600 mb-2">{{ notification.message }}</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>{{ formatDateTime(notification.created_at) }}</span>
                                <span v-if="notification.notifiable">
                                    Related to: {{ getRelatedEntityName(notification.notifiable) }}
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-2 ml-4">
                            <button v-if="!notification.is_read" @click="markAsRead(notification.id)"
                                    class="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200">
                                Mark Read
                            </button>
                            <button v-if="notification.action_data && notification.action_data.url" 
                                    @click="handleAction(notification)"
                                    class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                                {{ notification.action_data.action || 'View' }}
                            </button>
                            <button @click="deleteNotification(notification.id)"
                                    class="px-3 py-1 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200">
                                Delete
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-if="notifications.data.length === 0" class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
                    <p class="mt-1 text-sm text-gray-500">You're all caught up!</p>
                </div>
            </div>

            <!-- Pagination -->
            <div class="mt-6" v-if="notifications.links">
                <nav class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <Link v-if="notifications.prev_page_url" :href="notifications.prev_page_url" 
                              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </Link>
                        <Link v-if="notifications.next_page_url" :href="notifications.next_page_url"
                              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </Link>
                    </div>
                </nav>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'

const props = defineProps({
    notifications: Object,
    stats: Object
})

const filters = ref({
    type: '',
    unread_only: ''
})

const applyFilters = () => {
    router.get(route('notifications.index'), filters.value, {
        preserveState: true,
        preserveScroll: true
    })
}

const clearFilters = () => {
    filters.value = { type: '', unread_only: '' }
    applyFilters()
}

const markAsRead = (notificationId) => {
    router.post(route('notifications.read', notificationId), {}, {
        onSuccess: () => {
            router.reload({ only: ['notifications', 'stats'] })
        }
    })
}

const markAllAsRead = () => {
    if (confirm('Mark all notifications as read?')) {
        router.post(route('notifications.read-all'), {}, {
            onSuccess: () => {
                router.reload({ only: ['notifications', 'stats'] })
            }
        })
    }
}

const deleteNotification = (notificationId) => {
    if (confirm('Delete this notification?')) {
        router.delete(route('notifications.destroy', notificationId), {
            onSuccess: () => {
                router.reload({ only: ['notifications', 'stats'] })
            }
        })
    }
}

const handleAction = (notification) => {
    if (notification.action_data && notification.action_data.url) {
        // Mark as read first
        if (!notification.is_read) {
            markAsRead(notification.id)
        }
        // Navigate to the URL
        window.location.href = notification.action_data.url
    }
}

const formatDateTime = (date) => {
    return new Date(date).toLocaleString()
}

const formatType = (type) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getRelatedEntityName = (entity) => {
    if (entity.client_name) return entity.client_name
    if (entity.quotation_number) return `Quotation ${entity.quotation_number}`
    if (entity.order_number) return `Order ${entity.order_number}`
    if (entity.title) return entity.title
    return 'Unknown'
}

const getPriorityColor = (priority) => {
    const colors = {
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
}

const getTypeColor = (type) => {
    const colors = {
        task_reminder: 'bg-blue-100 text-blue-800',
        task_overdue: 'bg-red-100 text-red-800',
        lead_update: 'bg-green-100 text-green-800',
        quotation_update: 'bg-purple-100 text-purple-800',
        order_update: 'bg-orange-100 text-orange-800',
        system: 'bg-gray-100 text-gray-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
}
</script>
