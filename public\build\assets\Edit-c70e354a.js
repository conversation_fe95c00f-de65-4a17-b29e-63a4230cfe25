import{r as p,T as C,b as m,d as u,e,u as s,f as v,F as f,Z as z,g as l,t as V,q as E,i as y,j as T,k as P}from"./app-7a2cf3c5.js";import{_ as S,a as Y}from"./AdminLayout-61d8de5b.js";import{_ as r,a as d}from"./TextInput-e7255592.js";import{_ as n}from"./InputLabel-be4ef156.js";import{P as $}from"./PrimaryButton-8b39253a.js";import{_ as j}from"./TextArea-2182b4b1.js";import{_ as A}from"./SearchableDropdownNew-740d57c8.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},N={class:"text-2xl font-semibold leading-7 text-gray-900"},O=["onSubmit"],R={class:"border-b border-gray-900/10 pb-12"},D={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},I={class:"sm:col-span-4"},H={class:"sm:col-span-4"},L={class:"relative mt-2"},F={class:"sm:col-span-4"},M={class:"sm:col-span-4"},Z={class:"sm:col-span-4"},G={class:"sm:col-span-4"},J={class:"sm:col-span-4"},K={class:"sm:col-span-4"},W={class:"sm:col-span-4"},X=l("div",{class:"sm:col-span-12 mt-6"},[l("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quantities and Pricing")],-1),ee={class:"sm:col-span-3"},te={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},se={class:"sm:col-span-3"},le={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},oe={class:"sm:col-span-3"},ae={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},ie={class:"sm:col-span-3"},ne={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},de={class:"sm:col-span-12"},re={key:0,class:"sm:col-span-12"},me={class:"mt-2 space-y-2"},ue={class:"flex items-center space-x-3"},_e=l("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[l("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1),ce={class:"text-sm text-gray-700"},pe={class:"flex items-center space-x-2"},ye=["href"],ge=["onClick"],ve={class:"flex mt-6 items-center justify-between"},qe={class:"ml-auto flex items-center justify-end gap-x-6"},fe=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),ze={__name:"Edit",props:{data:{type:Object,required:!0},counties:{type:Array,required:!0},filepath:{type:Object,default:null},permissions:{type:Object,default:()=>({})}},setup(_){var q;const i=_,g=p(((q=i.permissions)==null?void 0:q.canEditPrice)||!1),t=C({id:i.data.id,client_name:i.data.client_name||"",county_id:i.data.county_id||"",lead_id:i.data.lead_id||null,dimensions:i.data.dimensions||"",open_size:i.data.open_size||"",box_style:i.data.box_style||"",stock:i.data.stock||"",lamination:i.data.lamination||"",printing:i.data.printing||"",add_ons:i.data.add_ons||"",qty_1:i.data.qty_1||"",qty_2:i.data.qty_2||"",qty_3:i.data.qty_3||"",qty_4:i.data.qty_4||"",price_qty_1:i.data.price_qty_1||"",price_qty_2:i.data.price_qty_2||"",price_qty_3:i.data.price_qty_3||"",price_qty_4:i.data.price_qty_4||"",notes:i.data.notes||"",valid_until:i.data.valid_until||""}),b=p(!!i.data.qty_1),x=p(!!i.data.qty_2),h=p(!!i.data.qty_3),k=p(!!i.data.qty_4),U=()=>{t.post(route("quotations.store"),{preserveScroll:!0})},Q=(c,a)=>{t.county_id=c},w=c=>{confirm("Are you sure you want to remove this document?")&&(window.location.href=route("removequotationdocument",c))};return(c,a)=>(m(),u(f,null,[e(s(z),{title:"Edit Quotation"}),e(S,null,{default:v(()=>[l("div",B,[l("h2",N," Edit Quotation - "+V(_.data.quotation_number),1),l("form",{onSubmit:E(U,["prevent"])},[l("div",R,[l("div",D,[l("div",I,[e(n,{for:"client_name",value:"Client Name *"}),e(r,{id:"client_name",type:"text",modelValue:s(t).client_name,"onUpdate:modelValue":a[0]||(a[0]=o=>s(t).client_name=o),required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.client_name},null,8,["message"])]),l("div",H,[e(n,{for:"county_id",value:"Country *"}),l("div",L,[e(A,{options:_.counties,modelValue:s(t).county_id,"onUpdate:modelValue":a[1]||(a[1]=o=>s(t).county_id=o),onOnchange:Q},null,8,["options","modelValue"])]),e(d,{message:s(t).errors.county_id},null,8,["message"])]),l("div",F,[e(n,{for:"dimensions",value:"Dimensions *"}),e(r,{id:"dimensions",type:"text",modelValue:s(t).dimensions,"onUpdate:modelValue":a[2]||(a[2]=o=>s(t).dimensions=o),required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.dimensions},null,8,["message"])]),l("div",M,[e(n,{for:"open_size",value:"Open Size *"}),e(r,{id:"open_size",type:"text",modelValue:s(t).open_size,"onUpdate:modelValue":a[3]||(a[3]=o=>s(t).open_size=o),required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.open_size},null,8,["message"])]),l("div",Z,[e(n,{for:"box_style",value:"Box Style *"}),e(r,{id:"box_style",type:"text",modelValue:s(t).box_style,"onUpdate:modelValue":a[4]||(a[4]=o=>s(t).box_style=o),required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.box_style},null,8,["message"])]),l("div",G,[e(n,{for:"stock",value:"Stock *"}),e(r,{id:"stock",type:"text",modelValue:s(t).stock,"onUpdate:modelValue":a[5]||(a[5]=o=>s(t).stock=o),required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.stock},null,8,["message"])]),l("div",J,[e(n,{for:"lamination",value:"Lamination *"}),e(r,{id:"lamination",type:"text",modelValue:s(t).lamination,"onUpdate:modelValue":a[6]||(a[6]=o=>s(t).lamination=o),required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.lamination},null,8,["message"])]),l("div",K,[e(n,{for:"printing",value:"Printing *"}),e(r,{id:"printing",type:"text",modelValue:s(t).printing,"onUpdate:modelValue":a[7]||(a[7]=o=>s(t).printing=o),required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.printing},null,8,["message"])]),l("div",W,[e(n,{for:"add_ons",value:"Add-ons"}),e(r,{id:"add_ons",type:"text",modelValue:s(t).add_ons,"onUpdate:modelValue":a[8]||(a[8]=o=>s(t).add_ons=o)},null,8,["modelValue"]),e(d,{message:s(t).errors.add_ons},null,8,["message"])]),X,l("div",ee,[b.value?(m(),u("div",te,[l("div",null,[e(n,{for:"qty_1",value:"QTY 1 *"}),e(r,{id:"qty_1",type:"number",modelValue:s(t).qty_1,"onUpdate:modelValue":a[9]||(a[9]=o=>s(t).qty_1=o),min:"1",required:""},null,8,["modelValue"]),e(d,{message:s(t).errors.qty_1},null,8,["message"])]),l("div",null,[e(n,{for:"price_qty_1",value:"PRICE QTY 1 *"}),e(r,{id:"price_qty_1",type:"number",step:"0.01",modelValue:s(t).price_qty_1,"onUpdate:modelValue":a[10]||(a[10]=o=>s(t).price_qty_1=o),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),e(d,{message:s(t).errors.price_qty_1},null,8,["message"])])])):y("",!0)]),l("div",se,[x.value?(m(),u("div",le,[l("div",null,[e(n,{for:"qty_2",value:"QTY 2"}),e(r,{id:"qty_2",type:"number",modelValue:s(t).qty_2,"onUpdate:modelValue":a[11]||(a[11]=o=>s(t).qty_2=o),min:"1"},null,8,["modelValue"]),e(d,{message:s(t).errors.qty_2},null,8,["message"])]),l("div",null,[e(n,{for:"price_qty_2",value:"PRICE QTY 2"}),e(r,{id:"price_qty_2",type:"number",step:"0.01",modelValue:s(t).price_qty_2,"onUpdate:modelValue":a[12]||(a[12]=o=>s(t).price_qty_2=o),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),e(d,{message:s(t).errors.price_qty_2},null,8,["message"])])])):y("",!0)]),l("div",oe,[h.value?(m(),u("div",ae,[l("div",null,[e(n,{for:"qty_3",value:"QTY 3"}),e(r,{id:"qty_3",type:"number",modelValue:s(t).qty_3,"onUpdate:modelValue":a[13]||(a[13]=o=>s(t).qty_3=o),min:"1"},null,8,["modelValue"]),e(d,{message:s(t).errors.qty_3},null,8,["message"])]),l("div",null,[e(n,{for:"price_qty_3",value:"PRICE QTY 3"}),e(r,{id:"price_qty_3",type:"number",step:"0.01",modelValue:s(t).price_qty_3,"onUpdate:modelValue":a[14]||(a[14]=o=>s(t).price_qty_3=o),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),e(d,{message:s(t).errors.price_qty_3},null,8,["message"])])])):y("",!0)]),l("div",ie,[k.value?(m(),u("div",ne,[l("div",null,[e(n,{for:"qty_4",value:"QTY 4"}),e(r,{id:"qty_4",type:"number",modelValue:s(t).qty_4,"onUpdate:modelValue":a[15]||(a[15]=o=>s(t).qty_4=o),min:"1"},null,8,["modelValue"]),e(d,{message:s(t).errors.qty_4},null,8,["message"])]),l("div",null,[e(n,{for:"price_qty_4",value:"PRICE QTY 4"}),e(r,{id:"price_qty_4",type:"number",step:"0.01",modelValue:s(t).price_qty_4,"onUpdate:modelValue":a[16]||(a[16]=o=>s(t).price_qty_4=o),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),e(d,{message:s(t).errors.price_qty_4},null,8,["message"])])])):y("",!0)]),l("div",de,[e(n,{for:"notes",value:"Notes"}),e(j,{id:"notes",modelValue:s(t).notes,"onUpdate:modelValue":a[17]||(a[17]=o=>s(t).notes=o),rows:"4"},null,8,["modelValue"]),e(d,{message:s(t).errors.notes},null,8,["message"])]),_.data.documents&&_.data.documents.length>0?(m(),u("div",re,[e(n,{value:"Existing Documents"}),l("div",me,[(m(!0),u(f,null,T(_.data.documents,o=>(m(),u("div",{key:o.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[l("div",ue,[_e,l("span",ce,V(o.orignal_name),1)]),l("div",pe,[l("a",{href:_.filepath.view+o.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,ye),l("button",{type:"button",onClick:Ve=>w(o.id),class:"text-red-600 hover:text-red-800 text-sm"},"Remove",8,ge)])]))),128))])])):y("",!0)])]),l("div",ve,[l("div",qe,[e(Y,{href:c.route("quotations.index")},{svg:v(()=>[fe]),_:1},8,["href"]),e($,{disabled:s(t).processing},{default:v(()=>[P(" Save ")]),_:1},8,["disabled"])])])],40,O)])]),_:1})],64))}};export{ze as default};
