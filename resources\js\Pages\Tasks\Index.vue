<template>
    <Head title="Tasks" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Tasks</h2>
                    <p class="text-sm text-gray-600 mt-1">Manage your tasks and follow-ups</p>
                </div>
                <Link :href="route('tasks.create')"
                      class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Task
                </Link>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-blue-600">Total Tasks</p>
                            <p class="text-2xl font-semibold text-blue-900">{{ stats.total }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-yellow-600">Pending</p>
                            <p class="text-2xl font-semibold text-yellow-900">{{ stats.pending }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-red-600">Overdue</p>
                            <p class="text-2xl font-semibold text-red-900">{{ stats.overdue }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-green-600">Due Today</p>
                            <p class="text-2xl font-semibold text-green-900">{{ stats.due_today }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select v-model="filters.status" @change="applyFilters"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <select v-model="filters.type" @change="applyFilters"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Types</option>
                            <option value="call">Call</option>
                            <option value="follow_up">Follow Up</option>
                            <option value="meeting">Meeting</option>
                            <option value="email">Email</option>
                            <option value="quote_follow_up">Quote Follow Up</option>
                            <option value="order_follow_up">Order Follow Up</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                        <select v-model="filters.priority" @change="applyFilters"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Priorities</option>
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Assigned To</label>
                        <select v-model="filters.assigned_to" @change="applyFilters"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Users</option>
                            <option v-for="user in users" :key="user.id" :value="user.id">
                                {{ user.first_name }} {{ user.last_name }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Tasks Cards View (Better for Mobile) -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div v-for="task in tasks.data" :key="task.id"
                     class="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <!-- Task Header -->
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <h3 class="text-sm font-semibold text-gray-900 mb-1">{{ task.title }}</h3>
                            <p class="text-xs text-gray-500" v-if="task.taskable">
                                📋 {{ task.taskable.client_name || task.taskable.quotation_number || task.taskable.order_number }}
                            </p>
                        </div>
                        <div class="flex flex-col items-end space-y-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                  :class="getPriorityColor(task.priority)">
                                {{ task.priority }}
                            </span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                  :class="getStatusColor(task.status)">
                                {{ formatStatus(task.status) }}
                            </span>
                        </div>
                    </div>

                    <!-- Task Details -->
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-xs text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            {{ task.assigned_to.first_name }} {{ task.assigned_to.last_name }}
                        </div>
                        <div class="flex items-center text-xs"
                             :class="{ 'text-red-600 font-semibold': isOverdue(task.due_date), 'text-gray-600': !isOverdue(task.due_date) }">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {{ formatDate(task.due_date) }}
                            <span v-if="isOverdue(task.due_date)" class="ml-1 text-red-500">⚠️ Overdue</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <span class="inline-flex px-2 py-1 text-xs rounded-full"
                                  :class="getTypeColor(task.type)">
                                {{ formatType(task.type) }}
                            </span>
                        </div>
                    </div>

                    <!-- Task Actions -->
                    <div class="flex space-x-2">
                        <Link :href="route('tasks.show', task.id)"
                              class="flex-1 text-center px-3 py-2 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100">
                            View
                        </Link>
                        <Link :href="route('tasks.edit', task.id)"
                              class="flex-1 text-center px-3 py-2 text-xs font-medium text-green-600 bg-green-50 rounded-md hover:bg-green-100">
                            Edit
                        </Link>
                        <button v-if="task.status !== 'completed'"
                                @click="completeTask(task.id)"
                                class="flex-1 px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100">
                            ✓ Complete
                        </button>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-if="tasks.data.length === 0" class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No tasks found</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating a new task.</p>
                <div class="mt-6">
                    <Link :href="route('tasks.create')"
                          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Task
                    </Link>
                </div>
            </div>

            <!-- Pagination -->
            <div class="mt-6" v-if="tasks.links">
                <nav class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <Link v-if="tasks.prev_page_url" :href="tasks.prev_page_url"
                              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </Link>
                        <Link v-if="tasks.next_page_url" :href="tasks.next_page_url"
                              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </Link>
                    </div>
                </nav>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'

const props = defineProps({
    tasks: Object,
    users: Array,
    stats: Object
})

const filters = ref({
    status: '',
    type: '',
    priority: '',
    assigned_to: ''
})

const applyFilters = () => {
    router.get(route('tasks.index'), filters.value, {
        preserveState: true,
        preserveScroll: true
    })
}

const completeTask = (taskId) => {
    if (confirm('Mark this task as completed?')) {
        router.post(route('tasks.complete', taskId), {}, {
            onSuccess: () => {
                // Refresh the page
                router.reload()
            }
        })
    }
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const formatType = (type) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const isOverdue = (dueDate) => {
    return new Date(dueDate) < new Date()
}

const getTypeColor = (type) => {
    const colors = {
        call: 'bg-blue-100 text-blue-800',
        follow_up: 'bg-yellow-100 text-yellow-800',
        meeting: 'bg-purple-100 text-purple-800',
        email: 'bg-green-100 text-green-800',
        quote_follow_up: 'bg-orange-100 text-orange-800',
        order_follow_up: 'bg-red-100 text-red-800',
        general: 'bg-gray-100 text-gray-800',
        reminder: 'bg-pink-100 text-pink-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
}

const getPriorityColor = (priority) => {
    const colors = {
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
}

const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        in_progress: 'bg-blue-100 text-blue-800',
        completed: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}
</script>
