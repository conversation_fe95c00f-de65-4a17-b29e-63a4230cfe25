import{r as u,T as _,b as y,d as w,e as o,f as a,g as s,H as h,k as c,u as t,I as g,n as x}from"./app-b86547fe.js";import{D as m}from"./DangerButton-********.js";import{_ as k,a as v}from"./TextInput-b0990503.js";import{_ as D}from"./InputLabel-7201704d.js";import{M as C,_ as b}from"./Modal-e5e7f5ec.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const V={class:"space-y-6"},B=s("header",null,[s("h2",{class:"text-lg font-medium text-gray-900"},"Delete Account"),s("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain. ")],-1),U={class:"p-6"},A=s("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete your account? ",-1),$=s("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ",-1),I={class:"mt-6"},K={class:"mt-6 flex justify-end"},z={__name:"DeleteUserForm",setup(M){const l=u(!1),r=u(null),e=_({password:""}),p=()=>{l.value=!0,h(()=>r.value.focus())},d=()=>{e.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>n(),onError:()=>r.value.focus(),onFinish:()=>e.reset()})},n=()=>{l.value=!1,e.reset()};return(N,i)=>(y(),w("section",V,[B,o(m,{onClick:p},{default:a(()=>[c("Delete Account")]),_:1}),o(C,{show:l.value,onClose:n},{default:a(()=>[s("div",U,[A,$,s("div",I,[o(D,{for:"password",value:"Password",class:"sr-only"}),o(k,{id:"password",ref_key:"passwordInput",ref:r,modelValue:t(e).password,"onUpdate:modelValue":i[0]||(i[0]=f=>t(e).password=f),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",onKeyup:g(d,["enter"])},null,8,["modelValue","onKeyup"]),o(v,{message:t(e).errors.password,class:"mt-2"},null,8,["message"])]),s("div",K,[o(b,{onClick:n},{default:a(()=>[c(" Cancel ")]),_:1}),o(m,{class:x(["ml-3",{"opacity-25":t(e).processing}]),disabled:t(e).processing,onClick:d},{default:a(()=>[c(" Delete Account ")]),_:1},8,["class","disabled"])])])]),_:1},8,["show"])]))}};export{z as default};
