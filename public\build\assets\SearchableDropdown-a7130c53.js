import{r as c,w as T,o as z,a as D,b as d,d as p,h as E,v as I,g as r,I as f,F as L,j as $,n as h,t as F,i as C}from"./app-be4dd2ad.js";const K={class:"relative"},S=["onKeydown"],N=r("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[r("path",{"fill-rule":"evenodd",d:"M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z","clip-rule":"evenodd"})],-1),U=[N],j=["onClick","onMouseenter"],A=r("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[r("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z","clip-rule":"evenodd"})],-1),R=[A],G={__name:"SearchableDropdown",props:["options","modelValue","editMode"],emits:["onchange"],setup(M,{emit:B}){const a=M,u=c(a.options),n=c(""),v=c(!1),l=c(-1),i=c(null),w=()=>{const e=new RegExp(n.value,"i");u.value=a.options.filter(t=>e.test(t.name))},b=(e,t)=>{n.value=e,v.value=!1,B("onchange",t,e)},x=()=>{a.editMode||(v.value=!0)},H=e=>{l.value=e,_(e)},y=e=>{const t=u.value.length;e==="down"?l.value=(l.value+1)%t:e==="up"&&(l.value=(l.value-1+t)%t),_(l.value)},V=()=>{const e=u.value[l.value];e&&b(e.name,e.id)},_=e=>{if(!i.value)return;const t=i.value.children[e];if(t){const o=i.value.offsetHeight,s=t.offsetHeight,m=i.value.scrollTop,g=t.offsetTop,O=g+s;g<m?i.value.scrollTop=g:O>m+o&&(i.value.scrollTop=O-o)}};T(()=>a.options,()=>{w()}),T(()=>a.modelValue,e=>{e===""&&(n.value="")}),z(()=>{const e=a.options.find(t=>t.id===a.modelValue);e?n.value=e.name:n.value="",document.addEventListener("click",k)}),D(()=>{document.removeEventListener("click",k)});const k=e=>{e.target.closest(".relative")||(v.value=!1)};return(e,t)=>(d(),p("div",K,[E(r("input",{id:"combobox",type:"text",placeholder:"Search...",role:"combobox","onUpdate:modelValue":t[0]||(t[0]=o=>n.value=o),onInput:w,onFocus:x,autocomplete:"off",class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-7 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",onKeydown:[t[1]||(t[1]=f(o=>y("down"),["down"])),t[2]||(t[2]=f(o=>y("up"),["up"])),f(V,["enter"])]},null,40,S),[[I,n.value]]),r("button",{type:"button",class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none",onClick:x},U),v.value&&u.value.length?(d(),p("ul",{key:0,class:"absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm cursor-pointer",id:"options",role:"listbox","aria-labelledby":"combobox",ref_key:"dropdown",ref:i},[(d(!0),p(L,null,$(u.value,(o,s)=>(d(),p("li",{class:h(["relative cursor-default select-none py-2 pl-3 pr-9 cursor-pointer",{"text-white bg-indigo-600":l.value===s,"text-gray-900":l.value!==s}]),key:s,onClick:m=>b(o.name,o.id),onMouseenter:m=>H(s),tabindex:"-1",role:"option"},[r("span",{class:h(["block truncate",{"font-semibold":o.name===n.value}])},F(o.name),3),o.name===n.value?(d(),p("span",{key:0,class:h(["absolute inset-y-0 right-0 flex items-center pr-4",{"text-white":l.value===s,"text-indigo-600":l.value!==s}])},R,2)):C("",!0)],42,j))),128))],512)):C("",!0)]))}};export{G as _};
