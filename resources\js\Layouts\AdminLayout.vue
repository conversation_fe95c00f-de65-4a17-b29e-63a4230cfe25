<script setup>
import {ref, onMounted, onUnmounted} from 'vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import SideMenu from '@/Components/SideMenu.vue';
import ToastNotificationVue from '@/Components/ToastNotification.vue'
import ToastNotificationSuccessVue from '@/Components/ToastNotificationSuccess.vue'
import ToastNotificationErrorVue from '@/Components/ToastNotificationError.vue'
import ToastNotificationWarningVue from '@/Components/ToastNotificationWarning.vue'
import ActionLink from '@/Components/ActionLink.vue';
import NotificationDropdown from '@/Components/NotificationDropdown.vue';
import {Link, useForm} from '@inertiajs/vue3';

const logoSrc = ref('/uploads/companyprofile/defaultimg.png');
const form = useForm({});
const updateLogoSrc = (value) => {
    logoSrc.value = value;
};

// Mobile sidebar state
const sidebarOpen = ref(false);

const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value;
};

const closeSidebar = () => {
    sidebarOpen.value = false;
};

// Close sidebar when clicking outside on mobile
const handleClickOutside = (event) => {
    const sidebar = document.getElementById('mobile-sidebar');
    const hamburger = document.getElementById('hamburger-button');

    if (sidebar && !sidebar.contains(event.target) && !hamburger.contains(event.target)) {
        closeSidebar();
    }
};

// Handle escape key
const handleEscape = (event) => {
    if (event.key === 'Escape') {
        closeSidebar();
    }
};

onMounted(async () => {
    // Add event listeners for mobile sidebar
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleEscape);
});

onUnmounted(() => {
    // Remove event listeners
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleEscape);
});

const props = defineProps({
    notifications: {
        type: Array,
        default: () => []
    }
});

const hasPermission = (permission) => {
//    return this.$store.state.user.permissions.includes(permission);
};

</script>

<template>
    <div>
        <!-- Static sidebar for desktop -->
        <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col border-t border-r">
            <div class="bg-white flex h-16 px-6 items-center px-10 shrink-0 w-full my-2">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="400" viewBox="0 0 849.75 567.000005" height="100" preserveAspectRatio="xMidYMid meet"><defs><clipPath id="943ba9c57f">
                    <path d="M 56.886719 356.386719 L 91.304688 356.386719 L 91.304688 404.597656 L 56.886719 404.597656 Z M 56.886719 356.386719 " clip-rule="nonzero"></path></clipPath><clipPath id="194d7ee4e2"><path d="M 63.75 370.226562 L 63.75 385.816406 C 66.398438 384.585938 69.828125 383.972656 74.042969 383.972656 L 74.042969 383.929688 C 80.90625 383.929688 84.339844 381.644531 84.339844 377.066406 L 84.339844 370.226562 C 84.339844 365.652344 80.90625 363.363281 74.042969 363.363281 C 67.179688 363.363281 63.75 365.671875 63.75 370.226562 M 91.179688 370.226562 L 91.179688 377.089844 C 91.179688 386.238281 85.460938 390.792969 74.042969 390.792969 L 74.042969 390.835938 C 67.351562 390.835938 63.917969 392.996094 63.75 397.320312 L 63.75 404.519531 L 56.886719 404.519531 L 56.886719 370.226562 C 56.886719 361.097656 62.605469 356.519531 74.023438 356.519531 C 85.441406 356.519531 91.160156 361.097656 91.160156 370.226562 " clip-rule="nonzero"></path></clipPath><clipPath id="58b94cbc59"><path d="M 128.226562 356.386719 L 162.644531 356.386719 L 162.644531 390.804688 L 128.226562 390.804688 Z M 128.226562 356.386719 " clip-rule="nonzero"></path></clipPath><clipPath id="cf7410951e"><path d="M 155.660156 370.226562 C 155.660156 365.652344 152.226562 363.363281 145.363281 363.363281 C 138.5 363.363281 135.089844 365.671875 135.089844 370.226562 L 135.089844 377.089844 C 135.089844 381.664062 138.523438 383.953125 145.386719 383.953125 C 152.25 383.953125 155.679688 381.664062 155.679688 377.089844 Z M 128.226562 377.089844 L 128.226562 370.226562 C 128.226562 361.097656 133.945312 356.519531 145.363281 356.519531 C 149.578125 356.519531 153.011719 357.136719 155.660156 358.40625 L 155.660156 356.519531 L 162.523438 356.519531 L 162.523438 390.792969 L 155.660156 390.792969 L 155.660156 388.953125 C 153.011719 390.179688 149.578125 390.792969 145.363281 390.792969 C 133.945312 390.792969 128.226562 386.242188 128.226562 377.089844 " clip-rule="nonzero"></path></clipPath><clipPath id="77b2eb9357"><path d="M 43.984375 161.480469 L 207 161.480469 L 207 324.964844 L 43.984375 324.964844 Z M 43.984375 161.480469 " clip-rule="nonzero"></path></clipPath><clipPath id="d5ce663471"><path d="M 174.261719 226.617188 C 174.261719 204.90625 157.972656 194.039062 125.414062 194.039062 C 92.855469 194.039062 76.566406 204.90625 76.566406 226.617188 L 76.566406 259.199219 C 76.566406 280.910156 92.855469 291.777344 125.414062 291.777344 C 157.972656 291.777344 174.261719 280.910156 174.261719 259.199219 Z M 43.984375 259.199219 L 43.984375 226.617188 C 43.984375 183.195312 71.121094 161.480469 125.414062 161.480469 C 145.386719 161.480469 161.679688 164.46875 174.261719 170.441406 L 174.261719 161.480469 L 206.839844 161.480469 L 206.839844 324.335938 L 174.261719 324.335938 L 174.261719 315.542969 C 161.65625 321.410156 145.386719 324.335938 125.414062 324.335938 C 71.121094 324.335938 43.984375 302.621094 43.984375 259.199219 " clip-rule="nonzero"></path></clipPath><clipPath id="a3bcb9c258"><path d="M 199.554688 356.386719 L 233.972656 356.386719 L 233.972656 390.804688 L 199.554688 390.804688 Z M 199.554688 356.386719 " clip-rule="nonzero"></path></clipPath><clipPath id="ea7ed97436"><path d="M 233.851562 377.089844 C 233.851562 386.238281 228.132812 390.792969 216.714844 390.792969 C 205.296875 390.792969 199.554688 386.238281 199.554688 377.089844 L 199.554688 370.226562 C 199.554688 361.097656 205.273438 356.519531 216.691406 356.519531 C 228.109375 356.519531 233.828125 361.097656 233.828125 370.226562 L 226.988281 370.226562 C 226.988281 365.652344 223.554688 363.363281 216.691406 363.363281 C 209.828125 363.363281 206.417969 365.671875 206.417969 370.226562 L 206.417969 377.089844 C 206.417969 381.664062 209.851562 383.953125 216.714844 383.953125 C 223.578125 383.953125 227.007812 381.664062 227.007812 377.089844 Z M 233.851562 377.089844 " clip-rule="nonzero"></path></clipPath><clipPath id="4ec397588f"><path d="M 239.234375 161.3125 L 320.996094 161.3125 L 320.996094 324.441406 L 239.234375 324.441406 Z M 239.234375 161.3125 " clip-rule="nonzero"></path></clipPath><clipPath id="df76811b89"><path d="M 239.402344 226.617188 C 239.402344 183.195312 266.539062 161.480469 320.828125 161.480469 L 320.828125 194.0625 C 288.25 194.0625 271.980469 204.929688 271.980469 226.640625 L 271.980469 324.335938 L 239.402344 324.335938 Z M 239.402344 226.617188 " clip-rule="nonzero"></path></clipPath><clipPath id="ee106d2b85"><path d="M 270.898438 342.476562 L 305.367188 342.476562 L 305.367188 390.976562 L 270.898438 390.976562 Z M 270.898438 342.476562 " clip-rule="nonzero"></path></clipPath><clipPath id="2c304f7b29"><path d="M 305.175781 383.941406 L 305.175781 390.804688 L 298.332031 390.804688 L 298.332031 383.941406 C 298.355469 379.367188 294.945312 377.078125 288.082031 377.078125 L 288.082031 376.929688 L 288.039062 376.929688 L 288.039062 377.144531 C 281.34375 377.144531 277.914062 379.304688 277.742188 383.625 L 277.742188 383.941406 L 277.785156 383.941406 L 277.742188 390.804688 L 270.902344 390.804688 L 270.902344 342.804688 L 277.742188 342.804688 L 277.742188 356.53125 L 277.785156 363.394531 L 277.742188 363.394531 L 277.742188 372.164062 C 279.628906 371.296875 281.917969 370.726562 284.605469 370.492188 C 291.46875 370.320312 294.902344 367.972656 294.902344 363.394531 L 294.859375 356.53125 L 301.722656 356.53125 L 301.765625 363.394531 C 301.765625 367.144531 300.789062 370.132812 298.84375 372.335938 C 303.097656 374.496094 305.21875 378.371094 305.175781 383.964844 " clip-rule="nonzero"></path></clipPath><clipPath id="dd7ea98c17"><path d="M 342.261719 356.386719 L 376.683594 356.386719 L 376.683594 390.804688 L 342.261719 390.804688 Z M 342.261719 356.386719 " clip-rule="nonzero"></path></clipPath><clipPath id="e166b95444"><path d="M 369.695312 370.226562 C 369.695312 365.652344 366.261719 363.363281 359.398438 363.363281 C 352.535156 363.363281 349.125 365.671875 349.125 370.226562 L 349.125 377.089844 C 349.125 381.664062 352.558594 383.953125 359.421875 383.953125 C 366.285156 383.953125 369.714844 381.664062 369.714844 377.089844 Z M 342.261719 377.089844 L 342.261719 370.226562 C 342.261719 361.097656 347.984375 356.519531 359.398438 356.519531 C 363.617188 356.519531 367.046875 357.136719 369.695312 358.40625 L 369.695312 356.519531 L 376.558594 356.519531 L 376.558594 390.792969 L 369.695312 390.792969 L 369.695312 388.953125 C 367.046875 390.179688 363.617188 390.792969 359.398438 390.792969 C 347.984375 390.792969 342.261719 386.242188 342.261719 377.089844 " clip-rule="nonzero"></path></clipPath><clipPath id="56e186e8a2"><path d="M 413.601562 356.519531 L 448 356.519531 L 448 404.601562 L 413.601562 404.601562 Z M 413.601562 356.519531 " clip-rule="nonzero"></path></clipPath><clipPath id="6df7062620"><path d="M 430.738281 363.382812 C 423.875 363.382812 420.441406 365.671875 420.441406 370.246094 L 420.441406 377.089844 C 420.441406 381.664062 423.875 383.953125 430.738281 383.953125 L 430.738281 383.996094 C 435.058594 383.996094 438.492188 384.609375 441.03125 385.816406 L 441.03125 369.886719 C 440.863281 365.542969 437.429688 363.382812 430.738281 363.382812 M 413.601562 370.226562 C 413.601562 361.097656 419.320312 356.519531 430.738281 356.519531 C 442.15625 356.519531 447.875 361.097656 447.875 370.226562 L 447.875 390.792969 L 445.796875 390.792969 L 445.796875 390.835938 L 447.894531 390.835938 C 447.894531 399.964844 442.175781 404.519531 430.757812 404.519531 L 420.464844 404.519531 L 420.464844 397.65625 L 430.738281 397.65625 C 435.589844 397.65625 438.722656 396.511719 440.121094 394.226562 C 438.703125 391.960938 435.566406 390.835938 430.738281 390.835938 L 430.738281 390.792969 C 419.320312 390.792969 413.601562 386.238281 413.601562 377.089844 Z M 413.601562 370.226562 " clip-rule="nonzero"></path></clipPath><clipPath id="3c89ba5c73"><path d="M 486.714844 356.519531 L 493.578125 356.519531 L 493.578125 390.8125 L 486.714844 390.8125 Z M 486.714844 356.519531 " clip-rule="nonzero"></path></clipPath><clipPath id="48ad8e2b4c"><path d="M 532.273438 356.519531 L 566.558594 356.519531 L 566.558594 390.804688 L 532.273438 390.804688 Z M 532.273438 356.519531 " clip-rule="nonzero"></path></clipPath><clipPath id="f3c4c322d9"><path d="M 539.136719 356.519531 L 539.136719 358.363281 C 541.785156 357.136719 545.214844 356.519531 549.410156 356.519531 C 560.828125 356.519531 566.546875 361.097656 566.546875 370.226562 L 566.546875 390.792969 L 559.703125 390.792969 L 559.703125 370.226562 C 559.703125 365.652344 556.273438 363.363281 549.410156 363.363281 C 542.714844 363.363281 539.285156 365.523438 539.117188 369.867188 L 539.117188 390.773438 L 532.273438 390.773438 L 532.273438 356.519531 Z M 539.136719 356.519531 " clip-rule="nonzero"></path></clipPath><clipPath id="e8d1a6815e"><path d="M 444 161 L 607.71875 161 L 607.71875 324.859375 L 444 324.859375 Z M 444 161 " clip-rule="nonzero"></path></clipPath><clipPath id="6c279415fe"><path d="M 525.894531 291.757812 C 558.472656 291.757812 574.742188 280.910156 574.742188 259.175781 L 444.46875 259.175781 L 444.46875 226.621094 C 444.46875 183.195312 471.601562 161.480469 525.894531 161.480469 L 607.171875 161.480469 L 607.171875 194.0625 L 525.894531 194.0625 C 493.316406 194.0625 477.046875 204.90625 477.046875 226.640625 L 607.320312 226.640625 L 607.320312 259.199219 C 607.320312 302.621094 580.1875 324.335938 525.894531 324.335938 L 494.226562 324.335938 L 494.226562 291.757812 Z M 525.894531 291.757812 " clip-rule="nonzero"></path></clipPath><clipPath id="827725ad13"><path d="M 342 161 L 465.378906 161 L 465.378906 324.859375 L 342 324.859375 Z M 342 161 " clip-rule="nonzero"></path></clipPath><clipPath id="90d7da7431"><path d="M 465.058594 291.757812 L 465.058594 324.3125 L 436.355469 324.3125 C 382.019531 324.3125 354.90625 302.644531 354.90625 259.199219 L 354.90625 236.554688 L 342.261719 236.554688 L 342.261719 202.765625 L 354.90625 202.765625 L 354.90625 161.480469 L 387.464844 161.480469 L 387.464844 202.765625 L 400.195312 202.765625 L 400.195312 236.554688 L 387.464844 236.554688 L 387.464844 259.199219 C 387.464844 280.867188 403.796875 291.757812 436.355469 291.757812 Z M 465.058594 291.757812 " clip-rule="nonzero"></path></clipPath><clipPath id="1aa95cba5a"><path d="M 632.074219 161 L 795.789062 161 L 795.789062 390 L 632.074219 390 Z M 632.074219 161 " clip-rule="nonzero"></path></clipPath><clipPath id="83536af7f3"><path d="M 664.886719 161.480469 L 664.886719 259.199219 C 664.886719 280.910156 681.175781 291.753906 713.753906 291.753906 L 713.753906 291.925781 C 734.28125 291.925781 750.570312 294.804688 762.601562 300.546875 L 762.601562 161.480469 L 795.183594 161.480469 L 795.183594 324.503906 C 795.183594 367.824219 768.046875 389.472656 713.753906 389.472656 L 664.886719 389.472656 L 664.886719 356.894531 L 713.753906 356.894531 C 736.78125 356.894531 751.652344 351.46875 758.386719 340.601562 C 751.652344 329.863281 736.78125 324.484375 713.753906 324.484375 L 713.753906 324.3125 C 659.464844 324.3125 632.328125 302.601562 632.328125 259.175781 L 632.328125 161.480469 Z M 664.886719 161.480469 " clip-rule="nonzero"></path></clipPath><clipPath id="4b2086b1a2"><path d="M 603 356.160156 L 638 356.160156 L 638 405 L 603 405 Z M 603 356.160156 " clip-rule="nonzero"></path></clipPath><clipPath id="c48a377642"><path d="M 620.742188 363.375 C 613.878906 363.375 610.445312 365.664062 610.445312 370.238281 L 610.445312 377.101562 C 610.445312 381.675781 613.878906 383.964844 620.742188 383.964844 L 620.742188 384.007812 C 625.0625 384.007812 628.496094 384.621094 631.035156 385.828125 L 631.035156 369.921875 C 630.84375 365.578125 627.433594 363.417969 620.742188 363.417969 M 603.605469 370.238281 C 603.605469 361.085938 609.324219 356.53125 620.742188 356.53125 C 632.160156 356.53125 637.878906 361.109375 637.878906 370.238281 L 637.878906 390.808594 L 635.800781 390.808594 L 635.800781 390.847656 L 637.898438 390.847656 C 637.898438 399.980469 632.179688 404.535156 620.761719 404.535156 L 610.46875 404.535156 L 610.46875 397.671875 L 620.761719 397.671875 C 625.613281 397.671875 628.75 396.527344 630.148438 394.238281 C 628.726562 391.972656 625.59375 390.847656 620.761719 390.847656 L 620.761719 390.808594 C 609.324219 390.808594 603.605469 386.230469 603.605469 377.101562 Z M 603.605469 370.238281 " clip-rule="nonzero"></path></clipPath></defs><g clip-path="url(#943ba9c57f)"><g clip-path="url(#194d7ee4e2)"><path fill="#231f20" d="M 56.886719 356.519531 L 91.171875 356.519531 L 91.171875 404.464844 L 56.886719 404.464844 Z M 56.886719 356.519531 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#58b94cbc59)"><g clip-path="url(#cf7410951e)"><path fill="#231f20" d="M 128.226562 356.519531 L 162.511719 356.519531 L 162.511719 390.804688 L 128.226562 390.804688 Z M 128.226562 356.519531 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#77b2eb9357)"><g clip-path="url(#d5ce663471)"><path fill="#231f20" d="M 43.984375 161.480469 L 206.835938 161.480469 L 206.835938 324.332031 L 43.984375 324.332031 Z M 43.984375 161.480469 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#a3bcb9c258)"><g clip-path="url(#ea7ed97436)"><path fill="#231f20" d="M 199.554688 356.519531 L 233.839844 356.519531 L 233.839844 390.804688 L 199.554688 390.804688 Z M 199.554688 356.519531 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#4ec397588f)"><g clip-path="url(#df76811b89)"><path fill="#231f20" d="M 228.8125 150.890625 L 331.417969 150.890625 L 331.417969 401.394531 L 228.8125 401.394531 Z M 228.8125 150.890625 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#ee106d2b85)"><g clip-path="url(#2c304f7b29)"><path fill="#231f20" d="M 228.8125 150.890625 L 331.417969 150.890625 L 331.417969 401.394531 L 228.8125 401.394531 Z M 228.8125 150.890625 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#dd7ea98c17)"><g clip-path="url(#e166b95444)"><path fill="#231f20" d="M 342.261719 356.519531 L 376.546875 356.519531 L 376.546875 390.804688 L 342.261719 390.804688 Z M 342.261719 356.519531 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#56e186e8a2)"><g clip-path="url(#6df7062620)"><path fill="#231f20" d="M 413.601562 356.519531 L 447.886719 356.519531 L 447.886719 404.46875 L 413.601562 404.46875 Z M 413.601562 356.519531 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#3c89ba5c73)"><path fill="#231f20" d="M 486.714844 356.519531 L 493.570312 356.519531 L 493.570312 390.804688 L 486.714844 390.804688 Z M 486.714844 356.519531 " fill-opacity="1" fill-rule="nonzero"></path></g><g clip-path="url(#48ad8e2b4c)"><g clip-path="url(#f3c4c322d9)"><path fill="#231f20" d="M 532.273438 356.519531 L 566.558594 356.519531 L 566.558594 390.804688 L 532.273438 390.804688 Z M 532.273438 356.519531 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#e8d1a6815e)"><g clip-path="url(#6c279415fe)"><path fill="#231f20" d="M 331.667969 150.890625 L 617.78125 150.890625 L 617.78125 334.921875 L 331.667969 334.921875 Z M 331.667969 150.890625 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#827725ad13)"><g clip-path="url(#90d7da7431)"><path fill="#231f20" d="M 331.667969 150.890625 L 617.78125 150.890625 L 617.78125 334.921875 L 331.667969 334.921875 Z M 331.667969 150.890625 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#1aa95cba5a)"><g clip-path="url(#83536af7f3)"><path fill="#231f20" d="M 593.011719 150.890625 L 805.761719 150.890625 L 805.761719 415.164062 L 593.011719 415.164062 Z M 593.011719 150.890625 " fill-opacity="1" fill-rule="nonzero"></path></g></g><g clip-path="url(#4b2086b1a2)"><g clip-path="url(#c48a377642)"><path fill="#231f20" d="M 593.011719 150.890625 L 805.761719 150.890625 L 805.761719 415.164062 L 593.011719 415.164062 Z M 593.011719 150.890625 " fill-opacity="1" fill-rule="nonzero">
                    </path></g></g>
                </svg>
            </div>
            <!-- Sidebar component, swap this element with another sidebar if you like -->
            <div class="flex grow flex-col gap-y-2 overflow-y-auto bg-gray-900 mt-2">
                <nav class="flex flex-1 flex-col px-6">
                    <ul role="list" class="flex flex-1 flex-col gap-y-7">
                        <li>
                            <ul role="list" class="-mx-2 space-y-1">
                                <li>
                                    <SideMenu v-if="$can('Dashboard')" :href="route('dashboard')"
                                              :active="route().current('dashboard')">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                 :class="route().current('dashboard') ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                 fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
                                            </svg>
                                        </template>
                                        <template #name>Dashboard</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Users')" :href="route('users.index')"
                                              :active="(route().current('users.index') || route().current('users.create') || route().current('users.edit') )">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                 :class="(route().current('users.index') || route().current('users.create') || route().current('users.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                 fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"/>
                                            </svg>
                                        </template>
                                        <template #name>Users</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Leads')" :href="route('leads.index')"
                                        :active="(route().current('leads.index') || route().current('leads.create') || route().current('leads.edit')  || route().current('quotations.convert') || route().current('leads.show') )">
                                        <template #svg>
                                            <svg
                                                class="h-6 w-6 shrink-0 transition-colors duration-200"
                                                :class="{
                                                    'text-white': ['leads.index', 'leads.create', 'leads.edit', 'quotations.convert', 'leads.show'].includes(route().current()),
                                                    'text-gray-700 group-hover:text-white': !['leads.index', 'leads.create', 'leads.edit', 'quotations.convert', 'leads.show'].includes(route().current())
                                                }"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                                stroke="currentColor"
                                                stroke-width="1.5"
                                            >
                                                <rect x="3" y="2" width="18" height="20" rx="2" ry="2" />
                                                <path d="M3 6h14M3 10h12M3 14h10M3 18h8" stroke-linecap="round" />
                                                <path d="M16 15l4 4m-5-3l3 3m-2-4l4 4M14 17l5 5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                        </template>
                                        <template #name>Leads</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Leads')" :href="route('quotations.index')"
                                        :active="(route().current('quotations.index') || route().current('quotations.create') || route().current('quotations.edit') || route().current('quotations.show') || route().current('orders.convert'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('quotations.index') || route().current('quotations.create') || route().current('quotations.edit') || route().current('quotations.show') || route().current('orders.convert')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/>
                                                <path d="M14 2v4h4"/>
                                                <path d="M8 10h8"/>
                                                <path d="M8 14h6"/>
                                                <path d="M8 18h8"/>
                                            </svg>
                                        </template>
                                        <template #name>Quotation</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Leads')" :href="route('orders.index')"
                                              :active="(route().current('orders.index') || route().current('orders.create') || route().current('orders.edit') || route().current('orders.show'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                            :class="(route().current('orders.index') || route().current('orders.create') || route().current('orders.edit') || route().current('orders.show')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 3.5l2.5 2.5 5-5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <g transform="translate(0, 2)">
                                                <path d="M3 6h2l3.6 9.6a1 1 0 0 0 .94.7h8.92a1 1 0 0 0 .94-.7L21 8H6" />
                                                <circle cx="9" cy="20" r="2" />
                                                <circle cx="17" cy="20" r="2" />
                                            </g>
                                        </svg>
                                        </template>
                                        <template #name>Orders</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('Audit Logs')" :href="route('logs')"
                                                :active="(route().current('logs'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('logs')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="8" cy="8" r="4"/>
                                                <path d="M3 20v-2a6 6 0 0 1 12 0v2"/>
                                                <rect x="14" y="6" width="6" height="10" rx="1"/>
                                                <path d="M16 8h2"/>
                                                <path d="M16 10h2"/>
                                                <path d="M16 12h2"/>
                                                <path d="M14 17l2 2 4-4"/>
                                            </svg>
                                        </template>
                                        <template #name>Audit Logs</template>
                                    </SideMenu>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="mt-auto px-4 mt-1">
                <SideMenu v-if="$can('List Setting')" :href="route('setting')" :active="(route().current('setting') || route().current('roles.permission') ||
                        route().current('roles.index') || route().current('roles.create') || route().current('roles.edit'))">
                    <template #svg>
                        <svg class="h-6 w-6 shrink-0 text-gray-700" :class="(route().current('setting') || route().current('roles.permission') ||
                                    route().current('roles.index') || route().current('roles.create') || route().current('roles.edit')) ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </template>
                    <template #name>Settings</template>
                </SideMenu>
            </div>
        </div>

        <!-- Mobile sidebar overlay -->
        <div
            v-show="sidebarOpen"
            class="relative z-50 lg:hidden"
            role="dialog"
            aria-modal="true"
        >
            <!-- Background backdrop -->
            <div
                class="fixed inset-0 bg-gray-900/80 transition-opacity duration-300"
                @click="closeSidebar"
            ></div>

            <!-- Sidebar panel -->
            <div class="fixed inset-0 flex">
                <div
                    id="mobile-sidebar"
                    class="relative mr-16 flex w-full max-w-xs flex-1 transform transition-transform duration-300 ease-in-out"
                    :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
                >
                    <!-- Close button -->
                    <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                        <button
                            type="button"
                            class="-m-2.5 p-2.5 text-white hover:text-gray-300 transition-colors duration-200"
                            @click="closeSidebar"
                        >
                            <span class="sr-only">Close sidebar</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile sidebar content -->
                    <div class="flex grow flex-col bg-white px-6 pb-4 border-r">
                        <!-- Logo -->
                        <img class="h-20 w-auto" src="https://artsypackaging.com/wp-content/uploads/2023/11/Ready_artsy-v4.pdf.svg" alt="">

                        <!-- Navigation -->
                        <nav class="flex flex-1 flex-col overflow-y-auto">
                            <ul role="list" class="flex flex-1 flex-col gap-y-7">
                                <li>
                                    <ul role="list" class="space-y-1">
                                        <li>
                                            <SideMenu v-if="$can('Dashboard')" :href="route('dashboard')"
                                                    :active="route().current('dashboard')">
                                                <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="route().current('dashboard') ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
                                                    </svg>
                                                </template>
                                                <template #name>Dashboard</template>
                                            </SideMenu>
                                        </li>
                                        <li>
                                            <SideMenu v-if="$can('List Users')" :href="route('users.index')"
                                                    :active="(route().current('users.index') || route().current('users.create') || route().current('users.edit') )">
                                                <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('users.index') || route().current('users.create') || route().current('users.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"/>
                                                    </svg>
                                                </template>
                                                <template #name>Users</template>
                                            </SideMenu>
                                        </li>
                                        <li>
                                            <SideMenu v-if="$can('List Leads')" :href="route('leads.index')"
                                                :active="(route().current('leads.index') || route().current('leads.create') || route().current('leads.edit') || route().current('quotations.convert') || route().current('leads.show'))">
                                                <template #svg>
                                                    <svg
                                                        class="h-6 w-6 shrink-0 transition-colors duration-200"
                                                        :class="{
                                                            'text-white': ['leads.index', 'leads.create', 'leads.edit', 'quotations.convert', 'leads.show'].includes(route().current()),
                                                            'text-gray-700 group-hover:text-white': !['leads.index', 'leads.create', 'leads.edit', 'quotations.convert', 'leads.show'].includes(route().current())
                                                        }"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        stroke="currentColor"
                                                        stroke-width="1.5"
                                                    >
                                                        <rect x="3" y="2" width="18" height="20" rx="2" ry="2" />
                                                        <path d="M3 6h14M3 10h12M3 14h10M3 18h8" stroke-linecap="round" />
                                                        <path d="M16 15l4 4m-5-3l3 3m-2-4l4 4M14 17l5 5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>
                                                </template>
                                                <template #name>Leads</template>
                                            </SideMenu>
                                        </li>
                                        <li>
                                            <SideMenu v-if="$can('List Leads')" :href="route('quotations.index')"
                                                :active="(route().current('quotations.index') || route().current('quotations.create') || route().current('quotations.edit')|| route().current('quotations.show') || route().current('orders.convert'))">
                                                <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('quotations.index') || route().current('quotations.create') || route().current('quotations.edit') || route().current('quotations.show') || route().current('orders.convert')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/>
                                                        <path d="M14 2v4h4"/>
                                                        <path d="M8 10h8"/>
                                                        <path d="M8 14h6"/>
                                                        <path d="M8 18h8"/>
                                                    </svg>
                                                </template>
                                                <template #name>Quotation</template>
                                            </SideMenu>
                                        </li>
                                        <li>
                                            <SideMenu v-if="$can('List Leads')" :href="route('orders.index')"
                                                    :active="(route().current('orders.index') || route().current('orders.create') || route().current('orders.edit') || route().current('orders.show'))">
                                                <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                    :class="(route().current('orders.index') || route().current('orders.create') || route().current('orders.edit') || route().current('orders.show')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                    fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M9 3.5l2.5 2.5 5-5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <g transform="translate(0, 2)">
                                                        <path d="M3 6h2l3.6 9.6a1 1 0 0 0 .94.7h8.92a1 1 0 0 0 .94-.7L21 8H6" />
                                                        <circle cx="9" cy="20" r="2" />
                                                        <circle cx="17" cy="20" r="2" />
                                                    </g>
                                                </svg>
                                                </template>
                                                <template #name>Orders</template>
                                            </SideMenu>
                                        </li>
                                        <li>
                                            <SideMenu v-if="$can('Audit Logs')" :href="route('logs')"
                                                        :active="(route().current('logs'))">
                                                <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('logs')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <circle cx="8" cy="8" r="4"/>
                                                        <path d="M3 20v-2a6 6 0 0 1 12 0v2"/>
                                                        <rect x="14" y="6" width="6" height="10" rx="1"/>
                                                        <path d="M16 8h2"/>
                                                        <path d="M16 10h2"/>
                                                        <path d="M16 12h2"/>
                                                        <path d="M14 17l2 2 4-4"/>
                                                    </svg>
                                                </template>
                                                <template #name>Audit Logs</template>
                                            </SideMenu>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:pl-72 border-t">
            <div class="sticky top-0 z-20 flex h-16 shrink-0 items-center gap-x-4 bg-white px-4 shadow sm:gap-x-6 sm:px-6 lg:px-8">
                <button
                    id="hamburger-button"
                    type="button"
                    class="-m-2.5 p-2.5 text-gray-700 lg:hidden hover:text-gray-900 transition-colors duration-200"
                    @click="toggleSidebar"
                >
                    <span class="sr-only">Open sidebar</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/>
                    </svg>
                </button>

                <!-- Separator -->
                <div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>

                <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-between">
                    <div class="flex items-center">
                        <Dropdown align="left" width="48">
                            <template #trigger>
                                <div class="flex w-32">
                                    <a class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" href="#"> Add New +</a>
                                </div>
                            </template>
                            <template #content>
                                <ActionLink :href="route('users.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Add New User</span>
                                    </template>
                                </ActionLink>
                            </template>
                        </Dropdown>
                    </div>
                    <div class="flex items-center gap-x-4 lg:gap-x-6">
                        <NotificationDropdown :notifications="notifications" />

                        <!-- Separator -->
                        <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true"></div>

                        <!-- Profile dropdown -->
                        <div class="relative">
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button type="button" class="-m-1.5 flex items-center p-1.5" id="user-menu-button"
                                            aria-expanded="false" aria-haspopup="true">
                                        <span class="sr-only">Open User Menu</span>
                                        <img class="h-8 w-8 rounded-full bg-gray-50"
                                             src="https://img.freepik.com/premium-photo/avatar-resourcing-company_1254967-6696.jpg?size=626&ext=jpg&ga=GA1.1.*********.1729255085&semt=ais_hybrid"
                                             alt="">
                                        <span class="hidden lg:flex lg:items-center">
                                <span class="ml-4 text-sm font-semibold leading-6 text-gray-900" aria-hidden="true">{{ $page.props.auth.user.name }}</span>
                                <svg class="ml-2 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"/>
                                </svg>
                            </span>
                                    </button>
                                </template>
                                <template #content>
                                    <ActionLink :href="route('profile.edit')" as="button">
                                        <template #svg></template>
                                        <template #text>
                                            <span class="text-sm text-gray-700 leading-5">Your Profile</span>
                                        </template>
                                    </ActionLink>
                                    <ActionLink :href="route('logout')" method="post" as="button">
                                        <template #svg></template>
                                        <template #text>
                                            <span class="text-sm text-gray-700 leading-5">Sign Out</span>
                                        </template>
                                    </ActionLink>
                                </template>
                            </Dropdown>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info flash message -->
            <div v-if="$page.props.flash.message">
                <ToastNotificationVue :message="$page.props.flash.message"/>
            </div>

            <!-- Success flash message -->
            <div v-if="$page.props.flash.success">
                <ToastNotificationSuccessVue :message="$page.props.flash.success"/>
            </div>

            <!-- Error flash message -->
            {{ $page.props.flash.error }}
            <div v-if="$page.props.flash.error">
                <ToastNotificationErrorVue :message="$page.props.flash.error"/>
            </div>

            <!-- Warning flash message -->
            <div v-if="$page.props.flash.warning">
                <ToastNotificationWarningVue :message="$page.props.flash.warning"/>
            </div>

            <main class="py-4 sm:py-6 lg:py-10 bg-gray-100">
                <div class="px-2 sm:px-4 lg:px-8 min-h-screen">
                    <slot/>
                </div>
            </main>
        </div>
    </div>
</template>

<style>

::-webkit-scrollbar {
    display: none;
}

html {
    scrollbar-width: none;
}

.bg-gray-900 {
    background: #ffffff !important;
}

.hover\:bg-indigo-500:hover {
    background: #3A0066 !important;
}

.bg-indigo-600 {
    background: #4B0082 !important;
}

.text-indigo-600 {
    color: #4B0082 !important;
}

.bg-gray-100 {
    background: #e3e9f1bd !important;
}
.bg-gray-50 , tr:nth-child(even){
    background: rgb(249 250 260) !important;
}
.animate-top{
    position:relative;
    animation:animatetop 0.6s
}
@keyframes animatetop
    {from{top:-100px;opacity:0} to{top:0;opacity:1}}

/* Mobile responsive improvements */
@media (max-width: 1023px) {
    /* Ensure mobile sidebar is above everything */
    .mobile-sidebar-overlay {
        z-index: 9999;
    }

    /* Adjust header spacing on mobile */
    .mobile-header {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Improve touch targets on mobile */
    button {
        min-height: 44px;
        min-width: 44px;
    }
}

@media (max-width: 640px) {
    /* Extra small screens */
    .mobile-content {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Smaller text on mobile */
    .mobile-text {
        font-size: 0.875rem;
    }
}

/* Smooth transitions for responsive elements */
.responsive-transition {
    transition: all 0.3s ease-in-out;
}

/* Mobile-first approach for tables and cards */
@media (max-width: 768px) {
    .responsive-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .responsive-card {
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }
}

</style>
