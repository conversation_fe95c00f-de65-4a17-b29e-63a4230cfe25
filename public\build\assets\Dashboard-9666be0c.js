import{_ as y}from"./AdminLayout-bed0e8e1.js";import{T as b,C as w,c as p,b as n,d as i,e as _,u as S,f as j,F as u,Z as M,h as t,t as s,D as h,k as x,j as z,n as C}from"./app-e1dc0e85.js";import"./_plugin-vue_export-helper-c27b6911.js";const O={class:"animate-top"},A=t("div",{class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},[t("div",null,[t("h1",{class:"text-3xl font-bold text-gray-900"},"Dashboard"),t("p",{class:"text-gray-600 mt-1"},"Complete overview of your business performance")])],-1),k={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},q={class:"bg-white overflow-hidden shadow rounded-lg"},T={class:"p-5"},V={class:"flex items-center"},B=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z","clip-rule":"evenodd"})])])],-1),D={class:"ml-5 w-0 flex-1"},H=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Revenue",-1),L={class:"text-lg font-semibold text-gray-900"},F={class:"bg-white overflow-hidden shadow rounded-lg"},N={class:"p-5"},P={class:"flex items-center"},Q=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})])])],-1),R={class:"ml-5 w-0 flex-1"},$=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"This Month",-1),I={class:"text-lg font-semibold text-gray-900"},G={class:"bg-white overflow-hidden shadow rounded-lg"},U={class:"p-5"},E={class:"flex items-center"},J=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM8 15a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})])])],-1),W={class:"ml-5 w-0 flex-1"},Z=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Orders",-1),K={class:"text-lg font-semibold text-gray-900"},X={class:"bg-white overflow-hidden shadow rounded-lg"},Y={class:"p-5"},tt={class:"flex items-center"},et=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})])])],-1),st={class:"ml-5 w-0 flex-1"},ot=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Leads",-1),lt={class:"text-lg font-semibold text-gray-900"},at={class:"bg-white shadow rounded-lg p-6 mb-8"},dt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Conversion Rates",-1),nt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},it={class:"text-center"},ct={class:"text-3xl font-bold text-blue-600"},rt=t("div",{class:"text-sm text-gray-500"},"Lead → Quotation",-1),ut={class:"text-center"},ht={class:"text-3xl font-bold text-green-600"},mt=t("div",{class:"text-sm text-gray-500"},"Quotation → Order",-1),_t={class:"text-center"},xt={class:"text-3xl font-bold text-purple-600"},vt=t("div",{class:"text-sm text-gray-500"},"Lead → Order",-1),gt={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},ft={class:"bg-white shadow rounded-lg p-6"},yt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Sales Pipeline",-1),bt={class:"space-y-4"},wt={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},pt=t("span",null,"Leads",-1),St={class:"w-full bg-gray-200 rounded-full h-2"},jt={class:"flex justify-between text-xs text-gray-500 mt-1"},Mt={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},zt=t("span",null,"Quotations",-1),Ct={class:"w-full bg-gray-200 rounded-full h-2"},Ot={class:"flex justify-between text-xs text-gray-500 mt-1"},At={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},kt=t("span",null,"Orders",-1),qt={class:"w-full bg-gray-200 rounded-full h-2"},Tt={class:"flex justify-between text-xs text-gray-500 mt-1"},Vt={class:"bg-white shadow rounded-lg p-6"},Bt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Status Distribution",-1),Dt={class:"space-y-3"},Ht={class:"flex items-center justify-between"},Lt=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-blue-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Confirmed")],-1),Ft={class:"text-sm font-medium text-gray-900"},Nt={class:"flex items-center justify-between"},Pt=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-purple-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Under Production")],-1),Qt={class:"text-sm font-medium text-gray-900"},Rt={class:"flex items-center justify-between"},$t=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-indigo-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Shipped")],-1),It={class:"text-sm font-medium text-gray-900"},Gt={class:"flex items-center justify-between"},Ut=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-green-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Delivered")],-1),Et={class:"text-sm font-medium text-gray-900"},Jt={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Wt={class:"lg:col-span-2 bg-white shadow rounded-lg p-6"},Zt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Recent Activities",-1),Kt={class:"flow-root"},Xt={class:"-mb-8"},Yt={key:0,class:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"},te={class:"relative flex space-x-3"},ee={class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},se=["d"],oe={class:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4"},le={class:"text-sm text-gray-900"},ae={class:"text-sm text-gray-500"},de={class:"text-right text-sm whitespace-nowrap text-gray-500"},ne={class:"bg-white shadow rounded-lg p-6"},ie=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Top Performing Agents",-1),ce={class:"space-y-4"},re={class:"text-sm font-medium text-gray-900"},ue={class:"text-xs text-gray-500"},he={class:"text-right"},me={class:"text-sm font-medium text-green-600"},ge={__name:"Dashboard",props:{recentOrders:Array,orderStats:Object,revenueStats:Object,leadStats:Object,quotationStats:Object,monthlyTrends:Array,topAgents:Array,recentActivities:Array,permissions:Object},setup(e){const l=e;b({}),w(async()=>{localStorage.setItem("permissions",JSON.stringify(l.permissions))});const c=a=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(a),v=a=>new Date(a).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),g=a=>{const d={lead:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z",quotation:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",order:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"};return d[a]||d.lead},f=a=>{const d={lead:"text-blue-600 bg-blue-100",quotation:"text-green-600 bg-green-100",order:"text-purple-600 bg-purple-100"};return d[a]||d.lead},r=p(()=>{const a=l.leadStats.total>0?(l.quotationStats.total/l.leadStats.total*100).toFixed(1):0,d=l.quotationStats.total>0?(l.orderStats.total/l.quotationStats.total*100).toFixed(1):0,o=l.leadStats.total>0?(l.orderStats.total/l.leadStats.total*100).toFixed(1):0;return{leadToQuotation:a,quotationToOrder:d,leadToOrder:o}});return(a,d)=>(n(),i(u,null,[_(S(M),{title:"Dashboard"}),_(y,null,{default:j(()=>[t("div",O,[A,t("div",k,[t("div",q,[t("div",T,[t("div",V,[B,t("div",D,[t("dl",null,[H,t("dd",L,s(c(e.revenueStats.total_revenue)),1)])])])])]),t("div",F,[t("div",N,[t("div",P,[Q,t("div",R,[t("dl",null,[$,t("dd",I,s(c(e.revenueStats.monthly_revenue)),1)])])])])]),t("div",G,[t("div",U,[t("div",E,[J,t("div",W,[t("dl",null,[Z,t("dd",K,s(e.orderStats.total),1)])])])])]),t("div",X,[t("div",Y,[t("div",tt,[et,t("div",st,[t("dl",null,[ot,t("dd",lt,s(e.leadStats.total),1)])])])])])]),t("div",at,[dt,t("div",nt,[t("div",it,[t("div",ct,s(r.value.leadToQuotation)+"%",1),rt]),t("div",ut,[t("div",ht,s(r.value.quotationToOrder)+"%",1),mt]),t("div",_t,[t("div",xt,s(r.value.leadToOrder)+"%",1),vt])])]),t("div",gt,[t("div",ft,[yt,t("div",bt,[t("div",null,[t("div",wt,[pt,t("span",null,s(e.leadStats.total),1)]),t("div",St,[t("div",{class:"bg-blue-600 h-2 rounded-full",style:h(`width: ${e.leadStats.total>0?100:0}%`)},null,4)]),t("div",jt,[t("span",null,"New: "+s(e.leadStats.new),1),t("span",null,"Won: "+s(e.leadStats.won),1)])]),t("div",null,[t("div",Mt,[zt,t("span",null,s(e.quotationStats.total),1)]),t("div",Ct,[t("div",{class:"bg-green-600 h-2 rounded-full",style:h(`width: ${e.quotationStats.total>0?e.quotationStats.total/Math.max(e.leadStats.total,1)*100:0}%`)},null,4)]),t("div",Ot,[t("span",null,"Pending: "+s(e.quotationStats.pending),1),t("span",null,"Order Placed: "+s(e.quotationStats.order_placed),1)])]),t("div",null,[t("div",At,[kt,t("span",null,s(e.orderStats.total),1)]),t("div",qt,[t("div",{class:"bg-purple-600 h-2 rounded-full",style:h(`width: ${e.orderStats.total>0?e.orderStats.total/Math.max(e.leadStats.total,1)*100:0}%`)},null,4)]),t("div",Tt,[t("span",null,"Confirmed: "+s(e.orderStats.confirmed),1),t("span",null,"Delivered: "+s(e.orderStats.delivered),1)])])])]),t("div",Vt,[Bt,t("div",Dt,[t("div",Ht,[Lt,t("span",Ft,s(e.orderStats.confirmed),1)]),t("div",Nt,[Pt,t("span",Qt,s(e.orderStats.under_production),1)]),t("div",Rt,[$t,t("span",It,s(e.orderStats.shipped),1)]),t("div",Gt,[Ut,t("span",Et,s(e.orderStats.delivered),1)])])])]),t("div",Jt,[t("div",Wt,[Zt,t("div",Kt,[t("ul",Xt,[(n(!0),i(u,null,x(e.recentActivities,(o,m)=>(n(),i("li",{key:m,class:"relative pb-8"},[m!==e.recentActivities.length-1?(n(),i("div",Yt)):z("",!0),t("div",te,[t("div",null,[t("span",{class:C(["h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white",f(o.type)])},[(n(),i("svg",ee,[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:g(o.type)},null,8,se)]))],2)]),t("div",oe,[t("div",null,[t("p",le,s(o.title),1),t("p",ae,s(o.description),1)]),t("div",de,[t("time",null,s(v(o.created_at)),1)])])])]))),128))])])]),t("div",ne,[ie,t("div",ce,[(n(!0),i(u,null,x(e.topAgents,o=>(n(),i("div",{key:o.id,class:"flex items-center justify-between"},[t("div",null,[t("p",re,s(o.first_name)+" "+s(o.last_name),1),t("p",ue,s(o.orders_count)+" orders",1)]),t("div",he,[t("p",me,s(c(o.orders_sum_total_amount)),1)])]))),128))])])])])]),_:1})],64))}};export{ge as default};
