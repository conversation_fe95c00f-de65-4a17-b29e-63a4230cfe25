<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

const props = defineProps({
    counties: {
        type: Array,
        required: true
    },
    lead: {
        type: Object,
        required: true
    }
});

// Initialize form with only quotation-specific fields
const form = useForm({
    lead_id: props.lead?.id || null,
    qty_1: props.lead?.qty_1 || '',
    qty_2: props.lead?.qty_2 || '',
    qty_3: props.lead?.qty_3 || '',
    qty_4: props.lead?.qty_4 || '',
    price_qty_1: '',
    price_qty_2: '',
    price_qty_3: '',
    price_qty_4: '',
    notes: '',
    valid_until: '',
    documents: []
});

// Checkbox states for quantity selection
const includeQty1 = ref(true); // Always include qty 1 by default
const includeQty2 = ref(!!props.lead?.qty_2);
const includeQty3 = ref(!!props.lead?.qty_3);
const includeQty4 = ref(!!props.lead?.qty_4);

const totalAmount = ref(0);

const submit = () => {
    form.post(route('quotations.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const setCounty = (id, name) => {
    form.county_id = id;
};

// const handleDocuments = (files) => {
//     form.documents = files;
// };

// Calculate total amount when prices or quantities change
const calculateTotal = () => {
    let total = 0;

    if (includeQty1.value && form.qty_1 && form.price_qty_1) {
        total += parseFloat(form.qty_1) * parseFloat(form.price_qty_1);
    }
    if (includeQty2.value && form.qty_2 && form.price_qty_2) {
        total += parseFloat(form.qty_2) * parseFloat(form.price_qty_2);
    }
    if (includeQty3.value && form.qty_3 && form.price_qty_3) {
        total += parseFloat(form.qty_3) * parseFloat(form.price_qty_3);
    }
    if (includeQty4.value && form.qty_4 && form.price_qty_4) {
        total += parseFloat(form.qty_4) * parseFloat(form.price_qty_4);
    }

    totalAmount.value = total;
};

// Clear quantity and price when checkbox is unchecked
const handleQtyCheckbox = (qtyNumber, isChecked) => {
    if (!isChecked) {
        form[`qty_${qtyNumber}`] = '';
        form[`price_qty_${qtyNumber}`] = '';
    }
    calculateTotal();
};

// Watch for changes in quantities, prices, and checkboxes
watch([
    () => form.qty_1, () => form.price_qty_1, () => includeQty1.value,
    () => form.qty_2, () => form.price_qty_2, () => includeQty2.value,
    () => form.qty_3, () => form.price_qty_3, () => includeQty3.value,
    () => form.qty_4, () => form.price_qty_4, () => includeQty4.value
], calculateTotal);

// Set default valid until date (30 days from now)
const defaultValidUntil = new Date();
defaultValidUntil.setDate(defaultValidUntil.getDate() + 30);
form.valid_until = defaultValidUntil.toISOString().split('T')[0];
</script>

<template>
    <Head title="Add Quotation" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">
                {{ lead ? 'Convert Lead to Quotation' : 'Add New Quotation' }}
            </h2>

            <div v-if="lead" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-sm text-blue-800">
                    <strong>Converting from Lead:</strong> {{ lead.lead_number }} - {{ lead.client_name }}
                </p>
            </div>

            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">

                        <!-- Lead Information (Read-only) -->
                        <div class="sm:col-span-12">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Lead Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Client Name</p>
                                    <p class="text-sm text-gray-900">{{ lead.client_name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">County</p>
                                    <p class="text-sm text-gray-900">{{ lead.county?.name || 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Dimensions</p>
                                    <p class="text-sm text-gray-900">{{ lead.dimensions }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Open Size</p>
                                    <p class="text-sm text-gray-900">{{ lead.open_size }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Box Style</p>
                                    <p class="text-sm text-gray-900">{{ lead.box_style }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Stock</p>
                                    <p class="text-sm text-gray-900">{{ lead.stock }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Lamination</p>
                                    <p class="text-sm text-gray-900">{{ lead.lamination }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Printing</p>
                                    <p class="text-sm text-gray-900">{{ lead.printing }}</p>
                                </div>
                                <div v-if="lead.add_ons">
                                    <p class="text-sm font-medium text-gray-500">Add-ons</p>
                                    <p class="text-sm text-gray-900">{{ lead.add_ons }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quantities and Pricing -->
                        <div class="sm:col-span-12 mt-6">
                            <h3 class="text-lg font-bold text-gray-900">Quantities and Pricing</h3>
                        </div>

                        <!-- Quantity 1 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty1"
                                    @update:checked="(checked) => handleQtyCheckbox(1, checked)"
                                />
                                <InputLabel value="Include Quantity 1" class="text-base font-medium text-blue-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty1">
                                <div>
                                    <InputLabel for="qty_1" value="QTY 1 *"/>
                                    <TextInput
                                        id="qty_1"
                                        type="number"
                                        v-model="form.qty_1"
                                        min="1"
                                        required
                                    />
                                    <InputError :message="form.errors.qty_1"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_1" value="PRICE QTY 1 *"/>
                                    <TextInput
                                        id="price_qty_1"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_1"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_1"/>
                                </div>
                            </div>
                        </div>
                        <!-- Quantity 2 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty2"
                                    @update:checked="(checked) => handleQtyCheckbox(2, checked)"
                                />
                                <InputLabel value="Include Quantity 2" class="text-base font-medium text-green-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty2">
                                <div>
                                    <InputLabel for="qty_2" value="QTY 2"/>
                                    <TextInput
                                        id="qty_2"
                                        type="number"
                                        v-model="form.qty_2"
                                        min="1"
                                    />
                                    <InputError :message="form.errors.qty_2"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_2" value="PRICE QTY 2"/>
                                    <TextInput
                                        id="price_qty_2"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_2"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_2"/>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 3 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty3"
                                    @update:checked="(checked) => handleQtyCheckbox(3, checked)"
                                />
                                <InputLabel value="Include Quantity 3" class="text-base font-medium text-yellow-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty3">
                                <div>
                                    <InputLabel for="qty_3" value="QTY 3"/>
                                    <TextInput
                                        id="qty_3"
                                        type="number"
                                        v-model="form.qty_3"
                                        min="1"
                                    />
                                    <InputError :message="form.errors.qty_3"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_3" value="PRICE QTY 3"/>
                                    <TextInput
                                        id="price_qty_3"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_3"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_3"/>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 4 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty4"
                                    @update:checked="(checked) => handleQtyCheckbox(4, checked)"
                                />
                                <InputLabel value="Include Quantity 4" class="text-base font-medium text-purple-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty4">
                                <div>
                                    <InputLabel for="qty_4" value="QTY 4"/>
                                    <TextInput
                                        id="qty_4"
                                        type="number"
                                        v-model="form.qty_4"
                                        min="1"
                                    />
                                    <InputError :message="form.errors.qty_4"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_4" value="PRICE QTY 4"/>
                                    <TextInput
                                        id="price_qty_4"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_4"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_4"/>
                                </div>
                            </div>
                        </div>

                        <!-- Total Amount Display -->
                        <div class="sm:col-span-12">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-lg font-semibold text-gray-900">
                                    Estimated Total: ${{ totalAmount.toFixed(2) }}
                                </p>
                            </div>
                        </div>

                        <!-- <div class="sm:col-span-6">
                            <InputLabel for="valid_until" value="Valid Until"/>
                            <TextInput
                                id="valid_until"
                                type="date"
                                v-model="form.valid_until"
                            />
                            <InputError :message="form.errors.valid_until"/>
                        </div> -->

                        <div class="sm:col-span-12">
                            <InputLabel for="notes" value="Notes"/>
                            <TextArea
                                id="notes"
                                v-model="form.notes"
                                rows="4"
                            />
                            <InputError :message="form.errors.notes"/>
                        </div>

                        <!-- <div class="sm:col-span-12">
                            <InputLabel for="documents" value="Documents"/>
                            <MultipleFileUpload @change="handleDocuments" />
                            <InputError :message="form.errors.documents"/>
                        </div> -->
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('quotations.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
