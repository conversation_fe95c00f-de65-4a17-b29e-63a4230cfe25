<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

const props = defineProps({
    counties: {
        type: Array,
        required: true
    },
    lead: {
        type: Object,
        default: null
    }
});

// Initialize form with lead data if converting from lead
const form = useForm({
    client_name: props.lead?.client_name || '',
    county_id: props.lead?.county_id || '',
    lead_id: props.lead?.id || null,
    dimensions: props.lead?.dimensions || '',
    open_size: props.lead?.open_size || '',
    box_style: props.lead?.box_style || '',
    stock: props.lead?.stock || '',
    lamination: props.lead?.lamination || '',
    printing: props.lead?.printing || '',
    add_ons: props.lead?.add_ons || '',
    qty_1: props.lead?.qty_1 || '',
    qty_2: props.lead?.qty_2 || '',
    qty_3: props.lead?.qty_3 || '',
    qty_4: props.lead?.qty_4 || '',
    price_qty_1: '',
    price_qty_2: '',
    price_qty_3: '',
    price_qty_4: '',
    notes: props.lead?.notes || '',
    valid_until: '',
    documents: []
});

const totalAmount = ref(0);

const submit = () => {
    form.post(route('quotations.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const setCounty = (id, name) => {
    form.county_id = id;
};

const handleDocuments = (files) => {
    form.documents = files;
};

// Calculate total amount when prices or quantities change
const calculateTotal = () => {
    let total = 0;

    if (form.qty_1 && form.price_qty_1) {
        total += parseFloat(form.qty_1) * parseFloat(form.price_qty_1);
    }
    if (form.qty_2 && form.price_qty_2) {
        total += parseFloat(form.qty_2) * parseFloat(form.price_qty_2);
    }
    if (form.qty_3 && form.price_qty_3) {
        total += parseFloat(form.qty_3) * parseFloat(form.price_qty_3);
    }
    if (form.qty_4 && form.price_qty_4) {
        total += parseFloat(form.qty_4) * parseFloat(form.price_qty_4);
    }

    totalAmount.value = total;
};

// Watch for changes in quantities and prices
watch([
    () => form.qty_1, () => form.price_qty_1,
    () => form.qty_2, () => form.price_qty_2,
    () => form.qty_3, () => form.price_qty_3,
    () => form.qty_4, () => form.price_qty_4
], calculateTotal);

// Set default valid until date (30 days from now)
const defaultValidUntil = new Date();
defaultValidUntil.setDate(defaultValidUntil.getDate() + 30);
form.valid_until = defaultValidUntil.toISOString().split('T')[0];
</script>

<template>
    <Head title="Add Quotation" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">
                {{ lead ? 'Convert Lead to Quotation' : 'Add New Quotation' }}
            </h2>

            <div v-if="lead" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-sm text-blue-800">
                    <strong>Converting from Lead:</strong> {{ lead.lead_number }} - {{ lead.client_name }}
                </p>
            </div>

            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">

                        <div class="sm:col-span-4">
                            <InputLabel for="client_name" value="Client Name *"/>
                            <TextInput
                                id="client_name"
                                type="text"
                                v-model="form.client_name"
                                required
                            />
                            <InputError :message="form.errors.client_name"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="county_id" value="County *"/>
                            <div class="relative mt-2">
                                <SearchableDropdownNew
                                    :options="counties"
                                    v-model="form.county_id"
                                    @onchange="setCounty"
                                />
                            </div>
                            <InputError :message="form.errors.county_id"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="dimensions" value="Dimensions *"/>
                            <TextInput
                                id="dimensions"
                                type="text"
                                v-model="form.dimensions"
                                required
                            />
                            <InputError :message="form.errors.dimensions"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="open_size" value="Open Size *"/>
                            <TextInput
                                id="open_size"
                                type="text"
                                v-model="form.open_size"
                                required
                            />
                            <InputError :message="form.errors.open_size"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="box_style" value="Box Style *"/>
                            <TextInput
                                id="box_style"
                                type="text"
                                v-model="form.box_style"
                                required
                            />
                            <InputError :message="form.errors.box_style"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="stock" value="Stock *"/>
                            <TextInput
                                id="stock"
                                type="text"
                                v-model="form.stock"
                                required
                            />
                            <InputError :message="form.errors.stock"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="lamination" value="Lamination *"/>
                            <TextInput
                                id="lamination"
                                type="text"
                                v-model="form.lamination"
                                required
                            />
                            <InputError :message="form.errors.lamination"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="printing" value="Printing *"/>
                            <TextInput
                                id="printing"
                                type="text"
                                v-model="form.printing"
                                required
                            />
                            <InputError :message="form.errors.printing"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="add_ons" value="Add-ons"/>
                            <TextInput
                                id="add_ons"
                                type="text"
                                v-model="form.add_ons"
                            />
                            <InputError :message="form.errors.add_ons"/>
                        </div>

                        <!-- Quantities and Pricing -->
                        <div class="sm:col-span-12 mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quantities and Pricing</h3>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="qty_1" value="QTY 1 *"/>
                            <TextInput
                                id="qty_1"
                                type="number"
                                v-model="form.qty_1"
                                min="1"
                            />
                            <InputError :message="form.errors.qty_1"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="price_qty_1" value="PRICE FOR QTY 1 *"/>
                            <TextInput
                                id="price_qty_1"
                                type="number"
                                step="0.01"
                                v-model="form.price_qty_1"
                                min="0"
                            />
                            <InputError :message="form.errors.price_qty_1"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="qty_2" value="QTY 2"/>
                            <TextInput
                                id="qty_2"
                                type="number"
                                v-model="form.qty_2"
                                min="1"
                            />
                            <InputError :message="form.errors.qty_2"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="price_qty_2" value="PRICE FOR QTY 2"/>
                            <TextInput
                                id="price_qty_2"
                                type="number"
                                step="0.01"
                                v-model="form.price_qty_2"
                                min="0"
                            />
                            <InputError :message="form.errors.price_qty_2"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="qty_3" value="QTY 3"/>
                            <TextInput
                                id="qty_3"
                                type="number"
                                v-model="form.qty_3"
                                min="1"
                            />
                            <InputError :message="form.errors.qty_3"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="price_qty_3" value="PRICE FOR QTY 3"/>
                            <TextInput
                                id="price_qty_3"
                                type="number"
                                step="0.01"
                                v-model="form.price_qty_3"
                                min="0"
                            />
                            <InputError :message="form.errors.price_qty_3"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="qty_4" value="QTY 4"/>
                            <TextInput
                                id="qty_4"
                                type="number"
                                v-model="form.qty_4"
                                min="1"
                            />
                            <InputError :message="form.errors.qty_4"/>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="price_qty_4" value="PRICE FOR QTY 4"/>
                            <TextInput
                                id="price_qty_4"
                                type="number"
                                step="0.01"
                                v-model="form.price_qty_4"
                                min="0"
                            />
                            <InputError :message="form.errors.price_qty_4"/>
                        </div>

                        <!-- Total Amount Display -->
                        <div class="sm:col-span-12">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-lg font-semibold text-gray-900">
                                    Estimated Total: ${{ totalAmount.toFixed(2) }}
                                </p>
                            </div>
                        </div>

                        <!-- <div class="sm:col-span-6">
                            <InputLabel for="valid_until" value="Valid Until"/>
                            <TextInput
                                id="valid_until"
                                type="date"
                                v-model="form.valid_until"
                            />
                            <InputError :message="form.errors.valid_until"/>
                        </div> -->

                        <div class="sm:col-span-12">
                            <InputLabel for="notes" value="Notes"/>
                            <TextArea
                                id="notes"
                                v-model="form.notes"
                                rows="4"
                            />
                            <InputError :message="form.errors.notes"/>
                        </div>

                        <div class="sm:col-span-12">
                            <InputLabel for="documents" value="Documents"/>
                            <MultipleFileUpload @change="handleDocuments" />
                            <InputError :message="form.errors.documents"/>
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('quotations.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
