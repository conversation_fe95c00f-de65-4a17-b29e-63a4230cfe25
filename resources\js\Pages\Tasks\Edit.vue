<template>
    <Head title="Edit Task" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Task</h2>
                    <p class="text-sm text-gray-600 mt-1">Update task details and status</p>
                </div>
                <div class="flex space-x-2">
                    <Link :href="route('tasks.show', task.id)" 
                          class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700">
                        View Task
                    </Link>
                    <Link :href="route('tasks.index')" 
                          class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700">
                        ← Back to Tasks
                    </Link>
                </div>
            </div>

            <!-- Form -->
            <form @submit.prevent="submit" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Title -->
                    <div class="md:col-span-2">
                        <InputLabel for="title" value="Task Title *" />
                        <TextInput
                            id="title"
                            type="text"
                            v-model="form.title"
                            class="mt-1 block w-full"
                            required
                        />
                        <InputError :message="form.errors.title" />
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <InputLabel for="description" value="Description" />
                        <textarea
                            id="description"
                            v-model="form.description"
                            rows="3"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        ></textarea>
                        <InputError :message="form.errors.description" />
                    </div>

                    <!-- Type -->
                    <div>
                        <InputLabel for="type" value="Task Type *" />
                        <select
                            id="type"
                            v-model="form.type"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required
                        >
                            <option value="call">📞 Phone Call</option>
                            <option value="follow_up">🔄 Follow Up</option>
                            <option value="meeting">🤝 Meeting</option>
                            <option value="email">📧 Email</option>
                            <option value="quote_follow_up">💰 Quote Follow Up</option>
                            <option value="order_follow_up">📦 Order Follow Up</option>
                            <option value="general">📋 General Task</option>
                            <option value="reminder">⏰ Reminder</option>
                        </select>
                        <InputError :message="form.errors.type" />
                    </div>

                    <!-- Priority -->
                    <div>
                        <InputLabel for="priority" value="Priority *" />
                        <select
                            id="priority"
                            v-model="form.priority"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required
                        >
                            <option value="low">🟢 Low</option>
                            <option value="medium">🟡 Medium</option>
                            <option value="high">🟠 High</option>
                            <option value="urgent">🔴 Urgent</option>
                        </select>
                        <InputError :message="form.errors.priority" />
                    </div>

                    <!-- Status -->
                    <div>
                        <InputLabel for="status" value="Status *" />
                        <select
                            id="status"
                            v-model="form.status"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required
                        >
                            <option value="pending">⏳ Pending</option>
                            <option value="in_progress">🔄 In Progress</option>
                            <option value="completed">✅ Completed</option>
                            <option value="cancelled">❌ Cancelled</option>
                        </select>
                        <InputError :message="form.errors.status" />
                    </div>

                    <!-- Due Date -->
                    <div>
                        <InputLabel for="due_date" value="Due Date & Time *" />
                        <TextInput
                            id="due_date"
                            type="datetime-local"
                            v-model="form.due_date"
                            class="mt-1 block w-full"
                            required
                        />
                        <InputError :message="form.errors.due_date" />
                    </div>

                    <!-- Reminder Date -->
                    <div>
                        <InputLabel for="reminder_date" value="Reminder Date & Time" />
                        <TextInput
                            id="reminder_date"
                            type="datetime-local"
                            v-model="form.reminder_date"
                            class="mt-1 block w-full"
                        />
                        <InputError :message="form.errors.reminder_date" />
                    </div>

                    <!-- Assigned To -->
                    <div>
                        <InputLabel for="assigned_to" value="Assign To *" />
                        <select
                            id="assigned_to"
                            v-model="form.assigned_to"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required
                        >
                            <option v-for="user in users" :key="user.id" :value="user.id">
                                {{ user.first_name }} {{ user.last_name }}
                            </option>
                        </select>
                        <InputError :message="form.errors.assigned_to" />
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <InputLabel for="notes" value="Additional Notes" />
                        <textarea
                            id="notes"
                            v-model="form.notes"
                            rows="3"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        ></textarea>
                        <InputError :message="form.errors.notes" />
                    </div>
                </div>

                <!-- Task Status Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Status Updates:</h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <button type="button" @click="form.status = 'in_progress'"
                                class="px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                            🔄 Start Working
                        </button>
                        <button type="button" @click="form.status = 'completed'"
                                class="px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200">
                            ✅ Mark Complete
                        </button>
                        <button type="button" @click="postponeTask"
                                class="px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200">
                            ⏰ Postpone +1 Day
                        </button>
                        <button type="button" @click="form.status = 'cancelled'"
                                class="px-3 py-2 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200">
                            ❌ Cancel Task
                        </button>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-3">
                    <Link :href="route('tasks.show', task.id)" 
                          class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400">
                        Cancel
                    </Link>
                    <button type="submit" :disabled="form.processing"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 disabled:opacity-50">
                        <span v-if="form.processing">Updating...</span>
                        <span v-else>Update Task</span>
                    </button>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import InputLabel from '@/Components/InputLabel.vue'
import TextInput from '@/Components/TextInput.vue'
import InputError from '@/Components/InputError.vue'

const props = defineProps({
    task: Object,
    users: Array
})

const form = useForm({
    title: props.task.title,
    description: props.task.description,
    type: props.task.type,
    priority: props.task.priority,
    status: props.task.status,
    due_date: props.task.due_date ? new Date(props.task.due_date).toISOString().slice(0, 16) : '',
    reminder_date: props.task.reminder_date ? new Date(props.task.reminder_date).toISOString().slice(0, 16) : '',
    assigned_to: props.task.assigned_to,
    notes: props.task.notes || ''
})

const postponeTask = () => {
    const currentDue = new Date(form.due_date)
    currentDue.setDate(currentDue.getDate() + 1)
    form.due_date = currentDue.toISOString().slice(0, 16)
    
    if (form.reminder_date) {
        const currentReminder = new Date(form.reminder_date)
        currentReminder.setDate(currentReminder.getDate() + 1)
        form.reminder_date = currentReminder.toISOString().slice(0, 16)
    }
}

const submit = () => {
    form.put(route('tasks.update', props.task.id), {
        preserveScroll: true,
    })
}
</script>
