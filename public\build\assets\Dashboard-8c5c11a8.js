import{_ as a}from"./AdminLayout-18e3a786.js";import{T as d,C as c,b as i,d as n,e as t,u as g,f as l,F as h,Z as x,p,l as m,g as s}from"./app-365f100b.js";import{_ as w}from"./_plugin-vue_export-helper-c27b6911.js";const r=e=>(p("data-v-879262d2"),e=e(),m(),e),v=r(()=>s("h2",{class:"font-semibold text-xl text-gray-700 leading-tight"}," Dashboard ",-1)),u=r(()=>s("div",{class:"animate-top"},[s("div",{class:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6"},[s("div",{class:"items-start"},[s("h1",{class:"text-xl lg:text-2xl font-semibold leading-7 text-gray-900"},"Dashboard")])]),s("div",{class:"mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},[s("div",{class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},[s("div",{class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},[s("svg",{class:"w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24"},[s("circle",{cx:"6",cy:"8",r:"3"}),s("path",{d:"M2 18v-1.5c0-1.8 2.5-3.5 4.5-3.5 1 0 2 .2 2.8.6-1.1.8-1.8 2.1-1.8 3.4V18H2z"}),s("circle",{cx:"12",cy:"7",r:"4"}),s("path",{d:"M12 12c-3.5 0-7 1.8-7 4v2h14v-2c0-2.2-3.5-4-7-4z"}),s("circle",{cx:"18",cy:"8",r:"3"}),s("path",{d:"M22 18v-1.5c0-1.8-2.5-3.5-4.5-3.5-1 0-2 .2-2.8.6 1.1.8 1.8 2.1 1.8 3.4V18H22z"})]),s("div",{class:"text-gray-700 min-w-0"},[s("p",{class:"font-semibold text-2xl lg:text-3xl"},"14"),s("p",{class:"text-sm lg:text-base"},"Total Leads")])])]),s("div",{class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},[s("div",{class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},[s("svg",{class:"w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[s("rect",{x:"3",y:"6",width:"18",height:"12",rx:"2",ry:"2",stroke:"currentColor",fill:"none"}),s("path",{d:"M6 10h12M6 14h12",stroke:"currentColor"}),s("path",{d:"M9 6V4h6v2M10 18v2h4v-2",stroke:"currentColor"})]),s("div",{class:"text-gray-700 min-w-0"},[s("p",{class:"font-semibold text-2xl lg:text-3xl"},"15"),s("p",{class:"text-sm lg:text-base"},"Converted Leads")])])]),s("div",{class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},[s("div",{class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},[s("svg",{class:"w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[s("path",{d:"M6 2c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13zM8 12h8v2H8v-2zm0 4h5v2H8v-2zm0-8h8v2H8V8z"})]),s("div",{class:"text-gray-700 min-w-0"},[s("p",{class:"font-semibold text-2xl lg:text-3xl"},"45"),s("p",{class:"text-sm lg:text-base"},"Total Quotations")])])]),s("div",{class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},[s("div",{class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},[s("svg",{class:"w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[s("path",{d:"M3 3h2l1.6 6.6 1.4 5.4c.2.8.9 1.4 1.7 1.4h7c.8 0 1.5-.6 1.7-1.4l1.6-6.6H7.2l-.5-2H21V5H6.2L5.8 3H3zm5 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM9 10h8v2H9v-2zm0 4h5v2H9v-2z"})]),s("div",{class:"text-gray-700 min-w-0"},[s("p",{class:"font-semibold text-2xl lg:text-3xl"},"15"),s("p",{class:"text-sm lg:text-base"},"Total Orders")])])])])],-1)),f={__name:"Dashboard",props:["permissions"],setup(e){const o=e;return d({}),c(async()=>{localStorage.setItem("permissions",JSON.stringify(o.permissions))}),(_,b)=>(i(),n(h,null,[t(g(x),{title:"Dashboard"}),t(a,null,{header:l(()=>[v]),default:l(()=>[u]),_:1})],64))}},H=w(f,[["__scopeId","data-v-879262d2"]]);export{H as default};
