import{r as _,b as n,d,e as l,u as c,f as i,F as w,Z as I,g as e,h as U,v as F,E as R,k as f,i as h,j as N,m as k,t as r,n as K}from"./app-72d803da.js";import{_ as Y,b as Z,a as q}from"./AdminLayout-490ef5fe.js";import{_ as G}from"./CreateButton-18453f12.js";import{M as H,_ as P}from"./Modal-bd242b64.js";import{D as X}from"./DangerButton-700028de.js";import{s as J,_ as Q,a as W}from"./ArrowIcon-125d6bb3.js";import{_ as S}from"./SearchableDropdownNew-67a3836c.js";import{_ as C}from"./InputLabel-8b22735f.js";import"./_plugin-vue_export-helper-c27b6911.js";const ee={class:"animate-top"},te={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},se=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Leads")],-1),oe={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},ae={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},le=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ne={key:0,class:"sm:flex-none"},ie={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},re={class:"flex justify-between mb-2"},de={class:"flex"},ce=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ue={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},me={class:"sm:col-span-4"},_e={class:"relative mt-2"},he={class:"sm:col-span-4"},ge={class:"relative mt-2"},fe={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},pe={class:"shadow rounded-lg"},xe={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ye={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},be={class:"border-b-2"},ve=["onClick"],we={key:0},ke={class:"px-4 py-2.5"},Ce={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Le={class:"px-4 py-2.5"},Me={class:"px-4 py-2.5"},Ae={class:"px-4 py-2.5"},Ne={class:"px-4 py-2.5"},Se={class:"px-4 py-2.5"},Ve={class:"px-4 py-2.5"},Be={class:"items-center px-4 py-2.5"},Ee={class:"flex items-center justify-start gap-4"},$e=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),De=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Te=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),ze=["onClick"],je=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Oe=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Ie=[je,Oe],Ue={key:1},Fe=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Re=[Fe],Ke={class:"p-6"},Ye=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this lead? ",-1),Ze={class:"mt-6 flex justify-end"},ot={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id"],setup(a){const L=a,{form:p,search:g,sort:V,fetchData:qe,sortKey:B,sortDirection:E}=J("leads.index"),x=_(!1),M=_(null),$=[{field:"lead_number",label:"LEAD NUMBER",sortable:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0},{field:"county.name",label:"COUNTRY",sortable:!1},{field:"open_size",label:"OPEN SIZE",sortable:!0},{field:"box_style",label:"BOX STYLE",sortable:!0},{field:"stock",label:"STOCK",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"creator.first_name",label:"AGENT",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],D=s=>{M.value=s,x.value=!0},y=()=>{x.value=!1},T=()=>{p.delete(route("leads.destroy",{lead:M.value}),{onSuccess:()=>y()})},z=s=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",u=_(L.agent_id),m=_(L.county_id),b=_(""),j=(s,o)=>{u.value=s,v(b.value,u.value,m.value)},O=(s,o)=>{m.value=s,v(b.value,u.value,m.value)},v=(s,o,t)=>{b.value=s,p.get(route("leads.index",{search:s,agent_id:o,county_id:t}),{preserveState:!0})};return(s,o)=>(n(),d(w,null,[l(c(I),{title:"Leads"}),l(Y,null,{default:i(()=>[e("div",ee,[e("div",te,[se,e("div",oe,[e("div",ae,[le,U(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>R(g)?g.value=t:null),onInput:o[1]||(o[1]=t=>v(c(g),u.value,m.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-gray-50",placeholder:"Search for leads..."},null,544),[[F,c(g)]])]),a.permissions.canCreateLead?(n(),d("div",ne,[l(G,{href:s.route("leads.create")},{default:i(()=>[f(" Add Lead ")]),_:1},8,["href"])])):h("",!0)])]),e("div",ie,[e("div",re,[e("div",de,[ce,l(C,{for:"customer_id",value:"Filters"})])]),e("div",ue,[e("div",me,[l(C,{for:"customer_id",value:"Agents"}),e("div",_e,[l(S,{options:a.agents,modelValue:u.value,"onUpdate:modelValue":o[2]||(o[2]=t=>u.value=t),onOnchange:j},null,8,["options","modelValue"])])]),e("div",he,[l(C,{for:"customer_id",value:"Country"}),e("div",ge,[l(S,{options:a.counties,modelValue:m.value,"onUpdate:modelValue":o[3]||(o[3]=t=>m.value=t),onOnchange:O},null,8,["options","modelValue"])])])])]),e("div",fe,[e("div",pe,[e("table",xe,[e("thead",ye,[e("tr",be,[(n(),d(w,null,N($,(t,A)=>e("th",{key:A,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:Ge=>c(V)(t.field,t.sortable)},[f(r(t.label)+" ",1),t.sortable?(n(),k(W,{key:0,isSorted:c(B)===t.field,direction:c(E)},null,8,["isSorted","direction"])):h("",!0)],8,ve)),64))])]),a.data.data&&a.data.data.length>0?(n(),d("tbody",we,[(n(!0),d(w,null,N(a.data.data,t=>(n(),d("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",ke,r(t.lead_number),1),e("td",Ce,r(t.client_name),1),e("td",Le,r(t.county?t.county.name:"N/A"),1),e("td",Me,r(t.open_size),1),e("td",Ae,r(t.box_style),1),e("td",Ne,r(t.stock),1),e("td",Se,[e("span",{class:K(["px-4 py-2 rounded-full text-sm font-medium",z(t.status)])},r(t.status.charAt(0).toUpperCase()+t.status.slice(1)),3)]),e("td",Ve,r(t.creator?t.creator.first_name:"N/A"),1),e("td",Be,[e("div",Ee,[l(Z,{align:"right",width:"48"},{trigger:i(()=>[$e]),content:i(()=>[a.permissions.canEditLead?(n(),k(q,{key:0,href:s.route("leads.edit",{id:t.id})},{svg:i(()=>[De]),text:i(()=>[Te]),_:2},1032,["href"])):h("",!0),a.permissions.canDeleteLead?(n(),d("button",{key:1,type:"button",onClick:A=>D(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ie,8,ze)):h("",!0)]),_:2},1024)])])]))),128))])):(n(),d("tbody",Ue,Re))])])]),a.data.data&&a.data.data.length>0?(n(),k(Q,{key:0,class:"mt-6",links:a.data.links},null,8,["links"])):h("",!0)]),l(H,{show:x.value,onClose:y},{default:i(()=>[e("div",Ke,[Ye,e("div",Ze,[l(P,{onClick:y},{default:i(()=>[f("Cancel")]),_:1}),l(X,{class:"ml-3",onClick:T,disabled:c(p).processing},{default:i(()=>[f(" Delete Lead ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{ot as default};
