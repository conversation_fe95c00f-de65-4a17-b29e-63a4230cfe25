import{b as c,d as h,e as l,u as r,f as n,F as u,Z as j,h as e,y as d,l as g,t as o,k as y,j as v,n as x}from"./app-e1dc0e85.js";import{_ as T}from"./AdminLayout-bed0e8e1.js";import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top space-y-6"},V={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},D={class:"flex justify-between items-center"},M=e("div",null,[e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Tasks Dashboard"),e("p",{class:"text-sm text-gray-600 mt-1"},"Overview of your tasks and productivity")],-1),B={class:"flex space-x-2"},S={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4"},A={class:"bg-white border rounded-lg p-6 shadow-sm"},N={class:"flex items-center"},z=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])])],-1),F={class:"ml-4"},O=e("p",{class:"text-sm font-medium text-gray-600"},"Total Tasks",-1),$={class:"text-2xl font-bold text-gray-900"},L={class:"bg-white border rounded-lg p-6 shadow-sm"},U={class:"flex items-center"},H=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1),P={class:"ml-4"},R=e("p",{class:"text-sm font-medium text-gray-600"},"Pending",-1),q={class:"text-2xl font-bold text-yellow-600"},E={class:"bg-white border rounded-lg p-6 shadow-sm"},Q={class:"flex items-center"},W=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])])],-1),Y={class:"ml-4"},Z=e("p",{class:"text-sm font-medium text-gray-600"},"Overdue",-1),G={class:"text-2xl font-bold text-red-600"},I={class:"bg-white border rounded-lg p-6 shadow-sm"},J={class:"flex items-center"},K=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])])],-1),X={class:"ml-4"},ee=e("p",{class:"text-sm font-medium text-gray-600"},"Due Today",-1),te={class:"text-2xl font-bold text-green-600"},se={class:"bg-white border rounded-lg p-6 shadow-sm"},oe={class:"flex items-center"},le=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1),re={class:"ml-4"},ne=e("p",{class:"text-sm font-medium text-gray-600"},"This Week",-1),ie={class:"text-2xl font-bold text-purple-600"},de={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ae={class:"bg-white border rounded-lg p-6 shadow-sm"},ce={class:"flex justify-between items-center mb-4"},he=e("h3",{class:"text-lg font-semibold text-gray-900"},"Recent Tasks",-1),ge={class:"space-y-3"},xe={class:"flex-1"},ue={class:"flex items-center space-x-2"},_e={class:"text-sm font-medium text-gray-900 mt-1"},me={class:"text-xs text-gray-700"},fe={class:"text-right"},pe={class:"text-xs text-gray-700"},be={key:0,class:"text-center py-4 text-gray-700"},ye={class:"bg-white border rounded-lg p-6 shadow-sm"},ve={class:"flex justify-between items-center mb-4"},we=e("h3",{class:"text-lg font-semibold text-gray-900"},"Upcoming Tasks",-1),ke={class:"space-y-3"},je={class:"flex-1"},Te={class:"flex items-center space-x-2"},Ce={class:"text-sm font-medium text-gray-900 mt-1"},Ve={class:"text-xs text-gray-700"},De={class:"text-right"},Me={key:0,class:"text-center py-4 text-gray-700"},Be={class:"bg-white border rounded-lg p-6 shadow-sm"},Se=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1),Ae={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Ne=e("div",{class:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"📞")],-1),ze=e("span",{class:"text-sm font-medium text-blue-900"},"Schedule Call",-1),Fe=e("div",{class:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"🔄")],-1),Oe=e("span",{class:"text-sm font-medium text-green-900"},"Follow Up",-1),$e=e("div",{class:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"🤝")],-1),Le=e("span",{class:"text-sm font-medium text-purple-900"},"Schedule Meeting",-1),Ue=e("div",{class:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"⏰")],-1),He=e("span",{class:"text-sm font-medium text-yellow-900"},"Set Reminder",-1),Ee={__name:"Dashboard",props:{stats:Object,recentTasks:Array,upcomingTasks:Array},setup(i){const _=s=>{const a=new Date(s),t=String(a.getDate()).padStart(2,"0"),w=String(a.getMonth()+1).padStart(2,"0"),k=a.getFullYear();return`${t}/${w}/${k}`},m=s=>s.replace("_"," ").replace(/\b\w/g,a=>a.toUpperCase()),f=s=>new Date(s)<new Date,p=s=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[s]||"bg-gray-100 text-gray-800",b=s=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800";return(s,a)=>(c(),h(u,null,[l(r(j),{title:"Tasks Dashboard"}),l(T,null,{default:n(()=>[e("div",C,[e("div",V,[e("div",D,[M,e("div",B,[l(r(d),{href:s.route("tasks.index"),class:"px-4 py-2 bg-slate-100 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100"},{default:n(()=>[g(" ← Back to Tasks ")]),_:1},8,["href"])])])]),e("div",S,[e("div",A,[e("div",N,[z,e("div",F,[O,e("p",$,o(i.stats.total_tasks),1)])])]),e("div",L,[e("div",U,[H,e("div",P,[R,e("p",q,o(i.stats.pending_tasks),1)])])]),e("div",E,[e("div",Q,[W,e("div",Y,[Z,e("p",G,o(i.stats.overdue_tasks),1)])])]),e("div",I,[e("div",J,[K,e("div",X,[ee,e("p",te,o(i.stats.due_today),1)])])]),e("div",se,[e("div",oe,[le,e("div",re,[ne,e("p",ie,o(i.stats.completed_this_week),1)])])])]),e("div",de,[e("div",ae,[e("div",ce,[he,l(r(d),{href:s.route("tasks.index"),class:"text-sm text-blue-600 hover:text-blue-800"},{default:n(()=>[g(" View All → ")]),_:1},8,["href"])]),e("div",ge,[(c(!0),h(u,null,y(i.recentTasks,t=>(c(),h("div",{key:t.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[e("div",xe,[e("div",ue,[e("span",{class:x(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",b(t.priority)])},o(t.priority),3),e("span",{class:x(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",p(t.type)])},o(m(t.type)),3)]),e("h4",_e,o(t.title),1),e("p",me," Assigned to: "+o(t.assigned_to.first_name)+" "+o(t.assigned_to.last_name),1)]),e("div",fe,[e("div",pe,o(_(t.created_at)),1),l(r(d),{href:s.route("tasks.show",t.id),class:"text-xs text-blue-600 hover:text-blue-800"},{default:n(()=>[g("View")]),_:2},1032,["href"])])]))),128)),i.recentTasks.length===0?(c(),h("div",be," No recent tasks ")):v("",!0)])]),e("div",ye,[e("div",ve,[we,l(r(d),{href:s.route("tasks.index",{status:"pending"}),class:"text-sm text-blue-600 hover:text-blue-800"},{default:n(()=>[g(" View All → ")]),_:1},8,["href"])]),e("div",ke,[(c(!0),h(u,null,y(i.upcomingTasks,t=>(c(),h("div",{key:t.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[e("div",je,[e("div",Te,[e("span",{class:x(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",b(t.priority)])},o(t.priority),3),e("span",{class:x(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",p(t.type)])},o(m(t.type)),3)]),e("h4",Ce,o(t.title),1),e("p",Ve," Assigned to: "+o(t.assigned_to.first_name)+" "+o(t.assigned_to.last_name),1)]),e("div",De,[e("div",{class:x(["text-xs",{"text-red-600 font-semibold":f(t.due_date),"text-gray-700":!f(t.due_date)}])},o(_(t.due_date)),3),l(r(d),{href:s.route("tasks.show",t.id),class:"text-xs text-blue-600 hover:text-blue-800"},{default:n(()=>[g("View")]),_:2},1032,["href"])])]))),128)),i.upcomingTasks.length===0?(c(),h("div",Me," No upcoming tasks ")):v("",!0)])])]),e("div",Be,[Se,e("div",Ae,[l(r(d),{href:s.route("tasks.create",{type:"call"}),class:"flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"},{default:n(()=>[Ne,ze]),_:1},8,["href"]),l(r(d),{href:s.route("tasks.create",{type:"follow_up"}),class:"flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"},{default:n(()=>[Fe,Oe]),_:1},8,["href"]),l(r(d),{href:s.route("tasks.create",{type:"meeting"}),class:"flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"},{default:n(()=>[$e,Le]),_:1},8,["href"]),l(r(d),{href:s.route("tasks.create",{type:"reminder"}),class:"flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"},{default:n(()=>[Ue,He]),_:1},8,["href"])])])])]),_:1})],64))}};export{Ee as default};
