import{_ as c}from"./AdminLayout-931121ed.js";import{b as r,m as d,f as a,g as e,B as m,u as l,x as h,T as _,r as f,d as i,e as o,F as g,Z as p,i as u}from"./app-21b1097d.js";const x={class:"text-lg font-semibold leading-7 text-gray-900"},w={__name:"CustomButton",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(s){return(t,n)=>(r(),d(l(h),{href:s.href,class:"flex justify-between items-center border border-gray-300 px-4 py-6 bg-white rounded-lg shadow-sm hover:shadow hover:border-gray-300"},{default:a(()=>[e("h3",x,[m(t.$slots,"default")])]),_:3},8,["href"]))}},v={class:"animate-top"},y=e("div",{class:"sm:flex sm:items-center"},[e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Settings")]),e("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"})],-1),b={class:"border-gray-900 mt-10",style:{height:"500px"}},B={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},k={key:0,class:"sm:col-span-2"},S=e("svg",{class:"w-12 h-12 fill-current text-indigo-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 2l7 4v6c0 5-3 9-7 10-4-1-7-5-7-10V6l7-4z"}),e("circle",{cx:"12",cy:"10",r:"3",fill:"white"}),e("path",{d:"M9 16c1-2 5-2 6 0",stroke:"white","stroke-width":"2",fill:"none"})],-1),V=e("span",{class:"font-semibold text-lg ml-4"},"Roles & Permissions",-1),N={__name:"Index",props:["permissions"],setup(s){return _({}),f(""),(t,n)=>(r(),i(g,null,[o(l(p),{title:"Settings"}),o(c,null,{default:a(()=>[e("div",v,[y,e("div",b,[e("div",B,[s.permissions.canPermissionsAdd?(r(),i("div",k,[o(w,{href:t.route("roles.index")},{default:a(()=>[S,V]),_:1},8,["href"])])):u("",!0)])])])]),_:1})],64))}};export{N as default};
