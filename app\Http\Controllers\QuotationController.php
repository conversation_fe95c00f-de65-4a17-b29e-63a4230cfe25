<?php

namespace App\Http\Controllers;

use App\Models\Quotation;
use App\Models\County;
use App\Models\Lead;
use App\Models\Document;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\StoreQuotationRequest;
use Inertia\Inertia;
use App\Traits\QueryTrait;
use App\Traits\FileUploadTrait;
use Config;
use PDF;

class QuotationController extends Controller
{
    use QueryTrait, FileUploadTrait;

    public function index(Request $request)
    {
        $query = Quotation::with(['county', 'creator', 'lead']);

        $county_id = $request->input('county_id');
        $agent_id = $request->input('agent_id');
        $status = $request->input('status');

        // If not admin, show only quotations created by the logged-in user
        if (!auth()->user()->hasRole('Admin')) {
            $query->where('created_by', auth()->id());
        }

        if($agent_id) {
            $query->where('created_by', $agent_id);
        }

        if($county_id) {
            $query->where('county_id', $county_id);
        }

        if($status) {
            $query->where('status', $status);
        }

        $searchableFields = ['quotation_number', 'client_name', 'status', 'lead.lead_number'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);

        $permissions = [
            'canCreateQuotation' => true,
            'canEditQuotation' => true,
            'canDeleteQuotation' => true,
        ];

        $counties = County::all();
        $agents = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $statusOptions = [
            ['id' => 'draft', 'name' => 'Draft'],
            ['id' => 'sent', 'name' => 'Sent'],
            ['id' => 'accepted', 'name' => 'Accepted'],
            ['id' => 'rejected', 'name' => 'Rejected'],
            ['id' => 'expired', 'name' => 'Expired'],
        ];

        return Inertia::render('Quotation/List', compact('data', 'permissions', 'counties', 'agents', 'statusOptions', 'county_id', 'agent_id', 'status'));
    }

    public function create(Request $request)
    {
        $counties = County::all();
        $lead = null;

        // If creating from lead
        if ($request->has('lead_id')) {
            $lead = Lead::findOrFail($request->lead_id);
        }

        return Inertia::render('Quotation/Add', compact('counties', 'lead'));
    }

    public function store(StoreQuotationRequest $request)
    {
        $data = $request->validated();
        DB::beginTransaction();
        try {

            if(isset($data['id'])){
                // Update existing quotation
                $data['updated_by'] = auth()->id();
                $quotation = Quotation::find($data['id']);
                $quotation->update($data);
                $files = $request->file('documents');
                if($files){
                    $this->uploadDocuments($files, $data['id']);
                }
                DB::commit();
                return Redirect::to('/quotations')->with('success', 'Quotation updated Successfully');
            } else {
                // Create new quotation
                $year = date('Y');
                $lastQuotation = Quotation::whereYear('created_at', $year)->latest()->first();
                $lastId = $lastQuotation ? intval(explode('-', $lastQuotation->quotation_number)[1]) : 0;
                $quotationNumber = 'QUO-' . $year . '-' . str_pad($lastId + 1, 4, '0', STR_PAD_LEFT);
                $data['quotation_number'] = $quotationNumber;
                $data['created_by'] = $data['updated_by'] = auth()->id();

                // Set default valid until date (30 days from now)
                // if (!isset($data['valid_until'])) {
                //     $data['valid_until'] = now()->addDays(30)->format('Y-m-d');
                // }

                $quotation = Quotation::create($data);
                $files = $request->file('documents');
                if($files){
                    $this->uploadDocuments($files, $quotation->id);
                }

                // If created from lead, update lead status to quotation
                if (isset($data['lead_id']) && $data['lead_id']) {
                    Lead::find($data['lead_id'])->update(['status' => 'quotation']);
                }

                DB::commit();
                return Redirect::to('/quotations')->with('success', 'Quotation created Successfully');
            }

        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::to('/quotations')->with('error', $e->getMessage());
        }
    }

    private function uploadDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.quotationDocument');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "quotation";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

    public function show(string $id)
    {
        $quotation = Quotation::with(['county', 'creator', 'lead', 'documents'])->findOrFail($id);

        // Check if user can view this quotation
        if (!auth()->user()->hasRole('Admin') && $quotation->created_by !== auth()->id()) {
            return Redirect::to('/quotations')->with('error', 'You are not authorized to view this quotation');
        }

        return Inertia::render('Quotation/Show', compact('quotation'));
    }

    public function edit(string $id)
    {
        $data = Quotation::with('documents')->findOrFail($id);
        $filepath = Config::get('constants.uploadFilePath.quotationDocument');

        // Check if user can edit this quotation
        if (!auth()->user()->hasRole('Admin') && $data->created_by !== auth()->id()) {
            return Redirect::to('/quotations')->with('error', 'You are not authorized to edit this quotation');
        }

        $counties = County::all();
        return Inertia::render('Quotation/Edit', compact('data', 'counties', 'filepath'));
    }

    public function destroy(string $id)
    {
        $quotation = Quotation::findOrFail($id);

        // Check if user can delete this quotation
        if (!auth()->user()->hasRole('Admin') && $quotation->created_by !== auth()->id()) {
            return Redirect::to('/quotations')->with('error', 'You are not authorized to delete this quotation');
        }

        DB::beginTransaction();
        try {
            $quotation->delete();
            DB::commit();
            return Redirect::to('/quotations')->with('success', 'Quotation Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/quotations')->with('error', $e->getMessage());
        }
    }

    public function generatePdf(string $id)
    {
        $quotation = Quotation::with(['county', 'creator', 'lead'])->findOrFail($id);

        // Check if user can view this quotation
        if (!auth()->user()->hasRole('Admin') && $quotation->created_by !== auth()->id()) {
            return Redirect::to('/quotations')->with('error', 'You are not authorized to view this quotation');
        }

        $pdf = PDF::loadView('quotations.pdf', compact('quotation'));
        // return $pdf->stream();
        return $pdf->download('quotation-' . $quotation->quotation_number . '.pdf');
    }

    public function updateStatus(Request $request, string $id)
    {
        $request->validate([
            'status' => 'required|in:draft,sent,accepted,rejected,expired'
        ]);

        $quotation = Quotation::findOrFail($id);

        // Check if user can edit this quotation
        if (!auth()->user()->hasRole('Admin') && $quotation->created_by !== auth()->id()) {
            return Redirect::back()->with('error', 'You are not authorized to edit this quotation');
        }

        DB::beginTransaction();
        try {
            $quotation->update([
                'status' => $request->status,
                'updated_by' => auth()->id()
            ]);
            DB::commit();
            return Redirect::back()->with('success', 'Quotation status updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function removedocument($id)
    {
        DB::beginTransaction();
        try {
            $document = Document::find($id);
            if ($document) {
                $filePath = Config::get('constants.uploadFilePath.quotationDocument');
                $filePathToDelete = $filePath['default']. $document->name;
                if (file_exists($filePathToDelete)) {
                    unlink($filePathToDelete); // Delete the file
                }
                $document->delete();
            }
            DB::commit();
            return Redirect::back()->with('success','Document Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function convertFromLead(string $leadId)
    {
        $lead = Lead::findOrFail($leadId);

        // Check if user can access this lead
        // if (!auth()->user()->hasRole('Admin') && $lead->created_by !== auth()->id()) {
        //     return Redirect::to('/leads')->with('error', 'You are not authorized to convert this lead');
        // }

        $counties = County::all();
        return Inertia::render('Quotation/Add', compact('counties', 'lead'));
    }
}
