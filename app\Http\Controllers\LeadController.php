<?php

namespace App\Http\Controllers;

use App\Models\Lead;
use App\Models\County;
use App\Models\Document;
use App\Models\User;
use App\Models\Quotation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\StoreLeadRequest;
use Inertia\Inertia;
use App\Traits\QueryTrait;
use App\Traits\FileUploadTrait;
use Config;

class LeadController extends Controller
{
    use QueryTrait, FileUploadTrait;

    public function __construct()
    {
        $this->middleware('permission:List Leads')->only(['index']);
        $this->middleware('permission:Create Lead')->only(['create', 'store']);
        $this->middleware('permission:Edit Lead')->only(['edit', 'update']);
        $this->middleware('permission:Delete Lead')->only('destroy');
    }

    public function index(Request $request)
    {
        $query = Lead::with(['county', 'creator']);


        $county_id = $request->input('county_id');
        $agent_id = $request->input('agent_id');
        $status = $request->input('status');
        $isAdmin = auth()->user()->hasRole('Admin');

        // If not admin, show only leads created by the logged-in user
        if (!auth()->user()->hasRole('Admin')) {
            $query->where('created_by', auth()->id());
        }

        if($agent_id) {
            $query->where('created_by', $agent_id);
        }

        if($county_id) {
            $query->where('county_id', $county_id);
        }

        if($status) {
            $query->where('status', $status);
        }

        $searchableFields = ['lead_number', 'client_name', 'dimensions', 'open_size', 'county.name', 'open_size', 'box_style', 'stock', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);

        $permissions = [
            'canCreateLead' => auth()->user()->can('Create Lead'),
            'canEditLead' => auth()->user()->can('Edit Lead'),
            'canDeleteLead' => auth()->user()->can('Delete Lead'),
            'canCreateQuotation' => true,
        ];

        $counties = County::where('active', true)->get();
        $agents = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();

        return Inertia::render('Lead/List', compact('data', 'permissions', 'counties', 'agents', 'county_id', 'agent_id', 'status', 'isAdmin'));
    }

    public function create()
    {
        $counties = County::where('active', true)->get();
        return Inertia::render('Lead/Add', compact('counties'));
    }

    public function store(StoreLeadRequest $request)
    {
        $data = $request->all();
        DB::beginTransaction();
        try {

            if(isset($data['id'])){
                $data['updated_by'] = auth()->id();
                $lead = Lead::find($data['id']);
                $lead->update($data);
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $data['id']);
                }
                DB::commit();
                $redirectUrl = session('leads_url', route('leads.index'));
                session()->forget('leads_url');
                return Redirect::to($redirectUrl)->with('success', 'Lead updated Successfully');
            } else {
                $year = date('Y');
                $lastLead = Lead::whereYear('created_at', $year)->latest()->first();
                $lastId = $lastLead ? intval(explode('-', $lastLead->lead_number)[1]) : 0;
                $leadNumber = $year . '-' . str_pad($lastId + 1, 4, '0', STR_PAD_LEFT);
                $data['lead_number'] = $leadNumber;
                $data['created_by'] = $data['updated_by'] = auth()->id();
                $lead = Lead::create($data);
                $files = $request->file('document');
                if($files){
                    $this->uploadDocuments($files, $lead->id);
                }
                DB::commit();
                return Redirect::to('/leads')->with('success', 'Lead created Successfully');
            }

        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::to('/leads')->with('error', $e->getMessage());
        }
    }

    private function uploadDocuments($files, $id)
    {
        $filePath = Config::get('constants.uploadFilePath.leadDocument');
        foreach ($files as $file){
            $originalName = $file->getClientOriginalName();
            $fileName = time().str_replace(' ', '-', $originalName);
            $path = $filePath['default'];
            if(!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            $upload_success = $file->move($path, $fileName);
            if($upload_success){
                $data['entity_id'] = $id;
                $data['entity_type'] = "lead";
                $data['name'] = $fileName;
                $data['orignal_name'] = $originalName;
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                Document::create($data);
            }
        }
    }

    public function edit(string $id)
    {
        $data = Lead::with('documents')->findOrFail($id);
        $filepath = Config::get('constants.uploadFilePath.leadDocument');
        // Check if user can edit this lead
        if (!auth()->user()->hasRole('Admin') && $data->created_by !== auth()->id()) {
            return Redirect::to('/leads')->with('error', 'You are not authorized to edit this lead');
        }

        $counties = County::where('active', true)->get();
        session(key: ['leads_url' => url()->previous()]);
        return Inertia::render('Lead/Edit', compact('data', 'counties', 'filepath'));
    }

    public function destroy(string $id)
    {
        $lead = Lead::findOrFail($id);

        // Check if user can delete this lead
        if (!auth()->user()->hasRole('Admin') && $lead->created_by !== auth()->id()) {
            return Redirect::to('/leads')->with('error', 'You are not authorized to delete this lead');
        }

        DB::beginTransaction();
        try {
            $lead->delete();
            DB::commit();
            return Redirect::to('/leads')->with('success', 'Lead Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/leads')->with('error', $e->getMessage());
        }
    }

    public function show(string $id)
    {
        $leads = Lead::with(['county', 'creator', 'documents'])->findOrFail($id);
        return Inertia::render('Lead/Show', compact('leads'));
    }

    public function removedocument($id)
    {
        DB::beginTransaction();
        try {
            $document = Document::find($id);
            if ($document) {
                $filePath = Config::get('constants.uploadFilePath.leadDocument');
                $filePathToDelete = $filePath['default']. $document->name;
                if (file_exists($filePathToDelete)) {
                    unlink($filePathToDelete); // Delete the file
                }
                $document->delete();
            }
            DB::commit();
            return Redirect::back()->with('success','Document Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:new,contacted,quotation,negotiation,won,lost'
        ]);

        DB::beginTransaction();
        try {

            $lead = Lead::findOrFail($id);
            if($request->status == 'quotation'){
                $quotation = Quotation::where('lead_id', $id)->first();
                if($quotation){
                    $lead->update([
                        'status' => $request->status,
                        'updated_by' => auth()->id()
                    ]);
                    DB::commit();
                    return Redirect::back()->with('error', 'Quotation already exists for this lead');
                } else {
                    $data = $lead->toArray();
                    $year = date('Y');
                    $lastQuotation = Quotation::whereYear('created_at', $year)->latest()->first();
                    $lastId = $lastQuotation ? intval(explode('-', $lastQuotation->quotation_number)[1]) : 0;
                    $quotationNumber = 'QUO-' . $year . '-' . str_pad($lastId + 1, 4, '0', STR_PAD_LEFT);
                    $data['quotation_number'] = $quotationNumber;
                    $data['created_by'] = $data['updated_by'] = auth()->id();
                    $data['status'] = 'pending';
                    $data['lead_id'] = $lead->id;
                    // if (!isset($data['valid_until'])) {
                    //     $data['valid_until'] = now()->addDays(30)->format('Y-m-d');
                    // }
                    $quotation = Quotation::create($data);
                }
            }
            $lead->update([
                'status' => $request->status,
                'updated_by' => auth()->id()
            ]);
            DB::commit();

            return Redirect::back()->with('success', 'Lead status updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
