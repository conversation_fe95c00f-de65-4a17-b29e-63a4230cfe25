import{r as O,b as o,d as n,e as b,u as _,f as m,F as p,Z as A,h as e,t as a,i as f,z as v,k as F,j as d,q as y,l as k,y as w,O as c,g as C,n as x}from"./app-e1dc0e85.js";import{_ as R}from"./AdminLayout-bed0e8e1.js";import"./_plugin-vue_export-helper-c27b6911.js";const $={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},D=e("div",null,[e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Notifications"),e("p",{class:"text-sm text-gray-600 mt-1"},"Stay updated with your tasks and activities")],-1),q={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},H={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},L={class:"flex items-center"},E=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"})])],-1),P={class:"ml-4"},Q=e("p",{class:"text-sm font-medium text-blue-600"},"Total",-1),I={class:"text-2xl font-semibold text-blue-900"},W={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},Y={class:"flex items-center"},Z=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1),G={class:"ml-4"},J=e("p",{class:"text-sm font-medium text-yellow-600"},"Unread",-1),K={class:"text-2xl font-semibold text-yellow-900"},X={class:"bg-red-50 border border-red-200 rounded-lg p-4"},ee={class:"flex items-center"},te=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1),se={class:"ml-4"},oe=e("p",{class:"text-sm font-medium text-red-600"},"High Priority",-1),re={class:"text-2xl font-semibold text-red-900"},le={class:"bg-gray-50 p-4 rounded-lg mb-6"},ae={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ne=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Type",-1),de=e("option",{value:""},"All Types",-1),ie=e("option",{value:"task_reminder"},"Task Reminder",-1),ce=e("option",{value:"task_overdue"},"Task Overdue",-1),ue=e("option",{value:"lead_update"},"Lead Update",-1),_e=e("option",{value:"quotation_update"},"Quotation Update",-1),me=e("option",{value:"order_update"},"Order Update",-1),xe=e("option",{value:"system"},"System",-1),ge=[de,ie,ce,ue,_e,me,xe],he=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1),be=e("option",{value:""},"All Notifications",-1),pe=e("option",{value:"1"},"Unread Only",-1),fe=[be,pe],ve={class:"space-y-3"},ye={class:"flex items-start justify-between"},ke={class:"flex-1"},we={class:"flex items-center space-x-2 mb-2"},Ce={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"},je={class:"text-sm font-semibold text-gray-900 mb-1"},Ne={class:"text-sm text-gray-600 mb-2"},ze={class:"flex items-center space-x-4 text-xs text-gray-500"},Me={key:0},Se={class:"flex flex-col space-y-2 ml-4"},Te=["onClick"],Ue=["onClick"],Ve=["onClick"],Be={key:0,class:"text-center py-12"},Oe=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"})],-1),Ae=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No notifications",-1),Fe=e("p",{class:"mt-1 text-sm text-gray-500"},"You're all caught up!",-1),Re=[Oe,Ae,Fe],$e={key:0,class:"mt-6"},De={class:"flex items-center justify-between"},qe={class:"flex-1 flex justify-between sm:hidden"},Pe={__name:"Index",props:{notifications:Object,stats:Object},setup(r){const i=O({type:"",unread_only:""}),u=()=>{c.get(route("notifications.index"),i.value,{preserveState:!0,preserveScroll:!0})},j=()=>{i.value={type:"",unread_only:""},u()},g=async t=>{try{await C.post(`/notifications/${t}/read`),c.reload({only:["notifications","stats"]})}catch(l){console.error("Failed to mark notification as read:",l)}},N=async()=>{try{await C.post("/notifications/read-all"),c.reload({only:["notifications","stats"]})}catch(t){console.error("Failed to mark all as read:",t)}},z=t=>{confirm("Delete this notification?")&&c.delete(route("notifications.destroy",t),{onSuccess:()=>{c.reload({only:["notifications","stats"]})}})},M=t=>{t.action_data&&t.action_data.url&&(t.is_read||g(t.id),window.location.href=t.action_data.url)},S=t=>new Date(t).toLocaleString(),T=t=>t.replace("_"," ").replace(/\b\w/g,l=>l.toUpperCase()),U=t=>t.client_name?t.client_name:t.quotation_number?`Quotation ${t.quotation_number}`:t.order_number?`Order ${t.order_number}`:t.title?t.title:"Unknown",V=t=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[t]||"bg-gray-100 text-gray-800",B=t=>({task_reminder:"bg-blue-100 text-blue-800",task_overdue:"bg-red-100 text-red-800",lead_update:"bg-green-100 text-green-800",quotation_update:"bg-purple-100 text-purple-800",order_update:"bg-orange-100 text-orange-800",system:"bg-gray-100 text-gray-800"})[t]||"bg-gray-100 text-gray-800";return(t,l)=>(o(),n(p,null,[b(_(A),{title:"Notifications"}),b(R,null,{default:m(()=>[e("div",$,[e("div",{class:"flex justify-between items-center mb-6"},[D,e("div",{class:"flex space-x-2"},[e("button",{onClick:N,class:"flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"}," ✅ Mark All Read ")])]),e("div",q,[e("div",H,[e("div",L,[E,e("div",P,[Q,e("p",I,a(r.stats.total),1)])])]),e("div",W,[e("div",Y,[Z,e("div",G,[J,e("p",K,a(r.stats.unread),1)])])]),e("div",X,[e("div",ee,[te,e("div",se,[oe,e("p",re,a(r.stats.high_priority),1)])])])]),e("div",le,[e("div",ae,[e("div",null,[ne,f(e("select",{"onUpdate:modelValue":l[0]||(l[0]=s=>i.value.type=s),onChange:u,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},ge,544),[[v,i.value.type]])]),e("div",null,[he,f(e("select",{"onUpdate:modelValue":l[1]||(l[1]=s=>i.value.unread_only=s),onChange:u,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},fe,544),[[v,i.value.unread_only]])]),e("div",{class:"flex items-end"},[e("button",{onClick:j,class:"px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700"}," Clear Filters ")])])]),e("div",ve,[(o(!0),n(p,null,F(r.notifications.data,s=>(o(),n("div",{key:s.id,class:x(["border rounded-lg p-4 hover:shadow-md transition-shadow",{"bg-blue-50 border-blue-200":!s.is_read,"bg-white":s.is_read}])},[e("div",ye,[e("div",ke,[e("div",we,[e("span",{class:x(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",V(s.priority)])},a(s.priority),3),e("span",{class:x(["inline-flex px-2 py-1 text-xs rounded-full",B(s.type)])},a(T(s.type)),3),s.is_read?d("",!0):(o(),n("span",Ce," NEW "))]),e("h3",je,a(s.title),1),e("p",Ne,a(s.message),1),e("div",ze,[e("span",null,a(S(s.created_at)),1),s.notifiable?(o(),n("span",Me," Related to: "+a(U(s.notifiable)),1)):d("",!0)])]),e("div",Se,[s.is_read?d("",!0):(o(),n("button",{key:0,onClick:h=>g(s.id),class:"px-3 py-1 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200"}," Mark Read ",8,Te)),s.action_data&&s.action_data.url?(o(),n("button",{key:1,onClick:h=>M(s),class:"px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"},a(s.action_data.action||"View"),9,Ue)):d("",!0),e("button",{onClick:h=>z(s.id),class:"px-3 py-1 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200"}," Delete ",8,Ve)])])],2))),128)),r.notifications.data.length===0?(o(),n("div",Be,Re)):d("",!0)]),r.notifications.links?(o(),n("div",$e,[e("nav",De,[e("div",qe,[r.notifications.prev_page_url?(o(),y(_(w),{key:0,href:r.notifications.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},{default:m(()=>[k(" Previous ")]),_:1},8,["href"])):d("",!0),r.notifications.next_page_url?(o(),y(_(w),{key:1,href:r.notifications.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},{default:m(()=>[k(" Next ")]),_:1},8,["href"])):d("",!0)])])])):d("",!0)])]),_:1})],64))}};export{Pe as default};
