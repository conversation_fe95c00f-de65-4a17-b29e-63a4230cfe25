import{b as s,d as t,g as o,B as a}from"./app-631e032c.js";import{_ as r}from"./_plugin-vue_export-helper-c27b6911.js";const _={class:"min-h-screen loginview bg-slate-100 flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100"},n={class:"w-full sm:max-w-md mt-6 p-6 bg-white shadow overflow-hidden sm:rounded-lg border-gray-300"},c={__name:"GuestLayout",setup(d){return(e,l)=>(s(),t("div",_,[o("div",n,[a(e.$slots,"default",{},void 0,!0)])]))}},m=r(c,[["__scopeId","data-v-07a94ea7"]]);export{m as G};
