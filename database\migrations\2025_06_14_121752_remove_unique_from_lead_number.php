<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
             $table->dropUnique(['lead_number']);
        });

        Schema::table('quotations', function (Blueprint $table) {
             $table->dropUnique(['quotation_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_number', function (Blueprint $table) {
            //
        });
    }
};
