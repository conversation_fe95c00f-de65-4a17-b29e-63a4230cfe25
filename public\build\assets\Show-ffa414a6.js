import{_ as p,a as x}from"./AdminLayout-4519ee57.js";import{P as b}from"./PrimaryButton-09156d82.js";import{b as s,d as a,e as n,u as w,f as i,F as _,Z as v,g as t,t as o,k as m,n as k,i as l,j as L}from"./app-3b719d4c.js";import"./_plugin-vue_export-helper-c27b6911.js";const q={class:"animate-top"},C={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},S={class:"text-3xl font-bold text-gray-900"},B=t("p",{class:"text-gray-600 mt-1"},"Leads Details",-1),N={class:"flex flex-col sm:flex-row gap-3"},j=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),z={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},I={class:"lg:col-span-2 space-y-8"},D={class:"bg-white shadow rounded-lg p-6"},P=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1),A={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),V={class:"mt-1 text-sm text-gray-700"},M=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),E={class:"mt-1 text-sm text-gray-700"},H=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),O=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),U={class:"mt-1 text-sm text-gray-700"},$={class:"bg-white shadow rounded-lg p-6"},F=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),T={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},R=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),Z={class:"mt-1 text-sm text-gray-700"},G=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),J={class:"mt-1 text-sm text-gray-700"},K=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),W={class:"mt-1 text-sm text-gray-700"},X=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),Y={class:"mt-1 text-sm text-gray-700"},tt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),et={class:"mt-1 text-sm text-gray-700"},st=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),at={class:"mt-1 text-sm text-gray-700"},ot={key:0,class:"md:col-span-2"},lt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),dt={class:"mt-1 text-sm text-gray-700"},nt={class:"bg-white shadow rounded-lg p-6"},it=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quantity Details",-1),ct={class:"overflow-x-auto"},rt={class:"min-w-full divide-y divide-gray-200"},xt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Requested"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Confirmed"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Status")])],-1),mt={class:"bg-white divide-y divide-gray-200"},ht={key:0},gt=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 1",-1),yt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ut={key:0,class:"text-green-600 font-medium"},ft={key:1,class:"text-gray-400"},pt={class:"px-6 py-4 whitespace-nowrap"},bt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},wt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},vt={key:1},kt=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 2",-1),Lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},qt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ct={key:0,class:"text-green-600 font-medium"},St={key:1,class:"text-gray-400"},Bt={class:"px-6 py-4 whitespace-nowrap"},Nt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},jt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},zt={key:2},It=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 3",-1),Dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Pt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},At={key:0,class:"text-green-600 font-medium"},Qt={key:1,class:"text-gray-400"},Vt={class:"px-6 py-4 whitespace-nowrap"},Mt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Et={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},Ht={key:3},Ot=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 4",-1),Ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ft={key:0,class:"text-green-600 font-medium"},Tt={key:1,class:"text-gray-400"},Rt={class:"px-6 py-4 whitespace-nowrap"},Zt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Gt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},Jt={key:0,class:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"},Kt={class:"flex items-center"},Wt=t("svg",{class:"w-5 h-5 text-green-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1),Xt=t("p",{class:"text-sm font-medium text-green-800"},"Order Confirmed",-1),Yt={class:"text-xs text-green-600"},te={key:0,class:"bg-white shadow rounded-lg p-6"},ee=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),se={class:"text-sm text-gray-700 whitespace-pre-wrap"},ae={class:"space-y-6"},oe={class:"bg-white shadow rounded-lg p-6"},le=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),de={class:"space-y-3 w-full"},ne=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),m(" Edit Lead ")],-1),ie=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),m(" Back to List ")],-1),ce={key:0,class:"bg-white shadow rounded-lg p-6"},re=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Documents",-1),xe={class:"space-y-3"},me={class:"flex items-center space-x-3"},he=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1),ge={class:"text-sm text-gray-700"},ye=["href"],_e={class:"bg-white shadow rounded-lg p-6"},ue=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),fe={class:"space-y-4"},pe={class:"flex items-start space-x-3"},be=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),we=t("p",{class:"text-sm font-semibold text-gray-900"},"leads Created",-1),ve={class:"text-xs text-gray-500"},ke={key:0,class:"flex items-start space-x-3"},Le=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),qe=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),Ce={class:"text-xs text-gray-500"},ze={__name:"Show",props:{leads:{type:Object,required:!0}},setup(e){const u=d=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",accepted:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",expired:"bg-yellow-100 text-yellow-800"})[d]||"bg-gray-100 text-gray-800",c=d=>d?new Date(d).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A";return(d,f)=>(s(),a(_,null,[n(w(v),{title:`Lead - ${e.leads.lead_number}`},null,8,["title"]),n(p,null,{default:i(()=>{var h,g,y;return[t("div",q,[t("div",C,[t("div",null,[t("h1",S,o(e.leads.lead_number),1),B]),t("div",N,[n(x,{href:d.route("leads.edit",e.leads.id)},{svg:i(()=>[n(b,{class:"w-full items-center"},{default:i(()=>[j,m(" Edit leads ")]),_:1})]),_:1},8,["href"])])]),t("div",z,[t("div",I,[t("div",D,[P,t("div",A,[t("div",null,[Q,t("p",V,o(e.leads.client_name),1)]),t("div",null,[M,t("p",E,o(((h=e.leads.county)==null?void 0:h.name)||"N/A"),1)]),t("div",null,[H,t("span",{class:k(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",u(e.leads.status)])},o(e.leads.status.charAt(0).toUpperCase()+e.leads.status.slice(1)),3)]),t("div",null,[O,t("p",U,o((g=e.leads.creator)==null?void 0:g.first_name)+" "+o((y=e.leads.creator)==null?void 0:y.last_name),1)])])]),t("div",$,[F,t("div",T,[t("div",null,[R,t("p",Z,o(e.leads.dimensions),1)]),t("div",null,[G,t("p",J,o(e.leads.open_size),1)]),t("div",null,[K,t("p",W,o(e.leads.box_style),1)]),t("div",null,[X,t("p",Y,o(e.leads.stock),1)]),t("div",null,[tt,t("p",et,o(e.leads.lamination),1)]),t("div",null,[st,t("p",at,o(e.leads.printing),1)]),e.leads.add_ons?(s(),a("div",ot,[lt,t("p",dt,o(e.leads.add_ons),1)])):l("",!0)])]),t("div",nt,[it,t("div",ct,[t("table",rt,[xt,t("tbody",mt,[e.leads.qty_1?(s(),a("tr",ht,[gt,t("td",yt,o(parseInt(e.leads.qty_1).toLocaleString())+" pcs",1),t("td",_t,[e.leads.confirmed_qty_1?(s(),a("span",ut,o(parseInt(e.leads.confirmed_qty_1).toLocaleString())+" pcs ",1)):(s(),a("span",ft,"Not confirmed"))]),t("td",pt,[e.leads.confirmed_qty_1?(s(),a("span",bt," ✓ Confirmed ")):(s(),a("span",wt," Pending "))])])):l("",!0),e.leads.qty_2?(s(),a("tr",vt,[kt,t("td",Lt,o(parseInt(e.leads.qty_2).toLocaleString())+" pcs",1),t("td",qt,[e.leads.confirmed_qty_2?(s(),a("span",Ct,o(parseInt(e.leads.confirmed_qty_2).toLocaleString())+" pcs ",1)):(s(),a("span",St,"Not confirmed"))]),t("td",Bt,[e.leads.confirmed_qty_2?(s(),a("span",Nt," ✓ Confirmed ")):(s(),a("span",jt," Pending "))])])):l("",!0),e.leads.qty_3?(s(),a("tr",zt,[It,t("td",Dt,o(parseInt(e.leads.qty_3).toLocaleString())+" pcs",1),t("td",Pt,[e.leads.confirmed_qty_3?(s(),a("span",At,o(parseInt(e.leads.confirmed_qty_3).toLocaleString())+" pcs ",1)):(s(),a("span",Qt,"Not confirmed"))]),t("td",Vt,[e.leads.confirmed_qty_3?(s(),a("span",Mt," ✓ Confirmed ")):(s(),a("span",Et," Pending "))])])):l("",!0),e.leads.qty_4?(s(),a("tr",Ht,[Ot,t("td",Ut,o(parseInt(e.leads.qty_4).toLocaleString())+" pcs",1),t("td",$t,[e.leads.confirmed_qty_4?(s(),a("span",Ft,o(parseInt(e.leads.confirmed_qty_4).toLocaleString())+" pcs ",1)):(s(),a("span",Tt,"Not confirmed"))]),t("td",Rt,[e.leads.confirmed_qty_4?(s(),a("span",Zt," ✓ Confirmed ")):(s(),a("span",Gt," Pending "))])])):l("",!0)])])]),e.leads.order_confirmed_at?(s(),a("div",Jt,[t("div",Kt,[Wt,t("div",null,[Xt,t("p",Yt,o(c(e.leads.order_confirmed_at)),1)])])])):l("",!0)]),e.leads.notes?(s(),a("div",te,[ee,t("p",se,o(e.leads.notes),1)])):l("",!0)]),t("div",ae,[t("div",oe,[le,t("div",de,[n(x,{href:d.route("leads.edit",e.leads.id),class:"w-full"},{svg:i(()=>[ne]),_:1},8,["href"]),n(x,{href:d.route("leads.index"),class:"w-full"},{svg:i(()=>[ie]),_:1},8,["href"])])]),e.leads.documents&&e.leads.documents.length>0?(s(),a("div",ce,[re,t("div",xe,[(s(!0),a(_,null,L(e.leads.documents,r=>(s(),a("div",{key:r.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",me,[he,t("span",ge,o(r.orignal_name),1)]),t("a",{href:"/uploads/leads/"+r.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,ye)]))),128))])])):l("",!0),t("div",_e,[ue,t("div",fe,[t("div",pe,[be,t("div",null,[we,t("p",ve,o(c(e.leads.created_at)),1)])]),e.leads.updated_at!==e.leads.created_at?(s(),a("div",ke,[Le,t("div",null,[qe,t("p",Ce,o(c(e.leads.updated_at)),1)])])):l("",!0)])])])])])]}),_:1})],64))}};export{ze as default};
