import{T as _,c as f,b as n,m as p,f as o,e as a,u as s,Z as g,d as h,i as y,g as t,k as r,n as v,x,q as b,p as k,l as V}from"./app-21b1097d.js";import{G as w}from"./GuestLayout-84627984.js";import{P as E}from"./PrimaryButton-c64acda1.js";import{_ as S}from"./_plugin-vue_export-helper-c27b6911.js";const c=e=>(k("data-v-e8e83c9d"),e=e(),V(),e),B=c(()=>t("h2",{class:"text-center text-xl font-semibold leading-9 tracking-tight text-indigo-600"},"Email Verification",-1)),I=c(()=>t("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1)),N={key:0,class:"mb-4 font-medium text-sm text-green-500"},C=["onSubmit"],L={class:"mt-4"},T={class:"text-center items-center mt-4"},G={__name:"VerifyEmail",props:{status:{type:String}},setup(e){const d=e,i=_({}),l=()=>{i.post(route("verification.send"))},m=f(()=>d.status==="verification-link-sent");return(u,P)=>(n(),p(w,null,{default:o(()=>[a(s(g),{title:"Email Verification"}),B,I,m.value?(n(),h("div",N," A new verification link has been sent to the email address you provided during registration. ")):y("",!0),t("form",{onSubmit:b(l,["prevent"])},[t("div",L,[a(E,{class:v({"opacity-25":s(i).processing}),disabled:s(i).processing},{default:o(()=>[r(" Resend Verification Email ")]),_:1},8,["class","disabled"])]),t("div",T,[a(s(x),{href:u.route("logout"),method:"post",as:"button",class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:o(()=>[r("Log Out")]),_:1},8,["href"])])],40,C)]),_:1}))}},M=S(G,[["__scopeId","data-v-e8e83c9d"]]);export{M as default};
