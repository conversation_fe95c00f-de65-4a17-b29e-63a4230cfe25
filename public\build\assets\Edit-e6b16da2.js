import{r as k,T as st,w as at,b as c,d as r,e as l,u as n,f as S,F as K,Z as dt,g as t,t as d,m as lt,k as R,i as u,q as ot,h as it,y as nt,j as ct}from"./app-df1bcebc.js";import{_ as rt,a as ut}from"./AdminLayout-e6738fa9.js";import{_ as p,a as g}from"./TextInput-ff2a6c73.js";import{_}from"./InputLabel-df28ac37.js";import{P as mt}from"./PrimaryButton-2296ddc6.js";import{_ as _t}from"./SecondaryButton-fc15966a.js";import{_ as yt}from"./TextArea-65f0749e.js";import{_ as Q}from"./Checkbox-a3885079.js";import"./_plugin-vue_export-helper-c27b6911.js";const gt={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},xt={class:"flex justify-between items-center mb-6"},vt={class:"text-2xl font-semibold leading-7 text-gray-900"},qt={class:"text-sm text-gray-600 mt-1"},ht={class:"flex space-x-3"},ft={key:1,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"},bt={class:"mb-8 p-4 bg-gray-50 rounded-lg"},pt=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Order Summary",-1),kt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},wt=t("p",{class:"text-sm font-medium text-gray-500"},"Quotation",-1),Vt={class:"text-sm text-blue-600"},At=t("p",{class:"text-sm font-medium text-gray-500"},"County",-1),St={class:"text-sm text-gray-900"},Qt=t("p",{class:"text-sm font-medium text-gray-500"},"Total Amount",-1),$t={class:"text-lg font-semibold text-green-600"},Ct=t("p",{class:"text-sm font-medium text-gray-500"},"Created By",-1),Ot={class:"text-sm text-gray-900"},Ut={class:"mb-8 p-4 bg-blue-50 rounded-lg"},Nt=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Selected Quantities",-1),Ft={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Pt={key:0,class:"bg-white p-3 rounded border"},Bt=t("p",{class:"text-sm font-medium text-gray-500"},"Quantity 1",-1),Dt={class:"text-lg font-semibold text-gray-900"},It={class:"text-sm text-gray-600"},Et={class:"text-sm font-medium text-green-600"},Lt={key:1,class:"bg-white p-3 rounded border"},jt=t("p",{class:"text-sm font-medium text-gray-500"},"Quantity 2",-1),zt={class:"text-lg font-semibold text-gray-900"},Mt={class:"text-sm text-gray-600"},Tt={class:"text-sm font-medium text-green-600"},Ht={key:2,class:"bg-white p-3 rounded border"},Zt=t("p",{class:"text-sm font-medium text-gray-500"},"Quantity 3",-1),Gt={class:"text-lg font-semibold text-gray-900"},Jt={class:"text-sm text-gray-600"},Kt={class:"text-sm font-medium text-green-600"},Rt={key:3,class:"bg-white p-3 rounded border"},Wt=t("p",{class:"text-sm font-medium text-gray-500"},"Quantity 4",-1),Xt={class:"text-lg font-semibold text-gray-900"},Yt={class:"text-sm text-gray-600"},te={class:"text-sm font-medium text-green-600"},ee=["onSubmit"],se={class:"border-b border-gray-900/10 pb-12"},ae={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},de=t("h3",{class:"text-lg font-medium text-gray-900"},"Edit Order Quantities",-1),le=t("p",{class:"text-sm text-gray-600 mb-6"},'Current order quantities are shown. Check/uncheck to add/remove quantities. Use "Select All" to see all available options:',-1),oe={class:"sm:col-span-12 mb-6"},ie=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Lead Information",-1),ne={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},ce=t("p",{class:"text-sm font-medium text-gray-500"},"Client Name",-1),re={class:"text-sm text-gray-900"},ue=t("p",{class:"text-sm font-medium text-gray-500"},"County",-1),me={class:"text-sm text-gray-900"},_e=t("p",{class:"text-sm font-medium text-gray-500"},"Dimensions",-1),ye={class:"text-sm text-gray-900"},ge=t("p",{class:"text-sm font-medium text-gray-500"},"Open Size",-1),xe={class:"text-sm text-gray-900"},ve=t("p",{class:"text-sm font-medium text-gray-500"},"Box Style",-1),qe={class:"text-sm text-gray-900"},he=t("p",{class:"text-sm font-medium text-gray-500"},"Stock",-1),fe={class:"text-sm text-gray-900"},be=t("p",{class:"text-sm font-medium text-gray-500"},"Lamination",-1),pe={class:"text-sm text-gray-900"},ke=t("p",{class:"text-sm font-medium text-gray-500"},"Printing",-1),we={class:"text-sm text-gray-900"},Ve={key:0},Ae=t("p",{class:"text-sm font-medium text-gray-500"},"Add-ons",-1),Se={class:"text-sm text-gray-900"},Qe=t("div",{class:"sm:col-span-12"},[t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Edit Order Quantities")],-1),$e={key:0,class:"sm:col-span-6"},Ce={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Oe={key:0,class:"grid grid-cols-1 gap-4"},Ue={class:"text-sm text-gray-500 mt-1"},Ne={key:1,class:"sm:col-span-6"},Fe={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Pe={key:0,class:"grid grid-cols-1 gap-4"},Be={class:"text-sm text-gray-500 mt-1"},De={key:2,class:"sm:col-span-6"},Ie={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},Ee={key:0,class:"grid grid-cols-1 gap-4"},Le={class:"text-sm text-gray-500 mt-1"},je={key:3,class:"sm:col-span-6"},ze={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Me={key:0,class:"grid grid-cols-1 gap-4"},Te={class:"text-sm text-gray-500 mt-1"},He={key:4,class:"sm:col-span-12"},Ze={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Ge={class:"flex items-center justify-between"},Je={class:"text-lg font-semibold text-green-800"},Ke=t("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing ",-1),Re=t("div",{class:"text-right"},[t("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),We={class:"sm:col-span-6 mt-8"},Xe=["value"],Ye={key:5,class:"sm:col-span-6"},ts=t("p",{class:"text-sm text-gray-500 mt-1"},"Add tracking number when order is shipped",-1),es={class:"sm:col-span-6"},ss={key:6,class:"sm:col-span-6"},as={class:"sm:col-span-12"},ds={class:"sm:col-span-12 mt-8"},ls=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Product Specifications",-1),os={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},is=t("p",{class:"text-sm font-medium text-gray-500"},"Dimensions",-1),ns={class:"text-sm text-gray-900"},cs=t("p",{class:"text-sm font-medium text-gray-500"},"Open Size",-1),rs={class:"text-sm text-gray-900"},us=t("p",{class:"text-sm font-medium text-gray-500"},"Box Style",-1),ms={class:"text-sm text-gray-900"},_s=t("p",{class:"text-sm font-medium text-gray-500"},"Stock",-1),ys={class:"text-sm text-gray-900"},gs=t("p",{class:"text-sm font-medium text-gray-500"},"Lamination",-1),xs={class:"text-sm text-gray-900"},vs=t("p",{class:"text-sm font-medium text-gray-500"},"Printing",-1),qs={class:"text-sm text-gray-900"},hs={key:0,class:"md:col-span-3"},fs=t("p",{class:"text-sm font-medium text-gray-500"},"Add-ons",-1),bs={class:"text-sm text-gray-900"},ps={class:"flex mt-6 items-center justify-between"},ks={class:"ml-auto flex items-center justify-end gap-x-6"},ws=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Fs={__name:"Edit",props:{data:{type:Object,required:!0}},setup(e){const i=e;k(!0);const q=k(!!i.data.selected_qty_1),h=k(!!i.data.selected_qty_2),f=k(!!i.data.selected_qty_3),b=k(!!i.data.selected_qty_4),$=k(0),s=st({selected_qty_1:i.data.selected_qty_1||"",selected_qty_2:i.data.selected_qty_2||"",selected_qty_3:i.data.selected_qty_3||"",selected_qty_4:i.data.selected_qty_4||"",status:i.data.status||"pending",tracking_number:i.data.tracking_number||"",expected_delivery:i.data.expected_delivery||"",actual_delivery:i.data.actual_delivery||"",notes:i.data.notes||""}),W=[{value:"pending",label:"Pending",color:"bg-yellow-100 text-yellow-800"},{value:"confirmed",label:"Confirmed",color:"bg-blue-100 text-blue-800"},{value:"under_production",label:"Under Production",color:"bg-purple-100 text-purple-800"},{value:"shipped",label:"Shipped",color:"bg-indigo-100 text-indigo-800"},{value:"delivered",label:"Delivered",color:"bg-green-100 text-green-800"}],X=()=>{s.put(route("orders.update",i.data.id),{preserveScroll:!0})},Y=()=>{confirm("Are you sure you want to confirm this order? This will update the lead with the selected quantities.")&&s.post(route("orders.confirm",i.data.id),{preserveScroll:!0})},y=m=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(m),w=()=>{var a,x,v,V;let m=0;q.value&&s.selected_qty_1&&((a=i.data.quotation)!=null&&a.price_qty_1)&&(m+=parseFloat(s.selected_qty_1)*parseFloat(i.data.quotation.price_qty_1)),h.value&&s.selected_qty_2&&((x=i.data.quotation)!=null&&x.price_qty_2)&&(m+=parseFloat(s.selected_qty_2)*parseFloat(i.data.quotation.price_qty_2)),f.value&&s.selected_qty_3&&((v=i.data.quotation)!=null&&v.price_qty_3)&&(m+=parseFloat(s.selected_qty_3)*parseFloat(i.data.quotation.price_qty_3)),b.value&&s.selected_qty_4&&((V=i.data.quotation)!=null&&V.price_qty_4)&&(m+=parseFloat(s.selected_qty_4)*parseFloat(i.data.quotation.price_qty_4)),$.value=m},A=(m,a)=>{a||(s[`selected_qty_${m}`]=""),w()},tt=()=>{var m,a,x,v;(m=i.data.quotation)!=null&&m.qty_1&&(q.value=!0,s.selected_qty_1=i.data.quotation.qty_1),(a=i.data.quotation)!=null&&a.qty_2&&(h.value=!0,s.selected_qty_2=i.data.quotation.qty_2),(x=i.data.quotation)!=null&&x.qty_3&&(f.value=!0,s.selected_qty_3=i.data.quotation.qty_3),(v=i.data.quotation)!=null&&v.qty_4&&(b.value=!0,s.selected_qty_4=i.data.quotation.qty_4),w()},et=()=>{q.value=!1,h.value=!1,f.value=!1,b.value=!1,s.selected_qty_1="",s.selected_qty_2="",s.selected_qty_3="",s.selected_qty_4="",w()};return at([()=>s.selected_qty_1,()=>q.value,()=>s.selected_qty_2,()=>h.value,()=>s.selected_qty_3,()=>f.value,()=>s.selected_qty_4,()=>b.value],w),w(),(m,a)=>(c(),r(K,null,[l(n(dt),{title:"Edit Order"}),l(rt,null,{default:S(()=>{var x,v,V,C,O,U,N,F,P,B,D,I,E,L,j,z,M,T,H,Z,G,J;return[t("div",gt,[t("div",xt,[t("div",null,[t("h2",vt," Edit Order - "+d(e.data.order_number),1),t("p",qt,"Client: "+d(e.data.client_name),1)]),t("div",ht,[!e.data.is_confirmed&&e.data.status==="pending"?(c(),lt(_t,{key:0,onClick:Y,class:"bg-green-600 text-white hover:bg-green-700"},{default:S(()=>[R(" Confirm Order ")]),_:1})):u("",!0),e.data.is_confirmed?(c(),r("span",ft," ✓ Confirmed ")):u("",!0)])]),t("div",bt,[pt,t("div",kt,[t("div",null,[wt,t("p",Vt,d(((x=e.data.quotation)==null?void 0:x.quotation_number)||"N/A"),1)]),t("div",null,[At,t("p",St,d(((v=e.data.county)==null?void 0:v.name)||"N/A"),1)]),t("div",null,[Qt,t("p",$t,d(y(e.data.total_amount)),1)]),t("div",null,[Ct,t("p",Ot,d((V=e.data.creator)==null?void 0:V.first_name)+" "+d((C=e.data.creator)==null?void 0:C.last_name),1)])])]),t("div",Ut,[Nt,t("div",Ft,[e.data.selected_qty_1?(c(),r("div",Pt,[Bt,t("p",Dt,d(parseInt(e.data.selected_qty_1).toLocaleString())+" pcs",1),t("p",It,d(y(e.data.price_qty_1))+" each",1),t("p",Et,d(y(e.data.selected_qty_1*e.data.price_qty_1)),1)])):u("",!0),e.data.selected_qty_2?(c(),r("div",Lt,[jt,t("p",zt,d(parseInt(e.data.selected_qty_2).toLocaleString())+" pcs",1),t("p",Mt,d(y(e.data.price_qty_2))+" each",1),t("p",Tt,d(y(e.data.selected_qty_2*e.data.price_qty_2)),1)])):u("",!0),e.data.selected_qty_3?(c(),r("div",Ht,[Zt,t("p",Gt,d(parseInt(e.data.selected_qty_3).toLocaleString())+" pcs",1),t("p",Jt,d(y(e.data.price_qty_3))+" each",1),t("p",Kt,d(y(e.data.selected_qty_3*e.data.price_qty_3)),1)])):u("",!0),e.data.selected_qty_4?(c(),r("div",Rt,[Wt,t("p",Xt,d(parseInt(e.data.selected_qty_4).toLocaleString())+" pcs",1),t("p",Yt,d(y(e.data.price_qty_4))+" each",1),t("p",te,d(y(e.data.selected_qty_4*e.data.price_qty_4)),1)])):u("",!0)])]),t("form",{onSubmit:ot(X,["prevent"])},[t("div",se,[t("div",ae,[t("div",{class:"sm:col-span-12 mt-8"},[t("div",{class:"flex items-center justify-between mb-4"},[de,t("div",{class:"flex space-x-2"},[t("button",{type:"button",onClick:tt,class:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"}," Select All Available "),t("button",{type:"button",onClick:et,class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"}," Clear All ")])]),le]),t("div",oe,[ie,t("div",ne,[t("div",null,[ce,t("p",re,d(((O=e.data.lead)==null?void 0:O.client_name)||"N/A"),1)]),t("div",null,[ue,t("p",me,d(((N=(U=e.data.lead)==null?void 0:U.county)==null?void 0:N.name)||"N/A"),1)]),t("div",null,[_e,t("p",ye,d(((F=e.data.lead)==null?void 0:F.dimensions)||"N/A"),1)]),t("div",null,[ge,t("p",xe,d(((P=e.data.lead)==null?void 0:P.open_size)||"N/A"),1)]),t("div",null,[ve,t("p",qe,d(((B=e.data.lead)==null?void 0:B.box_style)||"N/A"),1)]),t("div",null,[he,t("p",fe,d(((D=e.data.lead)==null?void 0:D.stock)||"N/A"),1)]),t("div",null,[be,t("p",pe,d(((I=e.data.lead)==null?void 0:I.lamination)||"N/A"),1)]),t("div",null,[ke,t("p",we,d(((E=e.data.lead)==null?void 0:E.printing)||"N/A"),1)]),(L=e.data.lead)!=null&&L.add_ons?(c(),r("div",Ve,[Ae,t("p",Se,d(e.data.lead.add_ons),1)])):u("",!0)])]),Qe,(j=e.data.quotation)!=null&&j.qty_1&&((z=e.data.quotation)!=null&&z.price_qty_1)?(c(),r("div",$e,[t("div",Ce,[l(Q,{checked:q.value,"onUpdate:checked":[a[0]||(a[0]=o=>q.value=o),a[1]||(a[1]=o=>A(1,o))]},null,8,["checked"]),l(_,{value:"Include Quantity 1",class:"text-base font-medium text-blue-800"})]),q.value?(c(),r("div",Oe,[t("div",null,[l(_,{for:"selected_qty_1",value:`Order Qty 1 (Available: ${e.data.quotation.qty_1})`},null,8,["value"]),l(p,{id:"selected_qty_1",type:"number",modelValue:n(s).selected_qty_1,"onUpdate:modelValue":a[2]||(a[2]=o=>n(s).selected_qty_1=o),max:e.data.quotation.qty_1,min:"1",placeholder:`Max: ${e.data.quotation.qty_1}`},null,8,["modelValue","max","placeholder"]),l(g,{message:n(s).errors.selected_qty_1},null,8,["message"]),t("p",Ue,"Price: $"+d(e.data.quotation.price_qty_1)+" per unit",1)])])):u("",!0)])):u("",!0),(M=e.data.quotation)!=null&&M.qty_2&&((T=e.data.quotation)!=null&&T.price_qty_2)?(c(),r("div",Ne,[t("div",Fe,[l(Q,{checked:h.value,"onUpdate:checked":[a[3]||(a[3]=o=>h.value=o),a[4]||(a[4]=o=>A(2,o))]},null,8,["checked"]),l(_,{value:"Include Quantity 2",class:"text-base font-medium text-green-800"})]),h.value?(c(),r("div",Pe,[t("div",null,[l(_,{for:"selected_qty_2",value:`Order Qty 2 (Available: ${e.data.quotation.qty_2})`},null,8,["value"]),l(p,{id:"selected_qty_2",type:"number",modelValue:n(s).selected_qty_2,"onUpdate:modelValue":a[5]||(a[5]=o=>n(s).selected_qty_2=o),max:e.data.quotation.qty_2,min:"1",placeholder:`Max: ${e.data.quotation.qty_2}`},null,8,["modelValue","max","placeholder"]),l(g,{message:n(s).errors.selected_qty_2},null,8,["message"]),t("p",Be,"Price: $"+d(e.data.quotation.price_qty_2)+" per unit",1)])])):u("",!0)])):u("",!0),(H=e.data.quotation)!=null&&H.qty_3&&((Z=e.data.quotation)!=null&&Z.price_qty_3)?(c(),r("div",De,[t("div",Ie,[l(Q,{checked:f.value,"onUpdate:checked":[a[6]||(a[6]=o=>f.value=o),a[7]||(a[7]=o=>A(3,o))]},null,8,["checked"]),l(_,{value:"Include Quantity 3",class:"text-base font-medium text-yellow-800"})]),f.value?(c(),r("div",Ee,[t("div",null,[l(_,{for:"selected_qty_3",value:`Order Qty 3 (Available: ${e.data.quotation.qty_3})`},null,8,["value"]),l(p,{id:"selected_qty_3",type:"number",modelValue:n(s).selected_qty_3,"onUpdate:modelValue":a[8]||(a[8]=o=>n(s).selected_qty_3=o),max:e.data.quotation.qty_3,min:"1",placeholder:`Max: ${e.data.quotation.qty_3}`},null,8,["modelValue","max","placeholder"]),l(g,{message:n(s).errors.selected_qty_3},null,8,["message"]),t("p",Le,"Price: $"+d(e.data.quotation.price_qty_3)+" per unit",1)])])):u("",!0)])):u("",!0),(G=e.data.quotation)!=null&&G.qty_4&&((J=e.data.quotation)!=null&&J.price_qty_4)?(c(),r("div",je,[t("div",ze,[l(Q,{checked:b.value,"onUpdate:checked":[a[9]||(a[9]=o=>b.value=o),a[10]||(a[10]=o=>A(4,o))]},null,8,["checked"]),l(_,{value:"Include Quantity 4",class:"text-base font-medium text-purple-800"})]),b.value?(c(),r("div",Me,[t("div",null,[l(_,{for:"selected_qty_4",value:`Order Qty 4 (Available: ${e.data.quotation.qty_4})`},null,8,["value"]),l(p,{id:"selected_qty_4",type:"number",modelValue:n(s).selected_qty_4,"onUpdate:modelValue":a[11]||(a[11]=o=>n(s).selected_qty_4=o),max:e.data.quotation.qty_4,min:"1",placeholder:`Max: ${e.data.quotation.qty_4}`},null,8,["modelValue","max","placeholder"]),l(g,{message:n(s).errors.selected_qty_4},null,8,["message"]),t("p",Te,"Price: $"+d(e.data.quotation.price_qty_4)+" per unit",1)])])):u("",!0)])):u("",!0),$.value>0?(c(),r("div",He,[t("div",Ze,[t("div",Ge,[t("div",null,[t("p",Je," Updated Order Total: $"+d($.value.toFixed(2)),1),Ke]),Re])])])):u("",!0),t("div",We,[l(_,{for:"status",value:"Order Status *"}),it(t("select",{id:"status","onUpdate:modelValue":a[12]||(a[12]=o=>n(s).status=o),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[(c(),r(K,null,ct(W,o=>t("option",{key:o.value,value:o.value},d(o.label),9,Xe)),64))],512),[[nt,n(s).status]]),l(g,{message:n(s).errors.status},null,8,["message"])]),n(s).status==="shipped"||e.data.tracking_number?(c(),r("div",Ye,[l(_,{for:"tracking_number",value:"Tracking Number"}),l(p,{id:"tracking_number",type:"text",modelValue:n(s).tracking_number,"onUpdate:modelValue":a[13]||(a[13]=o=>n(s).tracking_number=o),placeholder:"Enter tracking number"},null,8,["modelValue"]),l(g,{message:n(s).errors.tracking_number},null,8,["message"]),ts])):u("",!0),t("div",es,[l(_,{for:"expected_delivery",value:"Expected Delivery Date"}),l(p,{id:"expected_delivery",type:"date",modelValue:n(s).expected_delivery,"onUpdate:modelValue":a[14]||(a[14]=o=>n(s).expected_delivery=o)},null,8,["modelValue"]),l(g,{message:n(s).errors.expected_delivery},null,8,["message"])]),n(s).status==="delivered"||e.data.actual_delivery?(c(),r("div",ss,[l(_,{for:"actual_delivery",value:"Actual Delivery Date"}),l(p,{id:"actual_delivery",type:"date",modelValue:n(s).actual_delivery,"onUpdate:modelValue":a[15]||(a[15]=o=>n(s).actual_delivery=o)},null,8,["modelValue"]),l(g,{message:n(s).errors.actual_delivery},null,8,["message"])])):u("",!0),t("div",as,[l(_,{for:"notes",value:"Order Notes"}),l(yt,{id:"notes",modelValue:n(s).notes,"onUpdate:modelValue":a[16]||(a[16]=o=>n(s).notes=o),rows:"4",placeholder:"Add any notes about this order..."},null,8,["modelValue"]),l(g,{message:n(s).errors.notes},null,8,["message"])]),t("div",ds,[ls,t("div",os,[t("div",null,[is,t("p",ns,d(e.data.dimensions),1)]),t("div",null,[cs,t("p",rs,d(e.data.open_size),1)]),t("div",null,[us,t("p",ms,d(e.data.box_style),1)]),t("div",null,[_s,t("p",ys,d(e.data.stock),1)]),t("div",null,[gs,t("p",xs,d(e.data.lamination),1)]),t("div",null,[vs,t("p",qs,d(e.data.printing),1)]),e.data.add_ons?(c(),r("div",hs,[fs,t("p",bs,d(e.data.add_ons),1)])):u("",!0)])])])]),t("div",ps,[t("div",ks,[l(ut,{href:m.route("orders.index")},{svg:S(()=>[ws]),_:1},8,["href"]),l(mt,{disabled:n(s).processing},{default:S(()=>[R(" Update Order ")]),_:1},8,["disabled"])])])],40,ee)])]}),_:1})],64))}};export{Fs as default};
