import{T as j,r as p,w as L,b as u,d as _,e as o,u as d,f as C,F as Z,Z as G,g as e,k as I,t as n,i as m,q as J}from"./app-d08c0398.js";import{_ as K,a as R}from"./AdminLayout-3fd3a608.js";import{_ as b,a as y}from"./TextInput-3a644ba4.js";import{_ as r}from"./InputLabel-326caffb.js";import{P as W}from"./PrimaryButton-b2bd7aa7.js";import{_ as X}from"./TextArea-40ffbaa5.js";import{_ as k}from"./Checkbox-88b93dfb.js";import"./_plugin-vue_export-helper-c27b6911.js";const Y={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ee=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"}," Convert Quotation to Order ",-1),te={class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},se={class:"text-sm text-blue-800"},le=e("strong",null,"Converting from Quotation:",-1),oe=e("div",{class:"sm:col-span-12 mt-6"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information")],-1),ae={class:"mt-6 p-4 bg-gray-50 rounded-lg"},de={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ie=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1),ne={class:"text-sm text-gray-700"},ce=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1),re={class:"text-sm text-gray-700"},ue=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1),_e={class:"text-sm text-gray-700"},me=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1),ye={class:"text-sm text-gray-700"},qe=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1),xe={class:"text-sm text-gray-700"},ge=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1),ve={class:"text-sm text-gray-700"},pe=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1),be={class:"text-sm text-gray-700"},fe=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1),he={class:"text-sm text-gray-700"},ke={key:0},Ve=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1),Ce={class:"text-sm text-gray-700"},we=["onSubmit"],Qe={class:"border-b border-gray-900/10 pb-12"},Ae={class:"mt-6 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ne=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4"},"Select Quantities for Order"),e("p",{class:"text-sm text-gray-600"},"Select the quantities that the client needs. Check the boxes and enter the required amounts:")],-1),$e={key:0,class:"sm:col-span-3"},Fe={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Oe={class:"grid grid-cols-1 gap-4"},Se={class:"text-sm text-gray-500 mt-1"},De={key:1,class:"sm:col-span-3"},Ue={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Be={class:"grid grid-cols-1 gap-4"},Pe={class:"text-sm text-gray-500 mt-1"},Me={key:2,class:"sm:col-span-3"},ze={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},Te={class:"grid grid-cols-1 gap-4"},je={class:"text-sm text-gray-500 mt-1"},Ie={key:3,class:"sm:col-span-3"},Ee={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},He={class:"grid grid-cols-1 gap-4"},Le={class:"text-sm text-gray-500 mt-1"},Ze={key:4,class:"sm:col-span-12"},Ge={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Je={class:"flex items-center justify-between"},Ke={class:"text-lg font-semibold text-green-800"},Re=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing from quotation ",-1),We=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),Xe=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4 mb-4"},"Order Information")],-1),Ye={class:"sm:col-span-6"},et={class:"sm:col-span-12"},tt={class:"flex mt-6 items-center justify-between"},st={class:"ml-auto flex items-center justify-end gap-x-6"},lt=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),_t={__name:"Add",props:{quotation:{type:Object,required:!0}},setup(s){var Q,A,N,$;const c=s,t=j({quotation_id:c.quotation.id,lead_id:c.quotation.lead_id,selected_qty_1:((Q=c.quotation.lead)==null?void 0:Q.qty_1)||"",selected_qty_2:((A=c.quotation.lead)==null?void 0:A.qty_2)||"",selected_qty_3:((N=c.quotation.lead)==null?void 0:N.qty_3)||"",selected_qty_4:(($=c.quotation.lead)==null?void 0:$.qty_4)||"",notes:"",expected_delivery:"",tracking_number:""}),q=p(!1),x=p(!1),g=p(!1),v=p(!1),f=p(0),E=()=>{const i={quotation_id:t.quotation_id,lead_id:t.lead_id,notes:t.notes,expected_delivery:t.expected_delivery,tracking_number:t.tracking_number,selected_qty_1:null,selected_qty_2:null,selected_qty_3:null,selected_qty_4:null};return q.value&&t.selected_qty_1&&(i.selected_qty_1=t.selected_qty_1),x.value&&t.selected_qty_2&&(i.selected_qty_2=t.selected_qty_2),g.value&&t.selected_qty_3&&(i.selected_qty_3=t.selected_qty_3),v.value&&t.selected_qty_4&&(i.selected_qty_4=t.selected_qty_4),i},H=()=>{const i=E();j(i).post(route("orders.store"),{preserveScroll:!0,onSuccess:()=>t.reset()})},w=()=>{let i=0;q.value&&t.selected_qty_1&&c.quotation.price_qty_1&&(i+=parseFloat(t.selected_qty_1)*parseFloat(c.quotation.price_qty_1)),x.value&&t.selected_qty_2&&c.quotation.price_qty_2&&(i+=parseFloat(t.selected_qty_2)*parseFloat(c.quotation.price_qty_2)),g.value&&t.selected_qty_3&&c.quotation.price_qty_3&&(i+=parseFloat(t.selected_qty_3)*parseFloat(c.quotation.price_qty_3)),v.value&&t.selected_qty_4&&c.quotation.price_qty_4&&(i+=parseFloat(t.selected_qty_4)*parseFloat(c.quotation.price_qty_4)),f.value=i},h=(i,l)=>{w()};L([()=>t.selected_qty_1,()=>q.value,()=>t.selected_qty_2,()=>x.value,()=>t.selected_qty_3,()=>g.value,()=>t.selected_qty_4,()=>v.value],w);const V=new Date;return V.setDate(V.getDate()+7),t.expected_delivery=V.toISOString().split("T")[0],(i,l)=>(u(),_(Z,null,[o(d(G),{title:"Convert Quotation to Order"}),o(K,null,{default:C(()=>{var F,O,S,D,U,B,P,M,z,T;return[e("div",Y,[ee,e("div",te,[e("p",se,[le,I(" "+n(s.quotation.quotation_number),1)])]),oe,e("div",ae,[e("div",de,[e("div",null,[ie,e("p",ne,n(((F=s.quotation.lead)==null?void 0:F.client_name)||"N/A"),1)]),e("div",null,[ce,e("p",re,n(((S=(O=s.quotation.lead)==null?void 0:O.county)==null?void 0:S.name)||"N/A"),1)]),e("div",null,[ue,e("p",_e,n(((D=s.quotation.lead)==null?void 0:D.dimensions)||"N/A"),1)]),e("div",null,[me,e("p",ye,n(((U=s.quotation.lead)==null?void 0:U.open_size)||"N/A"),1)]),e("div",null,[qe,e("p",xe,n(((B=s.quotation.lead)==null?void 0:B.box_style)||"N/A"),1)]),e("div",null,[ge,e("p",ve,n(((P=s.quotation.lead)==null?void 0:P.stock)||"N/A"),1)]),e("div",null,[pe,e("p",be,n(((M=s.quotation.lead)==null?void 0:M.lamination)||"N/A"),1)]),e("div",null,[fe,e("p",he,n(((z=s.quotation.lead)==null?void 0:z.printing)||"N/A"),1)]),(T=s.quotation.lead)!=null&&T.add_ons?(u(),_("div",ke,[Ve,e("p",Ce,n(s.quotation.lead.add_ons),1)])):m("",!0)])]),e("form",{onSubmit:J(H,["prevent"])},[e("div",Qe,[e("div",Ae,[Ne,s.quotation.lead.qty_1&&s.quotation.price_qty_1?(u(),_("div",$e,[e("div",Fe,[o(k,{checked:q.value,"onUpdate:checked":[l[0]||(l[0]=a=>q.value=a),l[1]||(l[1]=a=>h(1,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",Oe,[e("div",null,[o(r,{for:"selected_qty_1",value:`Client Order Qty 1 (Available: ${s.quotation.lead.qty_1})`},null,8,["value"]),o(b,{id:"selected_qty_1",type:"number",modelValue:d(t).selected_qty_1,"onUpdate:modelValue":l[2]||(l[2]=a=>d(t).selected_qty_1=a),max:s.quotation.lead.qty_1,min:"1",placeholder:`Max: ${s.quotation.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_1},null,8,["message"]),e("p",Se,"Price: £"+n(s.quotation.price_qty_1)+" per unit",1)])])])):m("",!0),s.quotation.lead.qty_2&&s.quotation.price_qty_2?(u(),_("div",De,[e("div",Ue,[o(k,{checked:x.value,"onUpdate:checked":[l[3]||(l[3]=a=>x.value=a),l[4]||(l[4]=a=>h(2,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",Be,[e("div",null,[o(r,{for:"selected_qty_2",value:`Client Order Qty 2 (Available: ${s.quotation.lead.qty_2})`},null,8,["value"]),o(b,{id:"selected_qty_2",type:"number",modelValue:d(t).selected_qty_2,"onUpdate:modelValue":l[5]||(l[5]=a=>d(t).selected_qty_2=a),max:s.quotation.lead.qty_2,min:"1",placeholder:`Max: ${s.quotation.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_2},null,8,["message"]),e("p",Pe,"Price: £"+n(s.quotation.price_qty_2)+" per unit",1)])])])):m("",!0),s.quotation.lead.qty_3&&s.quotation.price_qty_3?(u(),_("div",Me,[e("div",ze,[o(k,{checked:g.value,"onUpdate:checked":[l[6]||(l[6]=a=>g.value=a),l[7]||(l[7]=a=>h(3,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",Te,[e("div",null,[o(r,{for:"selected_qty_3",value:`Client Order Qty 3 (Available: ${s.quotation.lead.qty_3})`},null,8,["value"]),o(b,{id:"selected_qty_3",type:"number",modelValue:d(t).selected_qty_3,"onUpdate:modelValue":l[8]||(l[8]=a=>d(t).selected_qty_3=a),max:s.quotation.lead.qty_3,min:"1",placeholder:`Max: ${s.quotation.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_3},null,8,["message"]),e("p",je,"Price: £"+n(s.quotation.price_qty_3)+" per unit",1)])])])):m("",!0),s.quotation.lead.qty_4&&s.quotation.price_qty_4?(u(),_("div",Ie,[e("div",Ee,[o(k,{checked:v.value,"onUpdate:checked":[l[9]||(l[9]=a=>v.value=a),l[10]||(l[10]=a=>h(4,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",He,[e("div",null,[o(r,{for:"selected_qty_4",value:`Client Order Qty 4 (Available: ${s.quotation.lead.qty_4})`},null,8,["value"]),o(b,{id:"selected_qty_4",type:"number",modelValue:d(t).selected_qty_4,"onUpdate:modelValue":l[11]||(l[11]=a=>d(t).selected_qty_4=a),max:s.quotation.lead.qty_4,min:"1",placeholder:`Max: ${s.quotation.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_4},null,8,["message"]),e("p",Le,"Price: £"+n(s.quotation.price_qty_4)+" per unit",1)])])])):m("",!0),f.value>0?(u(),_("div",Ze,[e("div",Ge,[e("div",Je,[e("div",null,[e("p",Ke," Order Total: £"+n(f.value.toFixed(2)),1),Re]),We])])])):m("",!0),Xe,e("div",Ye,[o(r,{for:"expected_delivery",value:"Expected Delivery Date"}),o(b,{id:"expected_delivery",type:"date",modelValue:d(t).expected_delivery,"onUpdate:modelValue":l[12]||(l[12]=a=>d(t).expected_delivery=a)},null,8,["modelValue"]),o(y,{message:d(t).errors.expected_delivery},null,8,["message"])]),e("div",et,[o(r,{for:"notes",value:"Order Notes"}),o(X,{id:"notes",modelValue:d(t).notes,"onUpdate:modelValue":l[13]||(l[13]=a=>d(t).notes=a),rows:"4",placeholder:"Any special instructions or notes for this order..."},null,8,["modelValue"]),o(y,{message:d(t).errors.notes},null,8,["message"])])])]),e("div",tt,[e("div",st,[o(R,{href:i.route("quotations.show",s.quotation.id)},{svg:C(()=>[lt]),_:1},8,["href"]),o(W,{disabled:d(t).processing||f.value===0},{default:C(()=>[I(" Save ")]),_:1},8,["disabled"])])])],40,we)])]}),_:1})],64))}};export{_t as default};
