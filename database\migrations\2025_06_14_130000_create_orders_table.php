<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->string('client_name');
            $table->foreignId('county_id')->constrained('counties');
            $table->foreignId('quotation_id')->constrained('quotations');
            $table->string('dimensions');
            $table->string('open_size');
            $table->string('box_style');
            $table->string('stock');
            $table->string('lamination');
            $table->string('printing');
            $table->string('add_ons')->nullable();

            // Selected quantities from quotation
            $table->integer('selected_qty_1')->nullable();
            $table->integer('selected_qty_2')->nullable();
            $table->integer('selected_qty_3')->nullable();
            $table->integer('selected_qty_4')->nullable();

            // Prices from quotation
            $table->decimal('price_qty_1', 10, 2)->nullable();
            $table->decimal('price_qty_2', 10, 2)->nullable();
            $table->decimal('price_qty_3', 10, 2)->nullable();
            $table->decimal('price_qty_4', 10, 2)->nullable();

            $table->text('notes')->nullable();
            $table->enum('status', ['pending', 'confirmed', 'under_production', 'shipped', 'delivered'])->default('pending');
            $table->boolean('is_confirmed')->default(false);
            $table->timestamp('confirmed_at')->nullable();
            $table->string('tracking_number')->nullable();
            $table->date('expected_delivery')->nullable();
            $table->date('actual_delivery')->nullable();
            $table->decimal('total_amount', 12, 2)->default(0);

            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
