import{_ as J,a as v}from"./AdminLayout-bed0e8e1.js";import{P as R}from"./PrimaryButton-5df40e25.js";import{b as o,d as a,e as d,u as W,f as x,F as T,Z as X,h as t,t as s,l as w,j as n,n as Y,k as tt}from"./app-e1dc0e85.js";import"./_plugin-vue_export-helper-c27b6911.js";const et={class:"animate-top"},st={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},ot={class:"text-3xl font-bold text-gray-900"},at=t("p",{class:"text-gray-600 mt-1"},"Quotation Details",-1),nt={class:"flex flex-col sm:flex-row gap-3"},it=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),lt={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},dt={class:"lg:col-span-2 space-y-8"},ct={class:"bg-white shadow rounded-lg p-6"},rt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Lead Information",-1),ut={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},mt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),xt={class:"mt-1 text-sm text-gray-700"},yt=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),ht={class:"mt-1 text-sm text-gray-700"},gt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Number",-1),_t={class:"mt-1 text-sm text-gray-700"},bt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Status",-1),ft={class:"mt-1 text-sm text-gray-700"},wt={class:"bg-white shadow rounded-lg p-6"},qt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),vt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},kt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),pt={class:"mt-1 text-sm text-gray-700"},At=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),Lt={class:"mt-1 text-sm text-gray-700"},Ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),Nt={class:"mt-1 text-sm text-gray-700"},St=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),Dt={class:"mt-1 text-sm text-gray-700"},Bt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),jt={class:"mt-1 text-sm text-gray-700"},Ut=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),Vt={class:"mt-1 text-sm text-gray-700"},Pt={key:0,class:"md:col-span-2"},zt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),Mt={class:"mt-1 text-sm text-gray-700"},Qt={class:"bg-white shadow rounded-lg p-6"},It=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quotation Information",-1),Ft={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Kt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),$t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Valid Until",-1),Et={class:"mt-1 text-sm text-gray-700"},Ht=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),Ot={class:"mt-1 text-sm text-gray-700"},Tt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created Date",-1),Gt={class:"mt-1 text-sm text-gray-700"},Zt={class:"bg-white shadow rounded-lg p-6"},Jt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Pricing Details",-1),Rt={class:"overflow-x-auto"},Wt={class:"min-w-full divide-y divide-gray-200"},Xt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Unit Price"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Total")])],-1),Yt={class:"bg-white divide-y divide-gray-200"},te={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ee={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},se={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},oe={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ae={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ne={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},ie={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},le={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},de={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},ce={key:0,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},re={key:1,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ue={key:2,class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},me={key:0,class:"bg-white shadow rounded-lg p-6"},xe=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),ye={class:"text-sm text-gray-700 whitespace-pre-wrap"},he={class:"space-y-6"},ge={class:"bg-white shadow rounded-lg p-6"},_e=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),be={class:"space-y-3 w-full"},fe=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),w(" Edit Quotation ")],-1),we=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),qe=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),w(" Back to List ")],-1),ve={key:0,class:"bg-white shadow rounded-lg p-6"},ke=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Documents",-1),pe={class:"space-y-3"},Ae={class:"flex items-center space-x-3"},Le=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1),Ce={class:"text-sm text-gray-700"},Ne=["href"],Se={class:"bg-white shadow rounded-lg p-6"},De=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),Be={class:"space-y-4"},je={class:"flex items-start space-x-3"},Ue=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),Ve=t("p",{class:"text-sm font-semibold text-gray-900"},"Quotation Created",-1),Pe={class:"text-xs text-gray-500"},ze={key:0,class:"flex items-start space-x-3"},Me=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),Qe=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),Ie={class:"text-xs text-gray-500"},He={__name:"Show",props:{quotation:{type:Object,required:!0}},setup(e){const k=e,G=i=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",accepted:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",expired:"bg-yellow-100 text-yellow-800"})[i]||"bg-gray-100 text-gray-800",Z=()=>{window.open(route("quotations.pdf",k.quotation.id),"_blank")},y=i=>i?new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",l=i=>{var u,m;const c={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},h=((m=(u=k.quotation.lead)==null?void 0:u.county)==null?void 0:m.name)||"UK",g=Object.keys(c).find(f=>h.toLowerCase().includes(f.toLowerCase())),{locale:_,currency:r}=c[g]||c.UK,b=new Intl.NumberFormat(_,{style:"currency",currency:r,currencyDisplay:"symbol"}).format(i);return`${r} ${b}`};return(i,c)=>(o(),a(T,null,[d(W(X),{title:"Quotations"}),d(J,null,{default:x(()=>{var h,g,_,r,b,u,m,f,p,A,L,C,N,S,D,B,j,U,V,P,z,M,Q,I,F,K,$,E,H,O;return[t("div",et,[t("div",st,[t("div",null,[t("h1",ot,s(e.quotation.quotation_number),1),at]),t("div",nt,[d(v,{href:i.route("quotations.edit",e.quotation.id)},{svg:x(()=>[d(R,{class:"w-full items-center"},{default:x(()=>[it,w(" Edit Quotation ")]),_:1})]),_:1},8,["href"])])]),t("div",lt,[t("div",dt,[t("div",ct,[rt,t("div",ut,[t("div",null,[mt,t("p",xt,s(((h=e.quotation.lead)==null?void 0:h.client_name)||"N/A"),1)]),t("div",null,[yt,t("p",ht,s(((_=(g=e.quotation.lead)==null?void 0:g.county)==null?void 0:_.name)||"N/A"),1)]),t("div",null,[gt,t("p",_t,s(((r=e.quotation.lead)==null?void 0:r.lead_number)||"N/A"),1)]),t("div",null,[bt,t("p",ft,s(((b=e.quotation.lead)==null?void 0:b.status)||"N/A"),1)])])]),t("div",wt,[qt,t("div",vt,[t("div",null,[kt,t("p",pt,s(((u=e.quotation.lead)==null?void 0:u.dimensions)||"N/A"),1)]),t("div",null,[At,t("p",Lt,s(((m=e.quotation.lead)==null?void 0:m.open_size)||"N/A"),1)]),t("div",null,[Ct,t("p",Nt,s(((f=e.quotation.lead)==null?void 0:f.box_style)||"N/A"),1)]),t("div",null,[St,t("p",Dt,s(((p=e.quotation.lead)==null?void 0:p.stock)||"N/A"),1)]),t("div",null,[Bt,t("p",jt,s(((A=e.quotation.lead)==null?void 0:A.lamination)||"N/A"),1)]),t("div",null,[Ut,t("p",Vt,s(((L=e.quotation.lead)==null?void 0:L.printing)||"N/A"),1)]),(C=e.quotation.lead)!=null&&C.add_ons?(o(),a("div",Pt,[zt,t("p",Mt,s(e.quotation.lead.add_ons),1)])):n("",!0)])]),t("div",Qt,[It,t("div",Ft,[t("div",null,[Kt,t("span",{class:Y(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",G(e.quotation.status)])},s(e.quotation.status.charAt(0).toUpperCase()+e.quotation.status.slice(1)),3)]),t("div",null,[$t,t("p",Et,s(y(e.quotation.valid_until)),1)]),t("div",null,[Ht,t("p",Ot,s((N=e.quotation.creator)==null?void 0:N.first_name)+" "+s((S=e.quotation.creator)==null?void 0:S.last_name),1)]),t("div",null,[Tt,t("p",Gt,s(y(e.quotation.created_at)),1)])])]),t("div",Zt,[Jt,t("div",Rt,[t("table",Wt,[Xt,t("tbody",Yt,[t("tr",null,[(D=e.quotation.lead)!=null&&D.qty_1?(o(),a("td",te,s(parseInt((B=e.quotation.lead)==null?void 0:B.qty_1).toLocaleString())+" pcs",1)):n("",!0),e.quotation.price_qty_1?(o(),a("td",ee,s(l(e.quotation.price_qty_1)),1)):n("",!0),(j=e.quotation.lead)!=null&&j.qty_1&&e.quotation.price_qty_1?(o(),a("td",se,s(l(((U=e.quotation.lead)==null?void 0:U.qty_1)*e.quotation.price_qty_1)),1)):n("",!0)]),t("tr",null,[(V=e.quotation.lead)!=null&&V.qty_2?(o(),a("td",oe,s(parseInt((P=e.quotation.lead)==null?void 0:P.qty_2).toLocaleString())+" pcs",1)):n("",!0),e.quotation.price_qty_2?(o(),a("td",ae,s(l(e.quotation.price_qty_2)),1)):n("",!0),(z=e.quotation.lead)!=null&&z.qty_2&&e.quotation.price_qty_2?(o(),a("td",ne,s(l(((M=e.quotation.lead)==null?void 0:M.qty_2)*e.quotation.price_qty_2)),1)):n("",!0)]),t("tr",null,[(Q=e.quotation.lead)!=null&&Q.qty_3?(o(),a("td",ie,s(parseInt((I=e.quotation.lead)==null?void 0:I.qty_3).toLocaleString())+" pcs",1)):n("",!0),e.quotation.price_qty_3?(o(),a("td",le,s(l(e.quotation.price_qty_3)),1)):n("",!0),(F=e.quotation.lead)!=null&&F.qty_3&&e.quotation.price_qty_3?(o(),a("td",de,s(l(((K=e.quotation.lead)==null?void 0:K.qty_3)*e.quotation.price_qty_3)),1)):n("",!0)]),t("tr",null,[($=e.quotation.lead)!=null&&$.qty_4?(o(),a("td",ce,s(parseInt((E=e.quotation.lead)==null?void 0:E.qty_4).toLocaleString())+" pcs",1)):n("",!0),e.quotation.price_qty_4?(o(),a("td",re,s(l(e.quotation.price_qty_4)),1)):n("",!0),(H=e.quotation.lead)!=null&&H.qty_4&&e.quotation.price_qty_4?(o(),a("td",ue,s(l(((O=e.quotation.lead)==null?void 0:O.qty_4)*e.quotation.price_qty_4)),1)):n("",!0)])])])])]),e.quotation.notes?(o(),a("div",me,[xe,t("p",ye,s(e.quotation.notes),1)])):n("",!0)]),t("div",he,[t("div",ge,[_e,t("div",be,[d(v,{href:i.route("quotations.edit",e.quotation.id),class:"w-full"},{svg:x(()=>[fe]),_:1},8,["href"]),t("div",{class:"px-3 py-2"},[t("button",{onClick:Z,class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[we,w(" Download PDF ")])]),d(v,{href:i.route("quotations.index"),class:"w-full"},{svg:x(()=>[qe]),_:1},8,["href"])])]),e.quotation.documents&&e.quotation.documents.length>0?(o(),a("div",ve,[ke,t("div",pe,[(o(!0),a(T,null,tt(e.quotation.documents,q=>(o(),a("div",{key:q.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",Ae,[Le,t("span",Ce,s(q.orignal_name),1)]),t("a",{href:"/uploads/leads/"+q.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,Ne)]))),128))])])):n("",!0),t("div",Se,[De,t("div",Be,[t("div",je,[Ue,t("div",null,[Ve,t("p",Pe,s(y(e.quotation.created_at)),1)])]),e.quotation.updated_at!==e.quotation.created_at?(o(),a("div",ze,[Me,t("div",null,[Qe,t("p",Ie,s(y(e.quotation.updated_at)),1)])])):n("",!0)])])])])])]}),_:1})],64))}};export{He as default};
