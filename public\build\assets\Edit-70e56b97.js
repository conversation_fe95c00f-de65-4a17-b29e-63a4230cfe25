import{K as j,T as S,r as $,c as E,w as B,b as c,d as m,e as n,u as r,f as p,F as u,Z as M,g as e,q as N,j as v,k as U,E as H,i as R,t as x}from"./app-2e8279f3.js";import{_ as T,a as A}from"./AdminLayout-536dfaf3.js";import{_ as D,a as F}from"./TextInput-80d6fd40.js";import{_ as b}from"./InputLabel-287a7499.js";import{P as O}from"./PrimaryButton-04471aec.js";import{_ as y}from"./Checkbox-554bafcf.js";import"./_plugin-vue_export-helper-c27b6911.js";const q={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},K=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Role & Permission",-1),L={class:"border-b border-gray-900/10 pb-12"},Z={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},z={class:"sm:col-span-12 grid grid-cols-6 gap-6"},G={class:"col-span-2"},I={class:"sm:col-span-12"},J={class:"sm:col-span-3"},Q={class:"flex justify-between items-center border px-4 py-2 bg-gray-50 rounded-lg"},W={class:"flex items-center text-lg font-semibold leading-7 text-gray-900 space-x-2"},X=e("div",{class:"cursor-pointer"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})])],-1),Y={class:"border border-t-0 rounded-b-lg"},ee={class:"text-sm font-semibold leading-6 text-gray-900 p-1"},se={class:"flex justify-end p-1"},te={class:"flex mt-6 items-center justify-between"},oe={class:"ml-auto flex items-center justify-end gap-x-6"},ae=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel ",-1),ne={key:0,class:"text-sm text-gray-600"},ue={__name:"Edit",props:["data","roleHasPermissions"],setup(f){const l=f,_=j().props.role,t=S({name:_.name,id:_.id,permissions:l.roleHasPermissions});$({});const k=a=>t.permissions.includes(a),w=(a,s)=>{if(a)t.permissions.push(s);else{const o=t.permissions.indexOf(s);o!==-1&&t.permissions.splice(o,1)}g()},C=(a,s)=>{const o=a.target.checked,i=new Set(t.permissions);s.forEach(d=>{o?i.add(d.id):i.delete(d.id)}),t.permissions=Array.from(i)},h=E(()=>{const a={};return Object.keys(l.data).forEach(s=>{const o=l.data[s].every(i=>t.permissions.includes(i.id));a[s]=o}),a}),g=a=>{for(const s in l.data){const o=l.data[s].every(i=>t.permissions.includes(i.id));h.value[s]=o}};return B(t.permissions,(a,s)=>{for(const o in l.data)g()},{deep:!0}),(a,s)=>(c(),m(u,null,[n(r(M),{title:"Update Role-Permission"}),n(T,null,{default:p(()=>[e("div",q,[K,e("form",{onSubmit:s[2]||(s[2]=N(o=>r(t).patch(a.route("roles.update",{role:r(t).id})),["prevent"]))},[e("div",L,[e("div",Z,[e("div",z,[e("div",G,[n(b,{for:"name",value:"Role Name"}),n(D,{id:"name",type:"text",modelValue:r(t).name,"onUpdate:modelValue":s[0]||(s[0]=o=>r(t).name=o),onChange:s[1]||(s[1]=o=>r(t).validate("name"))},null,8,["modelValue"]),n(F,{class:"",message:r(t).errors.name},null,8,["message"])])]),e("div",I,[n(b,{for:"name",value:"Select Permission"})]),(c(!0),m(u,null,v(f.data,(o,i)=>(c(),m("div",J,[e("div",Q,[e("h3",W,[n(y,{checked:h.value[i],onChange:d=>C(d,o)},null,8,["checked","onChange"]),e("span",null,x(i),1)]),X]),e("div",Y,[(c(!0),m(u,null,v(o,(d,P)=>(c(),m("div",{key:P,class:"flex justify-between items-center px-4 py-1 border-b last:border-b-0"},[e("div",ee,x(d.name),1),e("div",se,[n(y,{checked:k(d.id),"onUpdate:checked":V=>w(V,d.id),name:"permissions"},null,8,["checked","onUpdate:checked"])])]))),128))])]))),256))])]),e("div",te,[e("div",oe,[n(A,{href:a.route("roles.index")},{svg:p(()=>[ae]),_:1},8,["href"]),n(O,{disabled:r(t).processing},{default:p(()=>[U("Update")]),_:1},8,["disabled"]),n(H,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[r(t).recentlySuccessful?(c(),m("p",ne,"Saved.")):R("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{ue as default};
