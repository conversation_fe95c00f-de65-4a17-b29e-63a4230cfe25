import{_ as B,a as w}from"./AdminLayout-46709983.js";import{P as O}from"./PrimaryButton-3e573a38.js";import{b as d,d as r,e as n,u as U,f as y,F as j,Z as P,h as t,t as s,l as p,j as a,n as I}from"./app-e21f56bc.js";import"./_plugin-vue_export-helper-c27b6911.js";const z={class:"animate-top"},M={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},T={class:"text-3xl font-bold text-gray-900"},E=t("p",{class:"text-gray-600 mt-1"},"Order Details",-1),K={class:"flex flex-col sm:flex-row gap-3"},Q=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),V={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},$={class:"lg:col-span-2 space-y-8"},F={class:"bg-white shadow rounded-lg p-6"},G=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Lead Information",-1),Z={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},H=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),J={class:"mt-1 text-sm text-gray-700"},R=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),W={class:"mt-1 text-sm text-gray-700"},X=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Number",-1),Y={class:"mt-1 text-sm text-gray-700"},tt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Status",-1),et={class:"mt-1 text-sm text-gray-700"},st={class:"bg-white shadow rounded-lg p-6"},ot=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),dt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},rt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),at={class:"mt-1 text-sm text-gray-700"},lt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),ct={class:"mt-1 text-sm text-gray-700"},nt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),it={class:"mt-1 text-sm text-gray-700"},mt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),xt={class:"mt-1 text-sm text-gray-700"},ht=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),yt={class:"mt-1 text-sm text-gray-700"},_t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),gt={class:"mt-1 text-sm text-gray-700"},ut={key:0,class:"md:col-span-2"},bt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),ft={class:"mt-1 text-sm text-gray-700"},wt={class:"bg-white shadow rounded-lg p-6"},pt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Information",-1),vt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},kt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),qt={key:0},Nt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Quotation Number",-1),At={class:"mt-1 text-sm text-gray-700"},Ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),St={class:"mt-1 text-sm text-gray-700"},Lt={key:1},Dt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Tracking Number",-1),Bt={class:"mt-1 text-sm text-gray-700"},Ot=t("label",{class:"block text-sm font-semibold text-gray-900"},"Expected Delivery",-1),Ut={class:"mt-1 text-sm text-gray-700"},jt={key:2},Pt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Actual Delivery",-1),It={class:"mt-1 text-sm text-gray-700"},zt={class:"bg-white shadow rounded-lg p-6"},Mt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Quantities",-1),Tt={class:"overflow-x-auto"},Et={class:"min-w-full divide-y divide-gray-200"},Kt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Unit Price"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Total")])],-1),Qt={class:"bg-white divide-y divide-gray-200"},Vt={key:0},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Gt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Zt={key:1},Ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Rt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Wt={key:2},Xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Yt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},te={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},ee={key:3},se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},oe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},de={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},re={class:"bg-gray-50"},ae=t("td",{colspan:"2",class:"px-6 py-4 text-sm font-semibold text-gray-900 text-right"},"Total Amount:",-1),le={class:"px-6 py-4 text-sm font-bold text-green-600"},ce={key:0,class:"bg-white shadow rounded-lg p-6"},ne=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),ie={class:"text-sm text-gray-700 whitespace-pre-wrap"},me={class:"space-y-6"},xe={class:"bg-white shadow rounded-lg p-6"},he=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),ye={class:"space-y-3 w-full"},_e=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),p(" Edit Order ")],-1),ge=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),p(" Back to Orders ")],-1),ue={class:"bg-white shadow rounded-lg p-6"},be=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),fe={class:"space-y-4"},we={class:"flex items-start space-x-3"},pe=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),ve=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Created",-1),ke={class:"text-xs text-gray-500"},qe={key:0,class:"flex items-start space-x-3"},Ne=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),Ae=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Confirmed",-1),Ce={class:"text-xs text-gray-500"},Se={key:1,class:"flex items-start space-x-3"},Le=t("div",{class:"flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"},null,-1),De=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),Be={class:"text-xs text-gray-500"},Ie={__name:"Show",props:{order:{type:Object,required:!0}},setup(e){const S=e,L=o=>({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-indigo-100 text-indigo-800",delivered:"bg-green-100 text-green-800"})[o]||"bg-gray-100 text-gray-800",i=o=>o?new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",l=o=>{var x,h;const c={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},_=((h=(x=S.order.lead)==null?void 0:x.county)==null?void 0:h.name)||"UK",g=Object.keys(c).find(f=>_.toLowerCase().includes(f.toLowerCase())),{locale:u,currency:m}=c[g]||c.UK,b=new Intl.NumberFormat(u,{style:"currency",currency:m,currencyDisplay:"symbol"}).format(o);return`${m} ${b}`},D=o=>({pending:"Pending",confirmed:"Confirmed",under_production:"Under Production",shipped:"Shipped",delivered:"Delivered"})[o]||o;return(o,c)=>(d(),r(j,null,[n(U(P),{title:"Orders"}),n(B,null,{default:y(()=>{var _,g,u,m,b,x,h,f,v,k,q,N,A,C;return[t("div",z,[t("div",M,[t("div",null,[t("h1",T,s(e.order.order_number),1),E]),t("div",K,[n(w,{href:o.route("orders.edit",e.order.id)},{svg:y(()=>[n(O,{class:"w-full items-center"},{default:y(()=>[Q,p(" Edit Order ")]),_:1})]),_:1},8,["href"])])]),t("div",V,[t("div",$,[t("div",F,[G,t("div",Z,[t("div",null,[H,t("p",J,s(((_=e.order.lead)==null?void 0:_.client_name)||"N/A"),1)]),t("div",null,[R,t("p",W,s(((u=(g=e.order.lead)==null?void 0:g.county)==null?void 0:u.name)||"N/A"),1)]),t("div",null,[X,t("p",Y,s(((m=e.order.lead)==null?void 0:m.lead_number)||"N/A"),1)]),t("div",null,[tt,t("p",et,s(((b=e.order.lead)==null?void 0:b.status)||"N/A"),1)])])]),t("div",st,[ot,t("div",dt,[t("div",null,[rt,t("p",at,s(((x=e.order.lead)==null?void 0:x.dimensions)||"N/A"),1)]),t("div",null,[lt,t("p",ct,s(((h=e.order.lead)==null?void 0:h.open_size)||"N/A"),1)]),t("div",null,[nt,t("p",it,s(((f=e.order.lead)==null?void 0:f.box_style)||"N/A"),1)]),t("div",null,[mt,t("p",xt,s(((v=e.order.lead)==null?void 0:v.stock)||"N/A"),1)]),t("div",null,[ht,t("p",yt,s(((k=e.order.lead)==null?void 0:k.lamination)||"N/A"),1)]),t("div",null,[_t,t("p",gt,s(((q=e.order.lead)==null?void 0:q.printing)||"N/A"),1)]),(N=e.order.lead)!=null&&N.add_ons?(d(),r("div",ut,[bt,t("p",ft,s(e.order.lead.add_ons),1)])):a("",!0)])]),t("div",wt,[pt,t("div",vt,[t("div",null,[kt,t("span",{class:I(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",L(e.order.status)])},s(D(e.order.status)),3)]),e.order.quotation?(d(),r("div",qt,[Nt,t("p",At,s(e.order.quotation.quotation_number),1)])):a("",!0),t("div",null,[Ct,t("p",St,s((A=e.order.creator)==null?void 0:A.first_name)+" "+s((C=e.order.creator)==null?void 0:C.last_name),1)]),e.order.tracking_number?(d(),r("div",Lt,[Dt,t("p",Bt,s(e.order.tracking_number),1)])):a("",!0),t("div",null,[Ot,t("p",Ut,s(i(e.order.expected_delivery)),1)]),e.order.actual_delivery?(d(),r("div",jt,[Pt,t("p",It,s(i(e.order.actual_delivery)),1)])):a("",!0)])]),t("div",zt,[Mt,t("div",Tt,[t("table",Et,[Kt,t("tbody",Qt,[e.order.selected_qty_1&&e.order.price_qty_1?(d(),r("tr",Vt,[t("td",$t,s(parseInt(e.order.selected_qty_1).toLocaleString())+" pcs",1),t("td",Ft,s(l(e.order.price_qty_1)),1),t("td",Gt,s(l(e.order.selected_qty_1*e.order.price_qty_1)),1)])):a("",!0),e.order.selected_qty_2&&e.order.price_qty_2?(d(),r("tr",Zt,[t("td",Ht,s(parseInt(e.order.selected_qty_2).toLocaleString())+" pcs",1),t("td",Jt,s(l(e.order.price_qty_2)),1),t("td",Rt,s(l(e.order.selected_qty_2*e.order.price_qty_2)),1)])):a("",!0),e.order.selected_qty_3&&e.order.price_qty_3?(d(),r("tr",Wt,[t("td",Xt,s(parseInt(e.order.selected_qty_3).toLocaleString())+" pcs",1),t("td",Yt,s(l(e.order.price_qty_3)),1),t("td",te,s(l(e.order.selected_qty_3*e.order.price_qty_3)),1)])):a("",!0),e.order.selected_qty_4&&e.order.price_qty_4?(d(),r("tr",ee,[t("td",se,s(parseInt(e.order.selected_qty_4).toLocaleString())+" pcs",1),t("td",oe,s(l(e.order.price_qty_4)),1),t("td",de,s(l(e.order.selected_qty_4*e.order.price_qty_4)),1)])):a("",!0)]),t("tfoot",re,[t("tr",null,[ae,t("td",le,s(l(e.order.total_amount)),1)])])])])]),e.order.notes?(d(),r("div",ce,[ne,t("p",ie,s(e.order.notes),1)])):a("",!0)]),t("div",me,[t("div",xe,[he,t("div",ye,[n(w,{href:o.route("orders.edit",e.order.id),class:"w-full"},{svg:y(()=>[_e]),_:1},8,["href"]),n(w,{href:o.route("orders.index"),class:"w-full"},{svg:y(()=>[ge]),_:1},8,["href"])])]),t("div",ue,[be,t("div",fe,[t("div",we,[pe,t("div",null,[ve,t("p",ke,s(i(e.order.created_at)),1)])]),e.order.is_confirmed?(d(),r("div",qe,[Ne,t("div",null,[Ae,t("p",Ce,s(i(e.order.confirmed_at)),1)])])):a("",!0),e.order.updated_at!==e.order.created_at?(d(),r("div",Se,[Le,t("div",null,[De,t("p",Be,s(i(e.order.updated_at)),1)])])):a("",!0)])])])])])]}),_:1})],64))}};export{Ie as default};
