<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

const props = defineProps({
    data: {
        type: Object,
        required: true
    },
    counties: {
        type: Array,
        required: true
    },
    filepath: {
        type: Object,
        default: null
    }
});

// Initialize form with existing quotation data
const form = useForm({
    id: props.data.id,
    client_name: props.data.client_name || '',
    county_id: props.data.county_id || '',
    lead_id: props.data.lead_id || null,
    dimensions: props.data.dimensions || '',
    open_size: props.data.open_size || '',
    box_style: props.data.box_style || '',
    stock: props.data.stock || '',
    lamination: props.data.lamination || '',
    printing: props.data.printing || '',
    add_ons: props.data.add_ons || '',
    qty_1: props.data.qty_1 || '',
    qty_2: props.data.qty_2 || '',
    qty_3: props.data.qty_3 || '',
    qty_4: props.data.qty_4 || '',
    price_qty_1: props.data.price_qty_1 || '',
    price_qty_2: props.data.price_qty_2 || '',
    price_qty_3: props.data.price_qty_3 || '',
    price_qty_4: props.data.price_qty_4 || '',
    notes: props.data.notes || '',
    valid_until: props.data.valid_until || '',
    // documents: []
});

// Checkbox states for quantity selection based on existing data
const includeQty1 = ref(!!props.data.qty_1);
const includeQty2 = ref(!!props.data.qty_2);
const includeQty3 = ref(!!props.data.qty_3);
const includeQty4 = ref(!!props.data.qty_4);

const totalAmount = ref(0);

const submit = () => {
    form.post(route('quotations.store'), {
        preserveScroll: true,
    });
};

const setCounty = (id, name) => {
    form.county_id = id;
};

const handleDocuments = (files) => {
    form.documents = files;
};

// Calculate total amount when prices or quantities change
const calculateTotal = () => {
    let total = 0;

    if (includeQty1.value && form.qty_1 && form.price_qty_1) {
        total += parseFloat(form.qty_1) * parseFloat(form.price_qty_1);
    }
    if (includeQty2.value && form.qty_2 && form.price_qty_2) {
        total += parseFloat(form.qty_2) * parseFloat(form.price_qty_2);
    }
    if (includeQty3.value && form.qty_3 && form.price_qty_3) {
        total += parseFloat(form.qty_3) * parseFloat(form.price_qty_3);
    }
    if (includeQty4.value && form.qty_4 && form.price_qty_4) {
        total += parseFloat(form.qty_4) * parseFloat(form.price_qty_4);
    }

    totalAmount.value = total;
};

// Clear quantity and price when checkbox is unchecked
const handleQtyCheckbox = (qtyNumber, isChecked) => {
    if (!isChecked) {
        form[`qty_${qtyNumber}`] = '';
        form[`price_qty_${qtyNumber}`] = '';
    }
    calculateTotal();
};

// Watch for changes in quantities, prices, and checkboxes
watch([
    () => form.qty_1, () => form.price_qty_1, () => includeQty1.value,
    () => form.qty_2, () => form.price_qty_2, () => includeQty2.value,
    () => form.qty_3, () => form.price_qty_3, () => includeQty3.value,
    () => form.qty_4, () => form.price_qty_4, () => includeQty4.value
], calculateTotal);

// Calculate initial total
calculateTotal();

const removeDocument = (documentId) => {
    if (confirm('Are you sure you want to remove this document?')) {
        window.location.href = route('removequotationdocument', documentId);
    }
};
</script>

<template>
    <Head title="Edit Quotation" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">
                Edit Quotation - {{ data.quotation_number }}
            </h2>

            <form @submit.prevent="submit">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                        <div class="sm:col-span-4">
                            <InputLabel for="client_name" value="Client Name *"/>
                            <TextInput
                                id="client_name"
                                type="text"
                                v-model="form.client_name"
                                required
                            />
                            <InputError :message="form.errors.client_name"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="county_id" value="Country *"/>
                            <div class="relative mt-2">
                                <SearchableDropdownNew
                                    :options="counties"
                                    v-model="form.county_id"
                                    @onchange="setCounty"
                                />
                            </div>
                            <InputError :message="form.errors.county_id"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="dimensions" value="Dimensions *"/>
                            <TextInput
                                id="dimensions"
                                type="text"
                                v-model="form.dimensions"
                                required
                            />
                            <InputError :message="form.errors.dimensions"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="open_size" value="Open Size *"/>
                            <TextInput
                                id="open_size"
                                type="text"
                                v-model="form.open_size"
                                required
                            />
                            <InputError :message="form.errors.open_size"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="box_style" value="Box Style *"/>
                            <TextInput
                                id="box_style"
                                type="text"
                                v-model="form.box_style"
                                required
                            />
                            <InputError :message="form.errors.box_style"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="stock" value="Stock *"/>
                            <TextInput
                                id="stock"
                                type="text"
                                v-model="form.stock"
                                required
                            />
                            <InputError :message="form.errors.stock"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="lamination" value="Lamination *"/>
                            <TextInput
                                id="lamination"
                                type="text"
                                v-model="form.lamination"
                                required
                            />
                            <InputError :message="form.errors.lamination"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="printing" value="Printing *"/>
                            <TextInput
                                id="printing"
                                type="text"
                                v-model="form.printing"
                                required
                            />
                            <InputError :message="form.errors.printing"/>
                        </div>

                        <div class="sm:col-span-4">
                            <InputLabel for="add_ons" value="Add-ons"/>
                            <TextInput
                                id="add_ons"
                                type="text"
                                v-model="form.add_ons"
                            />
                            <InputError :message="form.errors.add_ons"/>
                        </div>

                        <!-- Quantities and Pricing -->
                        <div class="sm:col-span-12 mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quantities and Pricing</h3>
                        </div>

                        <!-- Quantity 1 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty1"
                                    @update:checked="(checked) => handleQtyCheckbox(1, checked)"
                                />
                                <InputLabel value="Include Quantity 1" class="text-base font-medium text-blue-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty1">
                                <div>
                                    <InputLabel for="qty_1" value="QTY 1 *"/>
                                    <TextInput
                                        id="qty_1"
                                        type="number"
                                        v-model="form.qty_1"
                                        min="1"
                                        required
                                    />
                                    <InputError :message="form.errors.qty_1"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_1" value="PRICE QTY 1 *"/>
                                    <TextInput
                                        id="price_qty_1"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_1"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_1"/>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 2 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty2"
                                    @update:checked="(checked) => handleQtyCheckbox(2, checked)"
                                />
                                <InputLabel value="Include Quantity 2" class="text-base font-medium text-green-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty2">
                                <div>
                                    <InputLabel for="qty_2" value="QTY 2"/>
                                    <TextInput
                                        id="qty_2"
                                        type="number"
                                        v-model="form.qty_2"
                                        min="1"
                                    />
                                    <InputError :message="form.errors.qty_2"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_2" value="PRICE QTY 2"/>
                                    <TextInput
                                        id="price_qty_2"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_2"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_2"/>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 3 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty3"
                                    @update:checked="(checked) => handleQtyCheckbox(3, checked)"
                                />
                                <InputLabel value="Include Quantity 3" class="text-base font-medium text-yellow-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty3">
                                <div>
                                    <InputLabel for="qty_3" value="QTY 3"/>
                                    <TextInput
                                        id="qty_3"
                                        type="number"
                                        v-model="form.qty_3"
                                        min="1"
                                    />
                                    <InputError :message="form.errors.qty_3"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_3" value="PRICE QTY 3"/>
                                    <TextInput
                                        id="price_qty_3"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_3"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_3"/>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity 4 Section -->
                        <div class="sm:col-span-3">
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg">
                                <Checkbox
                                    v-model:checked="includeQty4"
                                    @update:checked="(checked) => handleQtyCheckbox(4, checked)"
                                />
                                <InputLabel value="Include Quantity 4" class="text-base font-medium text-purple-800"/>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4" v-if="includeQty4">
                                <div>
                                    <InputLabel for="qty_4" value="QTY 4"/>
                                    <TextInput
                                        id="qty_4"
                                        type="number"
                                        v-model="form.qty_4"
                                        min="1"
                                    />
                                    <InputError :message="form.errors.qty_4"/>
                                </div>
                                <div>
                                    <InputLabel for="price_qty_4" value="PRICE QTY 4"/>
                                    <TextInput
                                        id="price_qty_4"
                                        type="number"
                                        step="0.01"
                                        v-model="form.price_qty_4"
                                        min="0"
                                    />
                                    <InputError :message="form.errors.price_qty_4"/>
                                </div>
                            </div>
                        </div>

                        <!-- Total Amount Display -->
                        <div class="sm:col-span-12">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-lg font-semibold text-gray-900">
                                    Estimated Total: ${{ totalAmount.toFixed(2) }}
                                </p>
                            </div>
                        </div>

                        <!-- <div class="sm:col-span-6">
                            <InputLabel for="valid_until" value="Valid Until"/>
                            <TextInput
                                id="valid_until"
                                type="date"
                                v-model="form.valid_until"
                            />
                            <InputError :message="form.errors.valid_until"/>
                        </div> -->

                        <div class="sm:col-span-12">
                            <InputLabel for="notes" value="Notes"/>
                            <TextArea
                                id="notes"
                                v-model="form.notes"
                                rows="4"
                            />
                            <InputError :message="form.errors.notes"/>
                        </div>

                        <!-- Existing Documents -->
                        <div class="sm:col-span-12" v-if="data.documents && data.documents.length > 0">
                            <InputLabel value="Existing Documents"/>
                            <div class="mt-2 space-y-2">
                                <div v-for="document in data.documents" :key="document.id" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">{{ document.orignal_name }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <a :href="filepath.view + document.name" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">View</a>
                                        <button type="button" @click="removeDocument(document.id)" class="text-red-600 hover:text-red-800 text-sm">Remove</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- <div class="sm:col-span-12">
                            <InputLabel for="documents" value="Add New Documents"/>
                            <MultipleFileUpload @change="handleDocuments" />
                            <InputError :message="form.errors.documents"/>
                        </div> -->
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('quotations.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
