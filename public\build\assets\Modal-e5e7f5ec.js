import{b as u,d as y,B as f,w,o as h,a as v,c as x,m as g,e as l,f as n,h as c,G as i,g as s,D as r,n as _,i as b,P as k,p as B,l as S}from"./app-b86547fe.js";/* empty css                                                              */import{_ as C}from"./_plugin-vue_export-helper-c27b6911.js";const E=["type"],T={__name:"SecondaryButton",props:{type:{type:String,default:"button"}},setup(e){return(a,t)=>(u(),y("button",{type:e.type,class:"inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"},[f(a.$slots,"default")],8,E))}},I=e=>(B("data-v-5ddf2f2a"),e=e(),S(),e),M={class:"fixed inset-0 overflow-y-auto px-4 py-12 sm:px-0 z-50","scroll-region":""},$=I(()=>s("div",{class:"absolute inset-0 bg-gray-500 opacity-75"},null,-1)),N=[$],V={__name:"Modal",props:{show:{type:Boolean,default:!1},maxWidth:{type:String,default:"2xl"},closeable:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:a}){const t=e;w(()=>t.show,()=>{t.show?document.body.style.overflow="hidden":document.body.style.overflow=null});const d=()=>{t.closeable&&a("close")},m=o=>{o.key==="Escape"&&t.show&&d()};h(()=>document.addEventListener("keydown",m)),v(()=>{document.removeEventListener("keydown",m),document.body.style.overflow=null});const p=x(()=>({sm:"sm:max-w-sm",md:"sm:max-w-md",lg:"sm:max-w-lg",xl:"sm:max-w-xl",custom:"custom-max-width",custom2:"custom-payment-width","2xl":"sm:max-w-lg"})[t.maxWidth]);return(o,W)=>(u(),g(k,{to:"body"},[l(r,{"leave-active-class":"duration-200"},{default:n(()=>[c(s("div",M,[l(r,{"enter-active-class":"ease-out duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:n(()=>[c(s("div",{class:"fixed inset-0 transform transition-all",onClick:d},N,512),[[i,e.show]])]),_:1}),l(r,{"enter-active-class":"ease-out duration-300","enter-from-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to-class":"opacity-100 translate-y-0 sm:scale-100","leave-active-class":"ease-in duration-200","leave-from-class":"opacity-100 translate-y-0 sm:scale-100","leave-to-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:n(()=>[c(s("div",{class:_(["mb-6 bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-full sm:mx-auto",p.value])},[e.show?f(o.$slots,"default",{key:0},void 0,!0):b("",!0)],2),[[i,e.show]])]),_:3})],512),[[i,e.show]])]),_:3})]))}},G=C(V,[["__scopeId","data-v-5ddf2f2a"]]);export{G as M,T as _};
