import{T as c,b as _,m as w,f as p,e as a,u as e,Z as f,g as t,k as g,n as y,q as x,p as b,l as h}from"./app-631e032c.js";import{G as v}from"./GuestLayout-e8c145a4.js";import{_ as l,a as i}from"./TextInput-21def0d8.js";import{_ as n}from"./InputLabel-1e9f0b7d.js";import{P as V}from"./PrimaryButton-8a3101e5.js";import{_ as k}from"./_plugin-vue_export-helper-c27b6911.js";const P=r=>(b("data-v-eabda7c0"),r=r(),h(),r),S=P(()=>t("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Reset Password",-1)),q=["onSubmit"],R={class:"mt-2"},B={class:"mt-4"},I={class:"flex items-center justify-end mt-4"},C={__name:"ResetPassword",props:{email:{type:String,required:!0},token:{type:String,required:!0}},setup(r){const m=r,s=c({token:m.token,email:m.email,password:"",password_confirmation:""}),u=()=>{s.post(route("password.store"),{onFinish:()=>s.reset("password","password_confirmation")})};return(N,o)=>(_(),w(v,null,{default:p(()=>[a(e(f),{title:"Reset Password"}),S,t("form",{onSubmit:x(u,["prevent"])},[t("div",null,[a(n,{for:"email",value:"Email"}),a(l,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).email,"onUpdate:modelValue":o[0]||(o[0]=d=>e(s).email=d),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),a(i,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),t("div",R,[a(n,{for:"password",value:"Password"}),a(l,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password,"onUpdate:modelValue":o[1]||(o[1]=d=>e(s).password=d),required:"",autocomplete:"new-password"},null,8,["modelValue"]),a(i,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),t("div",B,[a(n,{for:"password_confirmation",value:"Confirm Password"}),a(l,{id:"password_confirmation",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=d=>e(s).password_confirmation=d),required:"",autocomplete:"new-password"},null,8,["modelValue"]),a(i,{class:"mt-2",message:e(s).errors.password_confirmation},null,8,["message"])]),t("div",I,[a(V,{class:y({"opacity-25":e(s).processing}),disabled:e(s).processing},{default:p(()=>[g(" Reset Password ")]),_:1},8,["class","disabled"])])],40,q)]),_:1}))}},E=k(C,[["__scopeId","data-v-eabda7c0"]]);export{E as default};
