import{r as g,c as O,b as n,d as i,e as a,u as v,f as d,F as w,Z as oe,g as e,h as L,v as ae,G as le,j as V,m as $,i as B,k as M,t as r,y as ne,n as ie,O as E}from"./app-df1bcebc.js";import{_ as re,b as de,a as T}from"./AdminLayout-e6738fa9.js";import{_ as ce}from"./SecondaryButton-fc15966a.js";import{D as ue}from"./DangerButton-51ff0263.js";import{M as _e}from"./Modal-dd25c3f8.js";import{s as me,_ as pe,a as he}from"./ArrowIcon-d1ca548e.js";import{_ as N}from"./SearchableDropdownNew-f22323b4.js";import{_ as k}from"./InputLabel-df28ac37.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ge={class:"animate-top"},ve={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},fe=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Orders")],-1),xe={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},ye={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},be=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),we={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ke={class:"flex justify-between mb-2"},Ce={class:"flex"},Ae=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Se={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Oe={class:"sm:col-span-4"},Ve={class:"relative mt-2"},Me={class:"sm:col-span-4"},Ne={class:"relative mt-2"},De={class:"sm:col-span-4"},Le={class:"relative mt-2"},$e={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Be={class:"shadow rounded-lg"},Ee={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Te={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Ue={class:"border-b-2"},Pe=["onClick"],je={key:0},ze={class:"px-4 py-2.5 min-w-36"},Fe={class:"px-4 py-2.5 min-w-36"},Ie={class:"px-4 py-2.5 min-w-28"},Re={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},qe={class:"px-4 py-2.5 font-semibold text-green-600 min-w-36"},Ge={class:"px-4 py-2.5"},We={key:0,class:"text-blue-600 font-mono text-sm"},Ke={key:1,class:"text-gray-400 text-sm"},He={class:"px-4 py-2.5 min-w-36"},Qe={class:"px-4 py-2.5 min-w-44"},Xe={key:0,class:"flex items-center space-x-2"},Ye=["onUpdate:modelValue","onChange"],Ze=["value"],Je=["onClick"],et={key:1,class:"flex items-center space-x-2"},tt=["onClick"],st={class:"px-4 py-2.5"},ot={class:"items-center px-4 py-2.5"},at={class:"flex items-center justify-start gap-4"},lt=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),nt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),it=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),rt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),dt=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),ct=["onClick"],ut=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),_t=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),mt=[ut,_t],pt=["onClick"],ht=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),gt=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),vt=[ht,gt],ft={key:1},xt=e("tr",{class:"bg-white"},[e("td",{colspan:"10",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),yt=[xt],bt={class:"p-6"},wt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this order? ",-1),kt={class:"mt-6 flex justify-end"},Et={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status"],setup(p){const f=p,{form:C,search:y,sort:U,fetchData:Ct,sortKey:P,sortDirection:j}=me("orders.index"),A=g(!1),D=g(null),z=[{id:"confirmed",name:"Confirmed"},{id:"under_production",name:"Under Production"},{id:"shipped",name:"Shipped"},{id:"delivered",name:"Delivered"}],F=O(()=>[{id:"",name:"All Agents"},...f.agents]),I=O(()=>[{id:"",name:"All Country"},...f.counties]),R=O(()=>[{id:"",name:"All Status"},...f.statusOptions]),q=[{field:"order_number",label:"ORDER NO",sortable:!0},{field:"quotation.quotation_number",label:"QUOTATION NO",sortable:!1},{field:"lead.lead_number",label:"LEAD NO",sortable:!1},{field:"client_name",label:"CLIENT NAME",sortable:!0},{field:"total_amount",label:"TOTAL AMOUNT",sortable:!0},{field:"tracking_number",label:"TRACKING",sortable:!1},{field:"expected_delivery",label:"EXP. DELIVERY",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"creator.first_name",label:"AGENT",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],G=s=>{D.value=s,A.value=!0},S=()=>{A.value=!1},W=()=>{C.delete(route("orders.destroy",{order:D.value}),{onSuccess:()=>S()})},K=s=>({confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-indigo-100 text-indigo-800",delivered:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",c=g(f.agent_id||""),u=g(f.county_id||""),_=g(f.status||""),x=g(""),h=g({}),H=(s,o)=>{c.value=s,b(x.value,c.value,u.value,_.value)},Q=(s,o)=>{u.value=s,b(x.value,c.value,u.value,_.value)},X=(s,o)=>{_.value=s,b(x.value,c.value,u.value,_.value)},b=(s,o,t,m)=>{x.value=s;const l=o===""?null:o,te=t===""?null:t,se=m===""?null:m;C.get(route("orders.index",{search:s,agent_id:l,county_id:te,status:se}),{preserveState:!0})},Y=(s,o)=>{h.value[s]=o},Z=s=>{delete h.value[s]},J=s=>{window.open(route("orders.pdf",s),"_blank")},ee=(s,o)=>{E.post(route("orders.update-status",s),{status:o},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete h.value[s];const m=new URLSearchParams(window.location.search).get("page")||1;E.get(route("orders.index"),{search:x.value,agent_id:c.value===""?null:c.value,county_id:u.value===""?null:u.value,status:_.value===""?null:_.value,page:m},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})};return(s,o)=>(n(),i(w,null,[a(v(oe),{title:"Orders"}),a(re,null,{default:d(()=>[e("div",ge,[e("div",ve,[fe,e("div",xe,[e("div",ye,[be,L(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>le(y)?y.value=t:null),onInput:o[1]||(o[1]=t=>b(v(y),c.value,u.value,_.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for orders..."},null,544),[[ae,v(y)]])])])]),e("div",we,[e("div",ke,[e("div",Ce,[Ae,a(k,{for:"filters",value:"Filters"})])]),e("div",Se,[e("div",Oe,[a(k,{for:"agent_filter",value:"Agents"}),e("div",Ve,[a(N,{options:F.value,modelValue:c.value,"onUpdate:modelValue":o[2]||(o[2]=t=>c.value=t),onOnchange:H},null,8,["options","modelValue"])])]),e("div",Me,[a(k,{for:"county_filter",value:"Country"}),e("div",Ne,[a(N,{options:I.value,modelValue:u.value,"onUpdate:modelValue":o[3]||(o[3]=t=>u.value=t),onOnchange:Q},null,8,["options","modelValue"])])]),e("div",De,[a(k,{for:"status_filter",value:"Status"}),e("div",Le,[a(N,{options:R.value,modelValue:_.value,"onUpdate:modelValue":o[4]||(o[4]=t=>_.value=t),onOnchange:X},null,8,["options","modelValue"])])])])]),e("div",$e,[e("div",Be,[e("table",Ee,[e("thead",Te,[e("tr",Ue,[(n(),i(w,null,V(q,(t,m)=>e("th",{key:m,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:l=>v(U)(t.field,t.sortable)},[M(r(t.label)+" ",1),t.sortable?(n(),$(he,{key:0,isSorted:v(P)===t.field,direction:v(j)},null,8,["isSorted","direction"])):B("",!0)],8,Pe)),64))])]),p.data.data&&p.data.data.length>0?(n(),i("tbody",je,[(n(!0),i(w,null,V(p.data.data,t=>{var m;return n(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",ze,r(t.order_number),1),e("td",Fe,r(t.quotation?t.quotation.quotation_number:"N/A"),1),e("td",Ie,r(t.lead?t.lead.lead_number:"N/A"),1),e("td",Re,r((m=t.lead)==null?void 0:m.client_name),1),e("td",qe,"£"+r(parseFloat(t.total_amount).toFixed(2)),1),e("td",Ge,[t.tracking_number?(n(),i("span",We,r(t.tracking_number),1)):(n(),i("span",Ke,"-"))]),e("td",He,r(t.expected_delivery?new Date(t.expected_delivery).toLocaleDateString("en-GB"):"N/A"),1),e("td",Qe,[h.value[t.id]!==void 0?(n(),i("div",Xe,[L(e("select",{"onUpdate:modelValue":l=>h.value[t.id]=l,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:l=>ee(t.id,h.value[t.id])},[(n(),i(w,null,V(z,l=>e("option",{key:l.id,value:l.id},r(l.name),9,Ze)),64))],40,Ye),[[ne,h.value[t.id]]]),e("button",{onClick:l=>Z(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,Je)])):(n(),i("div",et,[e("span",{class:ie(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",K(t.status)]),onClick:l=>Y(t.id,t.status),title:"Click to edit status"},r(t.status.charAt(0).toUpperCase()+t.status.slice(1).replace("_"," ")),11,tt)]))]),e("td",st,r(t.lead.creator?t.lead.creator.first_name:"N/A"),1),e("td",ot,[e("div",at,[a(de,{align:"right",width:"48"},{trigger:d(()=>[lt]),content:d(()=>[a(T,{href:s.route("orders.show",{order:t.id})},{svg:d(()=>[nt]),text:d(()=>[it]),_:2},1032,["href"]),a(T,{href:s.route("orders.edit",{order:t.id})},{svg:d(()=>[rt]),text:d(()=>[dt]),_:2},1032,["href"]),e("button",{type:"button",onClick:l=>G(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},mt,8,ct),e("button",{type:"button",onClick:l=>J(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},vt,8,pt)]),_:2},1024)])])])}),128))])):(n(),i("tbody",ft,yt))])])]),p.data.data&&p.data.data.length>0?(n(),$(pe,{key:0,class:"mt-6",links:p.data.links},null,8,["links"])):B("",!0)]),a(_e,{show:A.value,onClose:S},{default:d(()=>[e("div",bt,[wt,e("div",kt,[a(ce,{onClick:S},{default:d(()=>[M("Cancel")]),_:1}),a(ue,{class:"ml-3",onClick:W,disabled:v(C).processing},{default:d(()=>[M(" Delete Order ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Et as default};
