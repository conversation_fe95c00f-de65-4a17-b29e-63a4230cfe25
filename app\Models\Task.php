<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'title', 'description', 'type', 'priority', 'status',
        'due_date', 'reminder_date', 'completed_at',
        'assigned_to', 'created_by', 'taskable_type', 'taskable_id',
        'metadata', 'notes', 'is_recurring', 'recurring_pattern'
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'reminder_date' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
        'is_recurring' => 'boolean',
    ];

    // Relationships
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function taskable(): MorphTo
    {
        return $this->morphTo();
    }

    public function activityLogs(): HasMany
    {
        return $this->hasMany(ActivityLog::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereIn('status', ['pending', 'in_progress']);
    }

    public function scopeDueToday($query)
    {
        return $query->whereDate('due_date', today())
                    ->whereIn('status', ['pending', 'in_progress']);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Accessors
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date < now() && in_array($this->status, ['pending', 'in_progress']);
    }

    public function getIsDueTodayAttribute(): bool
    {
        return $this->due_date->isToday() && in_array($this->status, ['pending', 'in_progress']);
    }

    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'urgent' => 'red',
            default => 'gray'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'yellow',
            'in_progress' => 'blue',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    // Methods
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now()
        ]);
    }

    public function createFollowUpTask(array $data): Task
    {
        return static::create([
            'title' => $data['title'] ?? 'Follow-up: ' . $this->title,
            'description' => $data['description'] ?? 'Follow-up task for: ' . $this->title,
            'type' => $data['type'] ?? 'follow_up',
            'priority' => $data['priority'] ?? $this->priority,
            'due_date' => $data['due_date'],
            'assigned_to' => $data['assigned_to'] ?? $this->assigned_to,
            'created_by' => $data['created_by'] ?? $this->created_by,
            'taskable_type' => $this->taskable_type,
            'taskable_id' => $this->taskable_id,
        ]);
    }
}
