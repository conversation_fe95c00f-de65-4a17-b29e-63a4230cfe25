<script setup>
import { ref, onMounted } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import Dropdown from '@/Components/Dropdown.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { sortAndSearch } from '@/Composables/sortAndSearch';

const props = defineProps(['data', 'search', 'permissions', 'counties', 'county_id', 'agents', 'agent_id']);
const { form, search, sort, fetchData, sortKey, sortDirection } = sortAndSearch('leads.index');

const modalVisible = ref(false);
const selectedLeadId = ref(null);

const columns = [
    { field: 'lead_number', label: 'LEAD NUMBER', sortable: true },
    { field: 'client_name', label: 'CLIENT NAME', sortable: true },
    { field: 'county.name', label: 'COUNTRY', sortable: false },
    { field: 'open_size', label: 'OPEN SIZE', sortable: true },
    { field: 'box_style', label: 'BOX STYLE', sortable: true },
    { field: 'stock', label: 'STOCK', sortable: true },
    { field: 'status', label: 'STATUS', sortable: true },
    { field: 'creator.first_name', label: 'AGENT', sortable: true },
    { field: 'action', label: 'ACTION', sortable: false }
];

const openDeleteModal = (leadId) => {
    selectedLeadId.value = leadId;
    modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteLead = () => {
    form.delete(route('leads.destroy', { lead: selectedLeadId.value }), {
        onSuccess: () => closeModal()
    });
};

const getStatusClass = (status) => {
    const classes = {
        'new': 'bg-blue-100 text-blue-800',
        'contacted': 'bg-purple-100 text-purple-800',
        'quotation': 'bg-yellow-100 text-yellow-800',
        'negotiation': 'bg-orange-100 text-orange-800',
        'won': 'bg-green-100 text-green-800',
        'lost': 'bg-red-100 text-red-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

const agentId = ref(props.agent_id);
const countyId = ref(props.county_id);
const searchValue = ref('');

const setAgent = (id, name) => {
    agentId.value = id;
    handleSearchChange(searchValue.value, agentId.value , countyId.value );
};

const setCounty = (id, name) => {
    countyId.value = id;
    handleSearchChange(searchValue.value, agentId.value , countyId.value );
};

const handleSearchChange = (value, agentId, countyId) => {
    searchValue.value = value;
    form.get(route('leads.index', { search: value , agent_id: agentId, county_id: countyId}), {
        preserveState: true,
    });
};
</script>

<template>
    <Head title="Leads" />
    <AdminLayout>
        <div class="animate-top">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Leads</h1>
                </div>
                <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                        </svg>
                        <input type="text" v-model="search" @input="handleSearchChange(search, agentId, countyId)" class="block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-gray-50" placeholder="Search for leads...">
                    </div>
                    <div v-if="permissions.canCreateLead" class="sm:flex-none">
                        <CreateButton :href="route('leads.create')">
                            Add Lead
                        </CreateButton>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex justify-between mb-2">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                        </svg>
                        <InputLabel for="customer_id" value="Filters" />
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Agents" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="agents"
                            v-model="agentId"
                            @onchange="setAgent"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Country" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="counties"
                            v-model="countyId"
                            @onchange="setCounty"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto rounded-lg max-w-full">
                <div class="shadow rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr  class="odd:bg-white even:bg-gray-50 border-b" v-for="lead in data.data" :key="lead.id">
                                <td class="px-4 py-2.5">{{ lead.lead_number }}</td>
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36">{{ lead.client_name }}</td>
                                <td class="px-4 py-2.5">{{ lead.county ? lead.county.name : 'N/A' }}</td>
                                <td class="px-4 py-2.5">{{ lead.open_size }}</td>
                                <td class="px-4 py-2.5">{{ lead.box_style }}</td>
                                <td class="px-4 py-2.5">{{ lead.stock }}</td>
                                <td class="px-4 py-2.5">
                                    <span :class="['px-4 py-2 rounded-full text-sm font-medium', getStatusClass(lead.status)]">
                                        {{ lead.status.charAt(0).toUpperCase() + lead.status.slice(1) }}
                                    </span>
                                </td>      
                                <td class="px-4 py-2.5">{{ lead.creator ? lead.creator.first_name : 'N/A' }}</td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown align="right" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink :href="route('leads.edit',{id:lead.id})" v-if="permissions.canEditLead">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button" @click="openDeleteModal(lead.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full" v-if="permissions.canDeleteLead">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>

        <!-- Delete Confirmation Modal -->
        <Modal :show="modalVisible" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this lead?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal">Cancel</SecondaryButton>
                    <DangerButton class="ml-3" @click="deleteLead" :disabled="form.processing">
                        Delete Lead
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>