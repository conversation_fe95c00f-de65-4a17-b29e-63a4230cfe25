<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\County;
use App\Models\Quotation;
use App\Models\Document;
use App\Models\User;
use App\Models\Lead;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use App\Traits\QueryTrait;
use App\Traits\FileUploadTrait;
use Config;
use PDF;

class OrderController extends Controller
{
    use QueryTrait, FileUploadTrait;

    public function index(Request $request)
    {
        $query = Order::with(['county', 'creator', 'quotation']);

        $county_id = $request->input('county_id');
        $agent_id = $request->input('agent_id');
        $status = $request->input('status');

        // If not admin, show only orders created by the logged-in user
        if (!auth()->user()->hasRole('Admin')) {
            $query->where('created_by', auth()->id());
        }

        if($agent_id) {
            $query->where('created_by', $agent_id);
        }

        if($county_id) {
            $query->where('county_id', $county_id);
        }

        if($status) {
            $query->where('status', $status);
        }

        $searchableFields = ['order_number', 'client_name', 'status', 'quotation.quotation_number'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);

        $permissions = [
            'canCreateOrder' => auth()->user()->can('Create Order'),
            'canEditOrder' => auth()->user()->can('Edit Order'),
            'canDeleteOrder' => auth()->user()->can('Delete Order'),
        ];

        $counties = County::all();
        $agents = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $statusOptions = [
            ['id' => 'pending', 'name' => 'Pending'],
            ['id' => 'confirmed', 'name' => 'Confirmed'],
            ['id' => 'under_production', 'name' => 'Under Production'],
            ['id' => 'shipped', 'name' => 'Shipped'],
            ['id' => 'delivered', 'name' => 'Delivered'],
        ];

        return Inertia::render('Order/List', compact('data', 'permissions', 'counties', 'agents', 'statusOptions', 'county_id', 'agent_id', 'status'));
    }

    public function create(Request $request)
    {
        $quotation = null;

        // If creating from quotation
        if ($request->has('quotation_id')) {
            $quotation = Quotation::with(['county', 'lead'])->findOrFail($request->quotation_id);
        }

        return Inertia::render('Order/Add', compact('quotation'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'quotation_id' => 'required|exists:quotations,id',
            'selected_qty_1' => 'nullable|integer|min:1',
            'selected_qty_2' => 'nullable|integer|min:1',
            'selected_qty_3' => 'nullable|integer|min:1',
            'selected_qty_4' => 'nullable|integer|min:1',
            'notes' => 'nullable|string',
            'expected_delivery' => 'nullable|date|after:today',
        ]);

        $data = $request->all();

        // Validate at least one quantity is selected
        if (empty($data['selected_qty_1']) && empty($data['selected_qty_2']) &&
            empty($data['selected_qty_3']) && empty($data['selected_qty_4'])) {
            return Redirect::back()->withErrors(['selected_qty_1' => 'At least one quantity must be selected.']);
        }

        DB::beginTransaction();
        try {
            $quotation = Quotation::findOrFail($data['quotation_id']);

            // Generate order number
            $year = date('Y');
            $lastOrder = Order::whereYear('created_at', $year)->latest()->first();
            $lastId = $lastOrder ? intval(explode('-', $lastOrder->order_number)[1]) : 0;
            $orderNumber = 'ORD-' . $year . '-' . str_pad($lastId + 1, 4, '0', STR_PAD_LEFT);

            // Copy data from quotation (only order-specific fields)
            $orderData = [
                'order_number' => $orderNumber,
                'quotation_id' => $quotation->id,
                'lead_id' => $quotation->lead_id,
                'notes' => $data['notes'] ?? null,
                'expected_delivery' => $data['expected_delivery'] ?? null,
                'status' => 'pending',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ];

            // Add selected quantities and corresponding prices
            if (!empty($data['selected_qty_1'])) {
                $orderData['selected_qty_1'] = $data['selected_qty_1'];
                $orderData['price_qty_1'] = $quotation->price_qty_1;
            }
            if (!empty($data['selected_qty_2'])) {
                $orderData['selected_qty_2'] = $data['selected_qty_2'];
                $orderData['price_qty_2'] = $quotation->price_qty_2;
            }
            if (!empty($data['selected_qty_3'])) {
                $orderData['selected_qty_3'] = $data['selected_qty_3'];
                $orderData['price_qty_3'] = $quotation->price_qty_3;
            }
            if (!empty($data['selected_qty_4'])) {
                $orderData['selected_qty_4'] = $data['selected_qty_4'];
                $orderData['price_qty_4'] = $quotation->price_qty_4;
            }

            $order = Order::create($orderData);

            // Update quotation status to accepted
            // $quotation->update(['status' => 'accepted']);

            DB::commit();
            return Redirect::to('/orders')->with('success', 'Order created successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function show(string $id)
    {
        $order = Order::with(['county', 'creator', 'quotation', 'documents'])->findOrFail($id);

        // Check if user can view this order
        if (!auth()->user()->hasRole('Admin') && $order->created_by !== auth()->id()) {
            return Redirect::to('/orders')->with('error', 'You are not authorized to view this order');
        }

        return Inertia::render('Order/Show', compact('order'));
    }

    public function edit(string $id)
    {
        $data = Order::with('documents')->findOrFail($id);

        // Check if user can edit this order
        if (!auth()->user()->hasRole('Admin') && $data->created_by !== auth()->id()) {
            return Redirect::to('/orders')->with('error', 'You are not authorized to edit this order');
        }

        return Inertia::render('Order/Edit', compact('data'));
    }

    public function update(Request $request, string $id)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,under_production,shipped,delivered',
            'tracking_number' => 'nullable|string',
            'notes' => 'nullable|string',
            'expected_delivery' => 'nullable|date',
            'actual_delivery' => 'nullable|date',
        ]);

        $order = Order::findOrFail($id);

        // Check if user can edit this order
        if (!auth()->user()->hasRole('Admin') && $order->created_by !== auth()->id()) {
            return Redirect::to('/orders')->with('error', 'You are not authorized to edit this order');
        }

        DB::beginTransaction();
        try {
            $updateData = [
                'status' => $request->status,
                'notes' => $request->notes,
                'expected_delivery' => $request->expected_delivery,
                'actual_delivery' => $request->actual_delivery,
                'updated_by' => auth()->id()
            ];

            // Add tracking number if provided
            if ($request->tracking_number) {
                $updateData['tracking_number'] = $request->tracking_number;
            }

            $order->update($updateData);

            DB::commit();
            return Redirect::to('/orders')->with('success', 'Order updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function destroy(string $id)
    {
        $order = Order::findOrFail($id);

        // Check if user can delete this order
        if (!auth()->user()->hasRole('Admin') && $order->created_by !== auth()->id()) {
            return Redirect::to('/orders')->with('error', 'You are not authorized to delete this order');
        }

        DB::beginTransaction();
        try {
            $order->delete();
            DB::commit();
            return Redirect::to('/orders')->with('success', 'Order deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/orders')->with('error', $e->getMessage());
        }
    }

    public function updateStatus(Request $request, string $id)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,under_production,shipped,delivered'
        ]);

        $order = Order::findOrFail($id);

        // Check if user can edit this order
        if (!auth()->user()->hasRole('Admin') && $order->created_by !== auth()->id()) {
            return response()->json(['error' => 'You are not authorized to edit this order'], 403);
        }

        DB::beginTransaction();
        try {
            $order->update([
                'status' => $request->status,
                'updated_by' => auth()->id()
            ]);
            DB::commit();
            return response()->json(['success' => 'Order status updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function confirmOrder(string $id)
    {
        $order = Order::with(['quotation.lead'])->findOrFail($id);

        // Check if user can confirm this order
        if (!auth()->user()->hasRole('Admin') && $order->created_by !== auth()->id()) {
            return Redirect::back()->with('error', 'You are not authorized to confirm this order');
        }

        DB::beginTransaction();
        try {
            // Update order confirmation status
            $order->update([
                'is_confirmed' => true,
                'confirmed_at' => now(),
                'status' => 'confirmed',
                'updated_by' => auth()->id()
            ]);

            // Update lead with confirmed quantities if lead exists
            if ($order->quotation && $order->quotation->lead) {
                $lead = $order->quotation->lead;
                $leadUpdateData = [];

                // Update lead quantities with confirmed order quantities
                if ($order->selected_qty_1) {
                    $leadUpdateData['confirmed_qty_1'] = $order->selected_qty_1;
                }
                if ($order->selected_qty_2) {
                    $leadUpdateData['confirmed_qty_2'] = $order->selected_qty_2;
                }
                if ($order->selected_qty_3) {
                    $leadUpdateData['confirmed_qty_3'] = $order->selected_qty_3;
                }
                if ($order->selected_qty_4) {
                    $leadUpdateData['confirmed_qty_4'] = $order->selected_qty_4;
                }

                if (!empty($leadUpdateData)) {
                    $leadUpdateData['status'] = 'order_confirmed';
                    $leadUpdateData['order_confirmed_at'] = now();
                    $lead->update($leadUpdateData);
                }
            }

            DB::commit();
            return Redirect::back()->with('success', 'Order confirmed successfully and lead updated with confirmed quantities');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function convertFromQuotation(string $quotationId)
    {
        $quotation = Quotation::with(['lead.county'])->findOrFail($quotationId);

        // Check if user can access this quotation
        if (!auth()->user()->hasRole('Admin') && $quotation->created_by !== auth()->id()) {
            return Redirect::to('/quotations')->with('error', 'You are not authorized to convert this quotation');
        }

        return Inertia::render('Order/Add', compact('quotation'));
    }

    public function generatePdf(string $id)
    {
        $order = Order::with(['county', 'creator', 'quotation'])->findOrFail($id);

        // Check if user can view this order
        if (!auth()->user()->hasRole('Admin') && $order->created_by !== auth()->id()) {
            return Redirect::to('/orders')->with('error', 'You are not authorized to view this order');
        }

        $data = [
            'order' => $order,
            'company' => [
                'name' => 'Artsy',
                'address' => '123 Business Street, City, State 12345',
                'phone' => '+****************',
                'email' => '<EMAIL>'
            ]
        ];

        $pdf = PDF::loadView('pdf.order', $data);

        return $pdf->download('order-' . $order->order_number . '.pdf');
    }
}
