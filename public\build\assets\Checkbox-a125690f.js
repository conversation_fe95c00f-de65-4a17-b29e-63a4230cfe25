import{c as u,h as d,L as n,b as l,d as i}from"./app-3b719d4c.js";const p=["value"],k={__name:"Checkbox",props:{checked:{type:[<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>],required:!0},value:{default:null}},emits:["update:checked"],setup(e,{emit:a}){const r=e,t=u({get(){return r.checked},set(o){a("update:checked",o)}});return(o,c)=>d((l(),i("input",{type:"checkbox",value:e.value,"onUpdate:modelValue":c[0]||(c[0]=s=>t.value=s),class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,p)),[[n,t.value]])}};export{k as _};
