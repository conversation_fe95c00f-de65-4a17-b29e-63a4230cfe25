import{T as w,r as a,b as l,d as i,j as m,h as p}from"./app-e21f56bc.js";function j(s,u={}){const n=w({}),d=a(""),o=a("id"),t=a("desc"),c=a({...u}),v=e=>{c.value={...c.value,...e}},h=(e={})=>{const r={...c.value,...e,search:d.value,sort_by:o.value,sort_direction:t.value},_=new URLSearchParams(window.location.search).get("page");_&&(r.page=_),n.get(route(s,r),{preserveState:!0,replace:!0})};return{form:n,search:d,sort:(e,r=!0)=>{r&&(o.value===e?t.value=t.value==="asc"?"desc":"asc":(t.value="asc",o.value=e),h())},fetchData:h,sortKey:o,sortDirection:t,updateParams:v}}const k={key:0},f={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},g=p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1),y=[g],x={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},B=p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1),S=[B],D={__name:"ArrowIcon",props:{isSorted:{type:Boolean,default:!1},direction:{type:String,default:"asc"}},setup(s){return(u,n)=>s.isSorted?(l(),i("span",k,[s.direction==="asc"?(l(),i("svg",f,y)):(l(),i("svg",x,S))])):m("",!0)}};export{D as _,j as s};
