import{T as Y,r as D,w as N,b as w,d as $,e as s,u as t,f as c,F as A,Z as E,g as a,t as u,k as z,i as B,q as I}from"./app-890b4b78.js";import{_ as j,a as L}from"./AdminLayout-bc98fd09.js";import{_ as d,a as n}from"./TextInput-fe680c40.js";import{_ as i}from"./InputLabel-795b7075.js";import{P as M}from"./PrimaryButton-b948b2ca.js";import{_ as Z}from"./TextArea-4687fa50.js";import{_ as G}from"./SearchableDropdownNew-813a9f4d.js";import{_ as H}from"./MultipleFileUpload-01fcaea9.js";import"./_plugin-vue_export-helper-c27b6911.js";const J={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},K={class:"text-2xl font-semibold leading-7 text-gray-900"},W={key:0,class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},X={class:"text-sm text-blue-800"},ee=a("strong",null,"Converting from Lead:",-1),se=["onSubmit"],te={class:"border-b border-gray-900/10 pb-12"},oe={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},le={class:"sm:col-span-4"},ae={class:"sm:col-span-4"},ne={class:"relative mt-2"},ie={class:"sm:col-span-4"},de={class:"sm:col-span-4"},re={class:"sm:col-span-4"},me={class:"sm:col-span-4"},_e={class:"sm:col-span-4"},ue={class:"sm:col-span-4"},pe={class:"sm:col-span-4"},ce=a("div",{class:"sm:col-span-12 mt-8"},[a("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quantities and Pricing")],-1),ye={class:"sm:col-span-3"},qe={class:"sm:col-span-3"},ge={class:"sm:col-span-3"},fe={class:"sm:col-span-3"},ve={class:"sm:col-span-3"},Ve={class:"sm:col-span-3"},be={class:"sm:col-span-3"},xe={class:"sm:col-span-3"},Ue={class:"sm:col-span-12"},Fe={class:"bg-gray-50 p-4 rounded-lg"},Te={class:"text-lg font-semibold text-gray-900"},Ce={class:"sm:col-span-12"},he={class:"sm:col-span-12"},ke={class:"flex mt-6 items-center justify-between"},Qe={class:"ml-auto flex items-center justify-end gap-x-6"},Se=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Ee={__name:"Add",props:{counties:{type:Array,required:!0},lead:{type:Object,default:null}},setup(_){var q,g,f,v,V,b,x,U,F,T,C,h,k,Q,S;const r=_,e=Y({client_name:((q=r.lead)==null?void 0:q.client_name)||"",county_id:((g=r.lead)==null?void 0:g.county_id)||"",lead_id:((f=r.lead)==null?void 0:f.id)||null,dimensions:((v=r.lead)==null?void 0:v.dimensions)||"",open_size:((V=r.lead)==null?void 0:V.open_size)||"",box_style:((b=r.lead)==null?void 0:b.box_style)||"",stock:((x=r.lead)==null?void 0:x.stock)||"",lamination:((U=r.lead)==null?void 0:U.lamination)||"",printing:((F=r.lead)==null?void 0:F.printing)||"",add_ons:((T=r.lead)==null?void 0:T.add_ons)||"",qty_1:((C=r.lead)==null?void 0:C.qty_1)||"",qty_2:((h=r.lead)==null?void 0:h.qty_2)||"",qty_3:((k=r.lead)==null?void 0:k.qty_3)||"",qty_4:((Q=r.lead)==null?void 0:Q.qty_4)||"",price_qty_1:"",price_qty_2:"",price_qty_3:"",price_qty_4:"",notes:((S=r.lead)==null?void 0:S.notes)||"",valid_until:"",documents:[]}),y=D(0),O=()=>{e.post(route("quotations.store"),{preserveScroll:!0,onSuccess:()=>e.reset()})},P=(m,o)=>{e.county_id=m},R=m=>{e.documents=m};N([()=>e.qty_1,()=>e.price_qty_1,()=>e.qty_2,()=>e.price_qty_2,()=>e.qty_3,()=>e.price_qty_3,()=>e.qty_4,()=>e.price_qty_4],()=>{let m=0;e.qty_1&&e.price_qty_1&&(m+=parseFloat(e.qty_1)*parseFloat(e.price_qty_1)),e.qty_2&&e.price_qty_2&&(m+=parseFloat(e.qty_2)*parseFloat(e.price_qty_2)),e.qty_3&&e.price_qty_3&&(m+=parseFloat(e.qty_3)*parseFloat(e.price_qty_3)),e.qty_4&&e.price_qty_4&&(m+=parseFloat(e.qty_4)*parseFloat(e.price_qty_4)),y.value=m});const p=new Date;return p.setDate(p.getDate()+30),e.valid_until=p.toISOString().split("T")[0],(m,o)=>(w(),$(A,null,[s(t(E),{title:"Add Quotation"}),s(j,null,{default:c(()=>[a("div",J,[a("h2",K,u(_.lead?"Convert Lead to Quotation":"Add New Quotation"),1),_.lead?(w(),$("div",W,[a("p",X,[ee,z(" "+u(_.lead.lead_number)+" - "+u(_.lead.client_name),1)])])):B("",!0),a("form",{onSubmit:I(O,["prevent"])},[a("div",te,[a("div",oe,[a("div",le,[s(i,{for:"client_name",value:"Client Name *"}),s(d,{id:"client_name",type:"text",modelValue:t(e).client_name,"onUpdate:modelValue":o[0]||(o[0]=l=>t(e).client_name=l),required:""},null,8,["modelValue"]),s(n,{message:t(e).errors.client_name},null,8,["message"])]),a("div",ae,[s(i,{for:"county_id",value:"County *"}),a("div",ne,[s(G,{options:_.counties,modelValue:t(e).county_id,"onUpdate:modelValue":o[1]||(o[1]=l=>t(e).county_id=l),onOnchange:P},null,8,["options","modelValue"])]),s(n,{message:t(e).errors.county_id},null,8,["message"])]),a("div",ie,[s(i,{for:"dimensions",value:"Dimensions *"}),s(d,{id:"dimensions",type:"text",modelValue:t(e).dimensions,"onUpdate:modelValue":o[2]||(o[2]=l=>t(e).dimensions=l),required:""},null,8,["modelValue"]),s(n,{message:t(e).errors.dimensions},null,8,["message"])]),a("div",de,[s(i,{for:"open_size",value:"Open Size *"}),s(d,{id:"open_size",type:"text",modelValue:t(e).open_size,"onUpdate:modelValue":o[3]||(o[3]=l=>t(e).open_size=l),required:""},null,8,["modelValue"]),s(n,{message:t(e).errors.open_size},null,8,["message"])]),a("div",re,[s(i,{for:"box_style",value:"Box Style *"}),s(d,{id:"box_style",type:"text",modelValue:t(e).box_style,"onUpdate:modelValue":o[4]||(o[4]=l=>t(e).box_style=l),required:""},null,8,["modelValue"]),s(n,{message:t(e).errors.box_style},null,8,["message"])]),a("div",me,[s(i,{for:"stock",value:"Stock *"}),s(d,{id:"stock",type:"text",modelValue:t(e).stock,"onUpdate:modelValue":o[5]||(o[5]=l=>t(e).stock=l),required:""},null,8,["modelValue"]),s(n,{message:t(e).errors.stock},null,8,["message"])]),a("div",_e,[s(i,{for:"lamination",value:"Lamination *"}),s(d,{id:"lamination",type:"text",modelValue:t(e).lamination,"onUpdate:modelValue":o[6]||(o[6]=l=>t(e).lamination=l),required:""},null,8,["modelValue"]),s(n,{message:t(e).errors.lamination},null,8,["message"])]),a("div",ue,[s(i,{for:"printing",value:"Printing *"}),s(d,{id:"printing",type:"text",modelValue:t(e).printing,"onUpdate:modelValue":o[7]||(o[7]=l=>t(e).printing=l),required:""},null,8,["modelValue"]),s(n,{message:t(e).errors.printing},null,8,["message"])]),a("div",pe,[s(i,{for:"add_ons",value:"Add-ons"}),s(d,{id:"add_ons",type:"text",modelValue:t(e).add_ons,"onUpdate:modelValue":o[8]||(o[8]=l=>t(e).add_ons=l)},null,8,["modelValue"]),s(n,{message:t(e).errors.add_ons},null,8,["message"])]),ce,a("div",ye,[s(i,{for:"qty_1",value:"QTY 1 *"}),s(d,{id:"qty_1",type:"number",modelValue:t(e).qty_1,"onUpdate:modelValue":o[9]||(o[9]=l=>t(e).qty_1=l),min:"1"},null,8,["modelValue"]),s(n,{message:t(e).errors.qty_1},null,8,["message"])]),a("div",qe,[s(i,{for:"price_qty_1",value:"PRICE FOR QTY 1 *"}),s(d,{id:"price_qty_1",type:"number",step:"0.01",modelValue:t(e).price_qty_1,"onUpdate:modelValue":o[10]||(o[10]=l=>t(e).price_qty_1=l),min:"0"},null,8,["modelValue"]),s(n,{message:t(e).errors.price_qty_1},null,8,["message"])]),a("div",ge,[s(i,{for:"qty_2",value:"QTY 2"}),s(d,{id:"qty_2",type:"number",modelValue:t(e).qty_2,"onUpdate:modelValue":o[11]||(o[11]=l=>t(e).qty_2=l),min:"1"},null,8,["modelValue"]),s(n,{message:t(e).errors.qty_2},null,8,["message"])]),a("div",fe,[s(i,{for:"price_qty_2",value:"PRICE FOR QTY 2"}),s(d,{id:"price_qty_2",type:"number",step:"0.01",modelValue:t(e).price_qty_2,"onUpdate:modelValue":o[12]||(o[12]=l=>t(e).price_qty_2=l),min:"0"},null,8,["modelValue"]),s(n,{message:t(e).errors.price_qty_2},null,8,["message"])]),a("div",ve,[s(i,{for:"qty_3",value:"QTY 3"}),s(d,{id:"qty_3",type:"number",modelValue:t(e).qty_3,"onUpdate:modelValue":o[13]||(o[13]=l=>t(e).qty_3=l),min:"1"},null,8,["modelValue"]),s(n,{message:t(e).errors.qty_3},null,8,["message"])]),a("div",Ve,[s(i,{for:"price_qty_3",value:"PRICE FOR QTY 3"}),s(d,{id:"price_qty_3",type:"number",step:"0.01",modelValue:t(e).price_qty_3,"onUpdate:modelValue":o[14]||(o[14]=l=>t(e).price_qty_3=l),min:"0"},null,8,["modelValue"]),s(n,{message:t(e).errors.price_qty_3},null,8,["message"])]),a("div",be,[s(i,{for:"qty_4",value:"QTY 4"}),s(d,{id:"qty_4",type:"number",modelValue:t(e).qty_4,"onUpdate:modelValue":o[15]||(o[15]=l=>t(e).qty_4=l),min:"1"},null,8,["modelValue"]),s(n,{message:t(e).errors.qty_4},null,8,["message"])]),a("div",xe,[s(i,{for:"price_qty_4",value:"PRICE FOR QTY 4"}),s(d,{id:"price_qty_4",type:"number",step:"0.01",modelValue:t(e).price_qty_4,"onUpdate:modelValue":o[16]||(o[16]=l=>t(e).price_qty_4=l),min:"0"},null,8,["modelValue"]),s(n,{message:t(e).errors.price_qty_4},null,8,["message"])]),a("div",Ue,[a("div",Fe,[a("p",Te," Estimated Total: $"+u(y.value.toFixed(2)),1)])]),a("div",Ce,[s(i,{for:"notes",value:"Notes"}),s(Z,{id:"notes",modelValue:t(e).notes,"onUpdate:modelValue":o[17]||(o[17]=l=>t(e).notes=l),rows:"4"},null,8,["modelValue"]),s(n,{message:t(e).errors.notes},null,8,["message"])]),a("div",he,[s(i,{for:"documents",value:"Documents"}),s(H,{onChange:R}),s(n,{message:t(e).errors.documents},null,8,["message"])])])]),a("div",ke,[a("div",Qe,[s(L,{href:m.route("quotations.index")},{svg:c(()=>[Se]),_:1},8,["href"]),s(M,{disabled:t(e).processing},{default:c(()=>[z(" Save ")]),_:1},8,["disabled"])])])],40,se)])]),_:1})],64))}};export{Ee as default};
